(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4944],{888:(e,t,r)=>{Promise.resolve().then(r.bind(r,13570))},1571:(e,t,r)=>{"use strict";r.d(t,{$:()=>u.A,f:()=>n.A});var u=r(78046),n=r(74500)},8652:(e,t,r)=>{"use strict";r.d(t,{AQ:()=>a.A,RY:()=>l.A,cu:()=>n.A,fA:()=>i.A,r9:()=>c.A,sR:()=>o.A,tl:()=>u.A});var u=r(94648),n=r(14615),c=r(5500),a=r(92975),i=r(69994),l=r(94038),o=r(39883)},13570:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var u=r(95155),n=r(22261),c=r(99323),a=r(90433);function i(e){let{children:t}=e;return(0,u.jsx)("div",{className:"flex h-screen bg-[#040716] w-full",children:(0,u.jsx)(n.G,{children:(0,u.jsx)(c.i9,{children:(0,u.jsx)(a.A,{children:t})})})})}},18685:(e,t,r)=>{"use strict";r.d(t,{AQ:()=>a.A,RY:()=>l.A,cu:()=>n.A,fA:()=>i.A,r9:()=>c.A,sR:()=>o.A,tl:()=>u.A});var u=r(51297),n=r(48612),c=r(10187),a=r(55616),i=r(34853),l=r(11595),o=r(22670)},21884:(e,t,r)=>{"use strict";r.d(t,{C1:()=>u.A,KS:()=>c.A,Pi:()=>n.A,fK:()=>i.A,qh:()=>a.A});var u=r(6865),n=r(55628),c=r(67695),a=r(52589),i=r(74500)},22261:(e,t,r)=>{"use strict";r.d(t,{G:()=>a,c:()=>i});var u=r(95155),n=r(12115);let c=(0,n.createContext)(void 0);function a(e){let{children:t}=e,[r,a]=(0,n.useState)(!0),[i,l]=(0,n.useState)(!1),[o,s]=(0,n.useState)(!1);return(0,u.jsx)(c.Provider,{value:{isCollapsed:r,isHovered:i,isHoverDisabled:o,toggleSidebar:()=>a(!r),collapseSidebar:()=>a(!0),expandSidebar:()=>a(!1),setHovered:e=>{o||l(e)},setHoverDisabled:e=>{s(e),e&&l(!1)}},children:t})}function i(){let e=(0,n.useContext)(c);if(void 0===e)throw Error("useSidebar must be used within a SidebarProvider");return e}},27016:(e,t,r)=>{"use strict";r.d(t,{$p:()=>s.A,AQ:()=>o.A,BF:()=>l.A,D3:()=>a.A,Rz:()=>u.A,Vy:()=>i.A,XF:()=>c.A,tK:()=>n.A});var u=r(55596),n=r(69598),c=r(58828),a=r(63418),i=r(37186),l=r(40710),o=r(92975),s=r(78046)},31430:(e,t,r)=>{"use strict";r.d(t,{AQ:()=>a.A,DP:()=>c.A,RY:()=>i.A,cu:()=>u.A,r9:()=>n.A,sR:()=>l.A});var u=r(14615),n=r(5500),c=r(5246),a=r(92975),i=r(94038),l=r(39883)},89732:(e,t,r)=>{"use strict";r.d(t,{AQ:()=>a.A,RY:()=>l.A,cu:()=>n.A,fA:()=>i.A,fK:()=>s.A,r9:()=>c.A,sR:()=>o.A,tl:()=>u.A});var u=r(94648),n=r(14615),c=r(5500),a=r(92975),i=r(69994),l=r(94038),o=r(39883),s=r(74500)},99323:(e,t,r)=>{"use strict";r.d(t,{bu:()=>l,i9:()=>i});var u=r(95155),n=r(12115),c=r(35695);let a=(0,n.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,n.useState)(!1),[l,o]=(0,n.useState)(null),[s,A]=(0,n.useState)([]),[d,f]=(0,n.useState)(new Set),[v,I]=(0,n.useState)(!1),T=(0,c.usePathname)(),h=(0,c.useRouter)(),D=(0,n.useRef)(null),m=(0,n.useRef)([]),S=(0,n.useRef)(null),g=(0,n.useRef)(0),C=(0,n.useRef)({}),b=(0,n.useRef)({});(0,n.useEffect)(()=>{I(!0)},[]);let P=(0,n.useCallback)(e=>{},[v]);(0,n.useEffect)(()=>{T&&!s.includes(T)&&(A(e=>[...e,T]),f(e=>new Set([...e,T])))},[T,s]),(0,n.useEffect)(()=>{P("\uD83D\uDD0D [OPTIMISTIC NAV] Route check: target=".concat(l,", current=").concat(T,", navigationId=").concat(S.current)),l&&S.current&&T===l&&(P("✅ [OPTIMISTIC NAV] Navigation completed: ".concat(l," -> ").concat(T)),D.current&&(clearTimeout(D.current),D.current=null),i(!1),o(null),S.current=null,m.current=m.current.filter(e=>e.route!==l))},[T,l,P]),(0,n.useEffect)(()=>{r&&l&&T===l&&(P("\uD83D\uDE80 [OPTIMISTIC NAV] Immediate route match detected, clearing navigation state"),i(!1),o(null),D.current&&(clearTimeout(D.current),D.current=null))},[T,l,r,P]);let N=(0,n.useCallback)(e=>d.has(e),[d]),R=(0,n.useCallback)(()=>{if(0===m.current.length)return;let e=m.current[m.current.length-1];m.current=[e];let{route:t,id:r}=e;P("\uD83D\uDE80 [OPTIMISTIC NAV] Processing navigation to: ".concat(t," (id: ").concat(r,")")),D.current&&(clearTimeout(D.current),D.current=null),S.current=r;let u=N(t);u&&(P("⚡ [OPTIMISTIC NAV] Using cached navigation for: ".concat(t)),setTimeout(()=>{S.current===r&&i(!1)},100));try{h.push(t)}catch(e){P("❌ [OPTIMISTIC NAV] Router.push failed for: ".concat(t,", using fallback")),window.location.href=t;return}D.current=setTimeout(()=>{if(P("⚠️ [OPTIMISTIC NAV] Timeout reached for: ".concat(t," (id: ").concat(r,"), current path: ").concat(T)),S.current===r){P("\uD83D\uDD04 [OPTIMISTIC NAV] Attempting fallback navigation to: ".concat(t));try{window.location.href=t}catch(e){P("❌ [OPTIMISTIC NAV] Fallback navigation failed: ".concat(e))}i(!1),o(null),S.current=null}D.current=null},u?800:3e3)},[h,T,N,P]),E=(0,n.useCallback)(e=>{if(T===e||!v)return;let t=Date.now();if(t-g.current<100&&l===e)return void P("\uD83D\uDD04 [OPTIMISTIC NAV] Debouncing duplicate navigation to: ".concat(e));if(g.current=t,C.current[e]||(C.current[e]=0),C.current[e]++,b.current[e]&&clearTimeout(b.current[e]),b.current[e]=setTimeout(()=>{C.current[e]=0},2e3),C.current[e]>=3){P("\uD83D\uDEA8 [OPTIMISTIC NAV] Force navigation escape hatch for: ".concat(e)),C.current[e]=0,window.location.href=e;return}D.current&&(clearTimeout(D.current),D.current=null),i(!0),o(e);let r="nav_".concat(t,"_").concat(Math.random().toString(36).substr(2,9));m.current=[{route:e,timestamp:t,id:r}],R()},[T,l,R,P,v]),O=(0,n.useCallback)(()=>{D.current&&(clearTimeout(D.current),D.current=null),i(!1),o(null),S.current=null,m.current=[]},[]);return(0,n.useEffect)(()=>{if(!v)return;let e=()=>{!document.hidden&&r&&(P("\uD83D\uDC41️ [OPTIMISTIC NAV] Document visible, checking if navigation should clear"),setTimeout(()=>{l&&T===l&&(P("\uD83D\uDD27 [OPTIMISTIC NAV] Force clearing navigation state"),i(!1),o(null),D.current&&(clearTimeout(D.current),D.current=null))},100))};return document.addEventListener("visibilitychange",e),()=>document.removeEventListener("visibilitychange",e)},[r,l,T,P,v]),(0,n.useEffect)(()=>()=>{D.current&&clearTimeout(D.current)},[]),(0,u.jsx)(a.Provider,{value:{isNavigating:r,targetRoute:l,navigateOptimistically:E,clearNavigation:O,isPageCached:N,navigationHistory:s},children:t})}function l(){return(0,n.useContext)(a)||null}}},e=>{var t=t=>e(e.s=t);e.O(0,[8888,1459,5738,9968,6060,4755,563,2662,8669,4703,3269,9173,9219,4288,7706,7544,1142,2993,1561,9248,6761,7358],()=>t(888)),_N_E=e.O()}]);