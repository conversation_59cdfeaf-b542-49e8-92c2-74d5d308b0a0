{"version": 3, "file": "edge-chunks/918.js", "mappings": "+FACA,8CACA,0BAA6C,UAC7C,EACA,qCAA6C,CAAE,SAAa,EAAC,IAC7D,IAAoD,EAAQ,IAA6B,EACzF,WAD2D,MAC3D,UASA,QAEA,OADA,qCAAmD,EAAM,GACzD,KAQA,SAEA,OADA,sCAAoD,EAAM,GAC1D,KAQA,QAEA,OADA,qCAAmD,EAAM,GACzD,KAQA,SAEA,OADA,sCAAoD,EAAM,GAC1D,KAQA,QAEA,OADA,qCAAmD,EAAM,GACzD,KAQA,SAEA,OADA,sCAAoD,EAAM,GAC1D,KAQA,UAEA,OADA,uCAAqD,EAAQ,GAC7D,KAQA,eAEA,OADA,2CAAyD,EAAE,aAAoB,GAC/E,KAQA,eAEA,OADA,2CAAyD,EAAE,aAAoB,GAC/E,IACA,CAOA,WAEA,OADA,wCAAsD,EAAQ,GAC9D,KAQA,gBAEA,OADA,4CAA0D,EAAE,aAAoB,GAChF,IACA,CAOA,gBAEA,OADA,4CAA0D,EAAE,aAAoB,GAChF,KAcA,QAEA,OADA,qCAAmD,EAAM,GACzD,KAQA,QACA,6BACA,OAGA,4CACA,IAA2B,EAAE,GAE7B,GAA0B,EAAE,GAE5B,UAEA,OADA,sCAAoD,EAAc,IAClE,KASA,cAcA,MAbA,mBAGA,qCAAuD,EAAM,GAE7D,iBAEA,oCAAsD,EAAE,aAAiB,GAIzE,qCAAuD,kBAAsB,GAE7E,KASA,iBAaA,MAZA,mBAEA,qCAAuD,EAAM,GAE7D,iBAEA,oCAAsD,EAAE,aAAiB,GAIzE,qCAAuD,kBAAsB,GAE7E,KASA,aAEA,OADA,qCAAmD,EAAM,GACzD,KAUA,cAEA,OADA,sCAAoD,EAAM,GAC1D,KASA,aAEA,OADA,qCAAmD,EAAM,GACzD,KAUA,cAEA,OADA,sCAAoD,EAAM,GAC1D,KAUA,mBAEA,OADA,sCAAoD,EAAM,GAC1D,IACA,CAQA,cASA,MARA,mBAEA,qCAAuD,EAAM,GAI7D,oCAAsD,EAAE,aAAiB,GAEzE,KAYA,uBAAgC,UAAe,EAAI,EACnD,QACA,aACA,OAEA,aACA,OAEA,iBACA,QAEA,wBAA2D,EAAO,GAElE,OADA,kCAAgD,EAAS,KAAK,EAAW,GAAG,EAAM,GAClF,KASA,SAIA,OAHA,oCACA,qCAAuD,EAAM,EAC7D,CAAS,EACT,KAeA,WAEA,OADA,sCAAoD,EAAS,GAAG,EAAM,GACtE,KAiBA,MAAkB,oCAAgD,EAAI,EACtE,WAAyC,EAAgB,UAEzD,OADA,mCAA8C,EAAQ,IACtD,KAeA,cAEA,OADA,kCAAgD,EAAS,GAAG,EAAM,GAClE,KAEA,CACA,SAAe,wBC1Xf,8CACA,0BAA6C,UAC7C,EACA,qCAA6C,CAAE,SAAa,EAAC,IAE7D,IAAqC,EAAQ,IAAsB,GACnE,IAAyC,EAAQ,IAAkB,EACnE,SACA,EAFgD,UAEhD,GACA,2BACA,qBACA,eACA,uBACA,qBACA,iBACA,6CACA,qBACA,mCACA,QACA,mBAEA,0BACA,qBAGA,gBAEA,CAOA,eAEA,OADA,2BACA,KAKA,eAGA,OAFA,6BAAuC,eACvC,kBACA,KAEA,UAEA,uBAGA,qCACA,2CAGA,6CAEA,2CACA,kDAKA,MADA,eACA,qBACA,mBACA,qBACA,+BACA,kBACA,CAAS,iBACT,UACA,WACA,OACA,OACA,WACA,eACA,SACA,yBACA,oBACA,UAIA,EADA,kCAGA,qBACA,gEACA,EAGA,cAEA,CACA,sFACA,8DACA,mBACA,mBAIA,4DACA,YACA,GAEA,gBACA,2BAAwD,UAAa,wDACrE,UACA,+DACA,EACA,OACA,OACA,MACA,oBAGA,EADA,aACA,KAGA,KAGA,KACA,CACA,qBACA,IACA,gBAEA,mCACA,KACA,OACA,MACA,OAEA,CACA,SAEA,wBACA,MACA,gBAGA,GACA,SACA,CAEA,CAMA,GALA,0FACA,OACA,MACA,QAEA,2BACA,sBAEA,CAQA,MAPA,CACA,QACA,OACA,QACA,SACA,YACA,CAEA,CAAS,EAkBT,OAjBA,yBACA,eACA,UACA,OACA,OACA,WAAoC,+CAA6H,UAAI,mBAA2E,EAChP,WAAoC,sCAAoH,EACxJ,QACA,QAAiC,qCAAmH,EAC/H,CACrB,UACA,WACA,SACA,aACA,CAAiB,EACJ,EAEb,WACA,CAOA,UAEA,YAwBA,gBACA,YAEA,CACA,SAAe,wBC1Nf,8CACA,0BAA6C,UAC7C,EACA,qCAA6C,CAAE,SAAa,EAAC,EAC7D,cAAsB,CAAG,kBAAwB,CAAG,2BAAiC,CAAG,wBAA8B,CAAG,uBAA6B,CAAG,iBAAuB,QAEhL,QAA0C,EAAQ,IAAmB,GACrE,UADiD,OAC1B,WACvB,QAAgD,EAAQ,IAAyB,GACjF,UADuD,aAC1B,WAC7B,QAAiD,EAAQ,EAA0B,GACnF,YADwD,YAC1B,WAC9B,QAAoD,EAAQ,IAA6B,EACzF,WAD2D,iBAC1B,WACjC,QAA2C,EAAQ,GAAoB,GACvE,WADkD,OAC1B,WACxB,QAAyC,EAAQ,IAAkB,GACnE,UADgD,MAC1B,WACtB,SAAe,EACf,0BACA,gCACA,iCACA,oCACA,2BACA,uCCxBA,qCAA6C,CAAE,SAAa,EAAC,EAC7D,OAAe,QACf,SAAe,sCKFf,qBJDO,UACP,MAUA,OARA,EADA,IAGA,0BACA,QAA8B,sCAA8B,QAAS,UAAgB,YAGrF,OAEA,eACA,CCZO,uBACP,oCACA,SACA,YACA,cACA,CACA,CACO,kBACP,eACA,8EACA,CACA,CACO,kBACP,eACA,uEACA,CACA,CACO,kBACP,eACA,4EACA,CACA,EAGA,YACA,YACA,gCACA,gCACA,wBACA,gCACA,gCACA,4BACA,4BACA,sBACA,sBACA,sBACA,sBACA,sBACA,sBACA,qBACA,CAAC,UAAwC,CC7BlC,SACP,uBAAuB,IAAY,wBAAwB,EAAc,KAAQ,EAAI,EACrF,GADyE,CACzE,OACA,eACA,cACA,WAAqB,EAAY,EACjC,CAKA,OANiC,CAMjC,GACA,qCAA+C,EAAM,EAOrD,aAAqC,MACrC,EA/B6B,QAgC7B,OAhC6B,EAgC7B,KAhC6B,EAgC7B,OAhC6B,EAgC7B,OAhC6B,EAgC7B,YACA,IACA,IASA,EA6CA,EAtDA,SAAwB,mBAAsC,EAC9D,KACA,QAAsB,GAAS,CAC/B,IACA,gBAEA,cACA,kBAGA,GACA,mEACA,6CACA,0BAGA,6CACA,KAEA,oBAEA,+BACA,KAEA,oDAGA,KAIA,qCACA,sBAGA,0BAAqD,SAAS,GAAG,EAAa,GAC9E,iBAKA,oDAAyE,qBACzE,MACA,CAAiB,YACjB,UAA8B,EAAmB,EACjD,CAAiB,EACjB,iCACA,iBACA,UAA8B,EAAmB,GAEjD,SACA,KAHiD,CAGjD,IAA8B,EAAkB,GAEhD,uEAAuI,aAkBvI,OAAyB,KAhBzB,uBACA,eAEA,+BACA,eAEA,wBACA,EAEA,0BACA,mBAIA,eAEyB,WACzB,CACA,SACA,OAAyB,kBACzB,CACA,CAAS,CA5GT,kCACA,cAAoC,IAAM,aAA+B,SAAY,MACrF,cAAmC,IAAM,cAAmC,SAAY,MACxF,kBAJA,EAIgC,mBAJJ,CAA5B,EAIgC,mBAJJ,sBAA+D,MAAiB,EAI5E,UAChC,8BACA,CAAK,CAwGL,CACA,CC/GA,IACA,kBACA,wBACA,yBACA,4BACA,mBACA,iBACA,CAAE,EAAE,GAAK,ECGT,EATA,WASe,EATf,WAS4B,EAAC,CANT,EAAQ,IAAI,EAIhC,WAJ2B,KAI3B,CERO,GAA0B,+BAAgC,MAAQ,GAMzE,MANwE,CAAC,EAMzE,GACA,+BACA,mBACA,yBACA,sBACA,CAAC,UAAsC,EAEvC,YACA,kBACA,oBACA,kBACA,oBACA,mBACA,CAAC,CAAE,IAAmB,EAAc,GAAK,EAEzC,YACA,CAHiB,CAGjB,GAHoC,EAGpC,aACA,oBACA,kBACA,oBACA,oBACA,6BACA,CAAC,UAAwC,EAIxC,UAAgC,EADjC,sBAGA,YACA,0BACA,cACA,oBACA,iBACA,CAAC,WCtCc,SACf,cACA,oBACA,CACA,mBACA,4BACA,yBAEA,mBACA,iBAEA,IAA0B,CAC1B,CACA,iBACA,sBACA,kBACA,mCACA,CACA,wBACA,oBACA,gBACA,uBACA,2BACA,KACA,+BAGA,OAFA,KAEA,CAAiB,iCADjB,6CACiB,CACjB,CACA,CCnBe,QACf,iBACA,gBACA,iBACA,kBACA,aACA,gBACA,gBACA,CACA,QACA,aACA,wBACA,CAEA,kBACA,yBACA,2BACA,wBACA,eACA,CAAS,8BACT,CACA,EC3BA,YACA,oBACA,cACA,cACA,wBACA,kBACA,kBACA,cACA,cACA,wBACA,cACA,wBACA,cACA,gBACA,gBACA,oBACA,YACA,oBACA,cACA,cACA,wBACA,4BACA,kBACA,oBACA,uBACA,CAAC,UAAsC,EAahC,eAAwD,IAC/D,MACA,iCACA,qCACA,gBACA,GACK,EAAI,CACT,EAeO,cACP,4BACA,wBACA,cACA,kBACA,OAEA,IACA,EAcO,UAEP,qBAEA,WADA,qBAIA,UACA,YACA,WACA,eACA,cACA,YACA,YACA,YACA,eACA,WACA,WACA,aACA,aACA,WACA,kBACA,WACA,MAD6C,EAC7C,QACA,YACA,iBACA,iBACA,iBACA,aACA,eACA,YACA,YACA,mBACA,cACA,eACA,iBAEA,QADA,WAIA,CACA,EACA,KACA,EAEO,MACP,UACA,QACA,QACA,SACA,QACA,SACA,QACA,CACA,EACO,MACP,uBACA,oBACA,oBACA,QAEA,CACA,QACA,EACO,MACP,sBACA,IACA,oBACA,CACA,SACA,iCAA6C,EAAM,EAEnD,CAEA,QACA,EAWO,UACP,sBACA,SAEA,iBACA,OAGA,KAAwB,IAFxB,MAEwB,EAAsB,IAAtB,EAAsB,CAE9C,IADA,EACA,eAEA,IACA,uBACA,CACA,SAEA,oBAEA,uBACA,CACA,QACA,EAQO,KACP,mBACA,mBAEA,EAEO,MACP,QAGA,MADA,IADA,4BACA,+DACA,kBACA,CCtNe,SASf,oBAA4C,GJNrC,GIMgE,EACvE,GADwD,CACxD,WACA,aACA,eACA,eACA,aACA,yBACA,YACA,uBACA,iBACA,kBACA,CACA,UACA,eACA,uBACA,YACA,mBACA,uBACA,aACA,WACA,CACA,OACA,+BAGA,oBACA,aACA,0BACA,yBACA,iBACA,qBACA,aACA,gCACA,CAAS,EACT,CACA,iBACA,2CAAqD,iBACrD,CACA,aACA,MAKA,OAJA,sBACA,iDAEA,2BAA6B,aAAkB,EAC/C,KAEA,eACA,oBAGA,wCACA,qDAOA,iCAA0C,CAN1C,IACA,uBACA,sBACA,oBACA,qBACA,GAEA,kCACA,yBAAsC,CACtC,CAAS,eACT,CACA,aACA,eACA,4CAAmD,aAAkB,CACrE,CACA,UACA,uBACA,qBACA,CACA,kBACA,eAGA,kCAA2C,CAC3C,CACA,iBACA,gCACA,wBACA,CACA,sBAAoB,aAAmB,EACvC,cACA,wBACA,yBACA,CACA,gBACA,sDACA,CACA,EC9FA,YACA,cACA,cACA,eACA,CAAC,UAA0E,CAC5D,SAQf,iBACA,eACA,cACA,qBACA,kBACA,aACA,aAA6B,CAC7B,cAA8B,CAC9B,aAA6B,EAE7B,kCACA,uBACA,oBACA,EACA,2BAAyC,KACzC,WAAoB,sBAA0B,YAC9C,qCACA,yCACA,8BACA,uCACA,CAAa,EACb,qBACA,GACA,CAAS,EACT,0BAAwC,KACxC,WAAoB,sBAA0B,YAC9C,0BACA,2BAGA,wCACA,IAEA,CAAS,EACT,sBACA,kCACA,aACA,MACA,mBACA,cACA,CAAa,CACb,CAAS,EACT,uBACA,kCACA,cACA,MACA,mBACA,eACA,CAAa,CACb,CAAS,EACT,iBACA,kCAAgD,aAAe,CAC/D,CAAS,CACT,CAWA,0BACA,wBACA,yBACA,KACA,KAwBA,OAvBA,mBACA,MACA,QAEA,CAAS,EACT,mBACA,WACA,MACA,+BACA,2BACA,2CACA,0CACA,aACA,SAEA,YACA,QAEA,MAEA,MAEA,CAAS,EACT,uBAAsC,WAAe,KACrD,CAWA,yBACA,IAAgB,kBAAgB,CAChC,mCACA,oCACA,EA6BA,OA5BA,GACA,WAEA,GACA,WAEA,mBACA,MACA,0BAEA,GADA,uBACA,YACA,kCACA,2CACA,kBACA,CACA,QACA,CAAS,EACT,mBACA,WACA,MACA,OACA,+BACA,2CACA,OACA,SACA,cACA,YACS,EACT,CACA,CAEA,gBACA,sDACA,CAwBA,yBAEA,kCADA,qBACA,eACA,WAYA,MAXA,YACA,qBACA,yBACA,iBACA,sBACA,IAIA,OAEA,CACA,CAAS,GAAI,CACb,CAEA,oBACA,oCACA,CAEA,UACA,oBACA,CAEA,WACA,qBACA,CAEA,UACA,oBACA,CAEA,qBACA,2DACA,CACA,ECvNA,YACA,UACA,kBACA,kBACA,iBACA,CAAC,UAAwF,EAEzF,YACA,wBACA,sBACA,sCACA,iBACA,CAAC,UAAsD,EAEvD,YACA,0BACA,wBACA,kBACA,+BACA,CAAC,YAA8D,CAOhD,SACf,YAEA,KAAsB,UAAY,IAClC,aACA,cACA,cACA,iBACA,WAAqB,EAAc,OACnC,eADmC,CACnC,GACA,mBACA,0CACA,kCACA,WAAyB,eAAyB,CAClD,UAAwB,OAAS,CACjC,UACA,CAAS,WACT,iCACA,kBAA4B,EAAI,KAAO,EAAc,+BACrD,qBAA+B,EAAK,+DACpC,gCACA,WAAyB,EAAc,OACvC,eADuC,CACvC,SACA,qCACA,mBACS,EACT,mBACA,yBACA,mCAAgD,YAAY,EAAE,gBAAgB,GAC9E,WAAyB,EAAc,OACvC,eADuC,IACvC,MACA,CAAS,EACT,kBACA,sCAGA,mCAAgD,WAAW,KAC3D,WAAyB,EAAc,QACvC,cADuC,EACvC,mBACA,CAAS,EACT,qCACA,oBAGA,qCAAkD,WAAW,yBAC7D,WAAyB,EAAc,QACvC,cADuC,EACvC,mBACA,CAAS,EACT,SAAiB,EAAc,QAAU,IAAV,KAC/B,wCACA,CAAS,EACT,kBAA4B,EAAgB,MAC5C,QAD4C,iBAC5C,CACY,EAAe,uCAC3B,2CACA,CAEA,4BACA,QAIA,GAHA,2BACA,sBAEA,gBACA,2GAEA,EACA,IAAoB,kBAAU,uBAAyC,EAAI,YAC3E,uDACA,+CACA,SACA,GACA,YACA,WACA,mGACA,SACA,CACA,+BACA,8CAEA,6CAAmD,EAAQ,KAC3D,mBACA,gBACA,cACA,qCAAwC,EAAkB,IAC1D,MAEA,GADA,sBACA,YACA,0BACA,MACA,CACA,CACA,qCACA,wCACA,KACA,YAAoC,IAAiB,KACrD,WACA,CAAgC,cAAU,4BAA8B,EAAI,EAC5E,UACA,MACA,aACA,cACA,aACA,aACA,qCAAmF,KAA4B,QAA6B,OAE5I,CACA,mBACA,WAAyC,EAAc,QACvD,cADuD,aACvD,4EACA,MACA,CACA,CACA,iCACA,oBACA,MACA,CACA,CAAa,EACb,oBACA,WAA6B,EAAc,QAC3C,cAD2C,aAC3C,6DAEA,CAAa,EACb,uBACA,wBAEA,CAAa,CACb,CACA,YAEA,gBACA,2BAEA,kBAAkC,EAClC,wBACA,gBACA,cACA,SACA,CAAS,yBACT,CACA,kBAA2B,EAC3B,wBACA,gBACA,eACA,CAAS,GACT,CACA,UACA,sBACA,CAUA,iBAA8B,EAC9B,QACA,yCAsCA,uBACA,UACA,kDACA,oHACA,QAEA,4BACA,kCACA,uCACA,CAAa,CA/Cb,EACA,UAAoB,aAAmC,EAIvD,GACA,cACA,SACA,cANA,6BACA,UAA4B,6BAA6B,EACzD,GAKA,gDACA,iCACA,CAAiB,CACjB,qBACA,UACA,CACA,oBACA,QACA,UACA,qBACyB,CACzB,CACiB,CACjB,EACA,IACA,mGAEA,OADA,2CACA,iBACA,CACA,SACA,yBACA,kBAGA,aAEA,CACA,CAaA,CACA,qBACA,8BACA,CAUA,4BACA,WAAqB,EAAc,QACnC,WACA,GAFmC,CAEnC,+BAAgD,WAAW,GAC3D,cAA0B,EAAc,8BACxC,EAEA,OADA,wBACA,gBACA,UAAkC,EAAI,KAAO,EAAc,QAAU,IAAV,EAE3D,kBACA,IACA,OACA,CAAa,EACb,uBACA,IACA,cACA,CAAa,EACb,qBACA,UACA,CAAa,EACb,SACA,iBACA,iBAA0C,CAE1C,CAAS,CACT,CAMA,WACA,wCACA,uDACA,uBACA,CAEA,+BACA,0BACA,8BACA,0DAAoF,KAAc,gBAA2B,GAE7H,OADA,gBACA,CACA,CAEA,0BACA,oBACA,uBAAoC,EAAM,QAAQ,WAAW,iEAE7D,UAA4B,EAAI,YAQhC,OAPA,gBACA,UAGA,iBACA,yBAEA,CACA,CASA,kBACA,QACA,CAEA,aACA,qBACA,CAEA,WACA,yBAGA,gBACA,QACA,4BACA,OAAgB,0BAA4B,EAE5C,MADA,MAD4D,CAC5D,GACA,mCACA,OAEA,6BACA,SACA,mFAEA,yCACA,uDACA,UACA,iDACA,8EACA,CAAa,0BAGb,yCACA,gBACA,4DAiBA,sCAhBA,aACA,WACA,oCACA,UACA,wCACA,UACA,yCACA,qDACA,CACA,CACA,8FACA,eACA,mEACA,CAKA,CAAa,UACb,kCACA,aACA,CAA4B,qDAAgD,EAU5E,gCAAmE,CATnE,CACA,SACA,QACA,mBACA,YACA,MAA+B,CAC/B,MAA+B,CAC/B,QACA,GACmE,2BACnE,CACA,eACA,CAAa,CAEb,CAEA,YACA,oBAA8B,EAAc,MAC5C,CAEA,YACA,GAJ4C,IAI5C,aAA8B,EAAc,OAG5C,aACA,EAJ4C,KAI5C,aAA8B,EAAc,QAG5C,aACA,CAJ4C,MAI5C,aAA8B,EAAc,QAG5C,cAH4C,EAG5C,GACA,oBAA6B,EAAI,EAGjC,WACA,4BACA,GACA,OACA,SACA,UACA,EAOA,OANA,iBACA,yBAGA,qBAEA,KAGA,UACA,4BAMA,OALA,6CACA,MACA,6DACA,sBACA,CAAS,EACT,KAGA,oBACA,iDACA,SAEA,eACA,eACA,SAGA,QACA,CAEA,wBACA,mCACA,2BACA,cAEA,CAMA,YACA,SAAiB,EAAc,QAAU,GACzC,CAD+B,SAO/B,GACA,SAAiB,EAAc,QAAU,IAAV,EAAU,GACzC,CAMA,WACA,kDACA,CAEA,wBACA,oBAGA,wCACA,WAAqB,EAAc,QACnC,wBACA,CAEA,sBACA,OACA,MAAmB,CACnB,MACA,EAOA,MANA,wCACA,OAA0B,EAA8B,qBAExD,wCACA,OAA0B,EAA8B,yBAExD,CACA,CACA,CCpfA,IAAM,EAAI,OACV,UADU;AAEV;AACA;AACA,sCAAsC,oBAAoB;AAC1D;AACA,GAAG,EAAE,QACU,EAmBf,iBACA,KACA,4BACA,iBACA,iBACA,iBACA,qBACA,aAAuB,EACvB,aADsC,EAEtC,aPrCO,EOqCgB,EACvB,aADsC,WACtC,MACA,2BACA,8BACA,uBAAiC,EACjC,WACA,MAFqC,KAErC,CAAsB,EACtB,eACA,EAF0B,EAE1B,eACA,oBAA8B,EAC9B,QADwC,iBACxC,EACA,QACA,SACA,SACA,YAEA,sBAMA,uBACA,MAUA,OARA,EADA,IAGA,0BACA,QAAsC,sCAA8B,QAAS,UAAgB,YAG7F,OAEA,eACA,EACA,iBAA2B,EAAS,GAAG,EAAU,UAAW,EAC5D,kBAA4B,EAAe,GAC3C,UAD2C,IAC3C,eACA,2BAGA,oBAEA,2BACA,uBACA,4BACA,4CAAyD,2BACzD,4BACA,yBACA,2BACA,uBACA,8DACA,sCACA,0CAAwD,eAAkB,wBAA0B,GAEpG,wCACA,iDACA,wDAuBA,GAtBA,IACA,wBACA,eAEA,0DACA,mBACA,GACA,4BAEA,sCACA,SACA,OACA,qBAEA,sCACA,SACA,6CACA,wBAAkC,EAAK,UACvC,kBACA,cACA,CAAS,wBACT,sDACA,yBACA,8CACA,0CAEA,2CACA,0CAEA,qDACA,CAIA,UACA,eAMA,GAHA,gBACA,gBAA6B,EAAS,CAEtC,gBAEA,8DAEA,iDAGA,wDACA,oBACA,CAAiB,EAEjB,uBACA,MACA,CACA,2CACA,WACA,cACA,CAAa,CACJ,EACT,CAKA,cACA,wDAAiE,cAAiB,IPnK3E,COmKgF,GAAG,IAAE,EAC5F,CAOA,gBACA,YACA,+BACA,CADiD,CAEjD,gCAGA,kBAEA,eAEA,wDACA,4BACA,uCAEA,CAIA,cACA,qBAMA,uBACA,4BAKA,OAJA,+DACA,0BACA,kBAEA,CACA,CAIA,0BACA,+DAGA,OAFA,iBACA,kBACA,CACA,CAMA,WACA,kBACA,CAIA,kBACA,wCACA,KAAiB,EAAa,WAC9B,OAAuB,EAAgB,gBACtB,EAAa,KAC9B,MAD8B,CACP,EAAgB,UACtB,EAAa,EADS,KACT,CAC9B,GAD8B,IACP,EAAgB,gBAEvC,OAAuB,EAAgB,OAEvC,CAIA,MANuC,OAMvC,CACA,gCAA0C,EAAgB,KAE1D,aAA8B,UAAY,EAC1C,kBAA0C,EAAM,EAChD,0CACA,KAMA,QANA,EACA,UAA6B,EAAe,YAAa,CAAb,CAAmB,UAE/D,OADA,sBACA,CACA,CAIA,CAMA,QACA,UAAgB,2BAA6B,EAC7C,OACA,kBACA,KACA,+BACA,CAAa,CACb,EACA,mBAA4B,GAAO,EAAE,GAAO,GAAG,EAAI,MACnD,mBACA,IAGA,uBAEA,CAUA,sBACA,SACA,4CACA,sBACA,2BACA,wBACA,0BACA,GACA,qBACA,eACA,oDACqB,EACrB,6BACA,QAAkC,EAAc,cAChD,cACA,CAAqB,CAErB,CAAa,EAEb,CAIA,sBACA,MACA,mCACA,uCAGA,6BACA,8BACA,iFACA,kCACA,6BAA2E,IAAe,oBAC1F,MACA,CACA,yCACA,WACA,gBACA,kBACA,UAAuB,CACvB,6BACS,EACT,+BACA,oBACA,CACA,eACA,wBACA,CAIA,kBACA,+CACA,gCACA,mBAEA,CAMA,WACA,iBAOA,OANA,aACA,WAGA,WAEA,mBACA,CAMA,mBACA,0EACA,IACA,iDAA8D,EAAM,IACpE,gBAEA,CAQA,WACA,wDACA,CAMA,kBACA,YACA,mCACA,wCACA,0CACA,8CACA,0CAEA,CAEA,kBACA,uBACA,UAAkB,2BAA6B,CAC/C,iCACA,4DAEA,iCACA,gCAEA,sBAAmC,cAAsB,EAAE,GAAO,EAAE,GAAO,EAAE,iBAA+B,KAC5G,0BACA,0BACA,8BACA,kDACA,CAAS,CACT,CAEA,cAIA,GAHA,qCAA8C,mBAAmB,GACjE,uBACA,4BACA,YAIA,CACA,eACA,8CAA+D,eAAe,GAG9E,6CAEA,2CACA,8BACA,2BACA,4CACA,0BACA,EACA,6BACA,4BACA,oBAEA,EACA,4BACA,cACA,kCACa,CACb,MAzBA,wDACA,mFAyBA,8CACA,CAEA,gBACA,gCACA,yBACA,wDACA,sCACA,gDACA,CAEA,gBACA,gCACA,yBACA,gDACA,CAEA,oBACA,oCAA4D,EAAc,OAC1E,CAEA,IAH0E,UAG1E,KACA,6BACA,SAEA,4BACA,yBACA,SAAkB,EAAI,EAAE,EAAO,EAAE,EAAM,EAEvC,oBACA,MACA,KACA,QAEA,CACA,oBAAqD,8BAAgC,EACrF,wBACA,CACA,QACA,CACA,CACA,QACA,mBACA,8BACA,oBACA,oBACA,sBACA,mBACA,gBAA0B,EAAa,eACvC,aACA,cACA,WACA,mBAEA,CEnfO,sBACP,eACA,SACA,yBACA,wBACA,CACA,CACO,cACP,0DACA,CACO,kBACP,iBACA,SACA,4BACA,aACA,CACA,SACA,OACA,eACA,qBACA,mBAEA,CACA,CACO,kBACP,iBACA,SACA,gCACA,oBACA,CACA,CCrBO,IAAM,EAAY,IACzB,MAUA,OARA,CAHyB,CAEzB,IAGA,0BACA,QAA8B,sCAA8B,QAAS,UAAgB,YAGrF,OAEA,eACA,EACO,MAtBsB,eAsBiB,CAtBjB,GAE7B,yCACA,cAAoC,IAAM,aAA+B,SAAY,MACrF,cAAmC,IAAM,cAAmC,SAAY,MACxF,kBAJA,EAIgC,mBAJJ,CAA5B,EAIgC,OAJJ,kCAA+D,MAAiB,EAI5E,UAChC,8BACA,CAAK,EACL,EAc8C,uCAC9C,6BAEA,OAAsB,uCAA8B,UAEpD,QACA,CAAC,EACM,MACP,oBACA,sBAEA,uCACA,SAEA,SAKA,OAJA,oCAEA,EADA,kEACA,KACA,CAAK,EACL,CACA,EC1CA,IhBuBO,EKjBA,EAOI,EAQJ,EASA,EAIA,EG7BA,EEDA,ECEA,EAOA,EAOA,GKpBH,GAAyB,EXaJ,OWbI,SAE7B,KAFqB,EAErB,EAFyB,CAEzB,QAF6B,MAE7B,iBACA,cAAoC,IAAM,aAA+B,SAAY,MACrF,cAAmC,IAAM,cAAmC,SAAY,MACxF,kBAJA,CAIgC,oBAJJ,CAA5B,EAIgC,mBAJJ,sBAA+D,MAAiB,EAI5E,UAChC,8BACA,CAAK,CACL,EAGA,4EACA,YAAgD,GAAS,iCAEzD,YADA,OAAsB,GAAe,GACrC,SADqC,CACrC,wBACA,EACA,OACA,SACA,MAAuB,EAAe,qBACtC,CAAS,EACT,UACA,MAAuB,EAAmB,SAC1C,CAAS,EAGT,KAJ0C,CAIvB,EAAmB,SAEtC,CAAC,EACD,KAHsC,CAGtC,SACA,cAAqB,gDACrB,UACA,GAEA,yBAAqC,kCAAoC,2BACzE,GACA,2BAEA,8BAAyC,OACzC,EACA,yBACA,OAAW,GAAS,+BACpB,2BACA,iBACA,SACA,SACA,cACA,iCACA,EACA,QACA,CAAa,EACb,cACA,mBACA,CAAS,CACT,CAAK,CACL,CACO,qBACP,OAAW,GAAS,+BACpB,wBACA,CAAK,CACL,CACO,uBACP,OAAW,GAAS,+BACpB,2BACA,CAAK,CACL,CAWO,uBACP,OAAW,GAAS,+BACpB,6BACA,CAAK,CACL,uBC/EI,GAAyB,kBAE7B,CAFa,IAAI,EAEjB,OAFqB,CAErB,GAFyB,MAEzB,iBACA,cAAoC,IAAM,aAA+B,SAAY,MACrF,cAAmC,IAAM,cAAmC,SAAY,MACxF,kBAJA,EAIgC,kBAJJ,EAA5B,EAIgC,OAJJ,kCAA+D,MAAiB,EAI5E,UAChC,8BACA,CAAK,CACL,EAIA,QACA,UACA,SACA,QACA,cACA,WACA,CACA,EACA,IACA,oBACA,wBAA6B,eAC7B,SACA,CACe,UACf,kBAAiC,MACjC,WACA,eACA,gBACA,WAAqB,EAAY,EACjC,CAQA,eATiC,CASjC,QACA,OAAe,GAAS,+BACxB,IAEA,IADA,EACA,gCAA8D,QAC9D,gCAA4D,4BAAyC,4BAAoC,EACzI,YACA,8CACA,iBACA,sCACA,GACA,4CAEA,gBAEA,qDACA,MACA,sCACA,GACA,8CAIA,IACA,8BAA0D,eAAqB,EAC/E,gCACA,GACA,yDAGA,4BACA,iCAA4D,gBAE5D,kCACA,wBACA,sBAAgD,SAAS,UAAU,EAAM,wBAAmB,mBAA6B,4BAA0E,iBAAyB,EAAI,GAChO,iBACA,QACA,OACA,MAAgC,8BAAkD,CAClF,UACA,EAIA,OAA6B,gBAD7B,CAC6B,CAE7B,CACA,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CAOA,cACA,OAAe,GAAS,+BACxB,wCACA,CAAS,CACT,CAOA,2BACA,OAAe,GAAS,+BACxB,kCACA,wBACA,0CAAkE,EAAM,GACxE,8BACA,IAEA,IADA,EACA,iBAAgD,iBAAqC,IACrF,gCAA8D,gBAAmB,4BAAoC,CACrH,8CACA,iBACA,sCACA,gBAEA,oDACA,MACA,uCAGA,IACA,8BAA0D,eAAqB,EAC/E,iCAEA,qCACA,aACA,OACA,SACA,CAAiB,EACjB,iBACA,QACA,OACA,MAAgC,sBAAqC,CACrE,UACA,EAIA,OAA6B,gBAD7B,CAC6B,CAE7B,CACA,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CAQA,2BACA,OAAe,GAAS,+BACxB,IACA,4BACA,kBAAgD,cAChD,4BACA,uBAEA,YAAmC,GAAI,cAAgB,SAAS,sBAAsB,EAAM,IAAK,EAAI,UAAS,EAC9G,0BACA,8BACA,MACA,UAA8B,EAAY,4BAE1C,OAAyB,MAAQ,sCAAwC,YACzE,CACA,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CAOA,cACA,OAAe,GAAS,+BACxB,uCACA,CAAS,CACT,CAQA,YACA,OAAe,GAAS,+BACxB,IAOA,OAAyB,KANzB,MAAmC,GAAI,cAAgB,SAAS,eAChE,uBACA,YACA,iBACA,qDACiB,EAAI,qBAAuB,EACnB,WACzB,CACA,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CAQA,YACA,OAAe,GAAS,+BACxB,IAOA,OAAyB,MAAQ,KANjC,OAAmC,GAAI,cAAgB,SAAS,eAChE,uBACA,YACA,iBACA,qDACiB,EAAI,sBAAuB,EACX,IAAgB,YACjD,CACA,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CASA,uBACA,OAAe,GAAS,+BACxB,IACA,4BACA,QAAiC,GAAI,cAAgB,SAAS,eAAe,EAAM,2BAAmB,EAAW,+BAA6E,uBAA+B,EAAI,GAAM,qBAAuB,EAC9P,8BACA,aAAmC,8BAAkD,EACrF,GAGA,OAAyB,KADzB,GAAyB,UADzB,aAA+C,SAAS,EAAE,YAAe,EAAE,EAAmB,EACrE,EACA,WACzB,CACA,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CAQA,wBACA,OAAe,GAAS,+BACxB,IACA,YAAmC,GAAI,cAAgB,SAAS,eAAe,cAAc,aAAK,UAAkB,EAAI,qBAAuB,EAC/I,8BACA,aAAmC,8BAAkD,EACrF,GACA,OACA,4CAA6E,KAAY,sBACzF,aAA2C,SAAS,EAAE,YAAgB,EAAE,EAAmB,GAC3F,KAAoC,GACpC,UACA,CACA,CACA,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CAOA,cACA,OAAe,GAAS,+BACxB,4CAEA,kEAAmJ,EACnJ,QAA0D,EAAoB,KAC9E,IACA,4BACA,QAAkC,GAAG,cAAgB,SAAS,GAL9D,wCAK4E,GAAG,EAAM,EAAE,EAAY,GACnG,qBACA,gBACA,CAAiB,EAEjB,OAAyB,KADzB,eACyB,WACzB,CACA,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CAKA,QACA,OAAe,GAAS,+BACxB,4BACA,IACA,YAAmC,GAAG,cAAgB,SAAS,eAAe,EAAM,GACpF,qBACiB,EACjB,OAAyB,KAAM,EAAgB,eAE/C,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CAKA,UACA,OAAe,GAAS,+BACxB,4BACA,IAIA,OAHA,MAAsB,IAAI,KD5SnB,SACP,OAAW,GAAS,+BACpB,mDAAkF,KAAc,iBAAqB,EAF9G,OAGP,CAAK,CACL,ECwS0B,cAAgB,SAAS,UAAU,EAAM,GACnE,oBACA,CAAiB,EACjB,CAAyB,mBACzB,CACA,SACA,GAAoB,EAAc,iBAA4B,EAAmB,CACjF,gBADiF,KACjF,CACA,+CACA,OAAiC,gBAEjC,CACA,OACA,CACA,CAAS,CACT,CASA,kBACA,4BACA,KACA,8BACA,YAA0B,8BAAkD,EAC5E,EACA,SACA,UAEA,4CAEA,kEAA+I,CAC/I,SACA,UAEA,kBAIA,MAHA,QACA,OAA8B,GAAY,EAE1C,CACA,MAAoB,uBAAwB,SAAS,GAVrD,0BAUmE,UAAU,EAAM,EAAE,EAAY,GAAI,CAErG,CAMA,UACA,OAAe,GAAS,+BACxB,IAEA,OAAyB,KADzB,MAAmC,GAAM,cAAgB,SAAS,UAAU,cAAc,GAAK,WAAiB,EAAI,qBAAuB,EAClH,WACzB,CACA,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CAgEA,YACA,OAAe,GAAS,+BACxB,IACA,kDAAyE,SAAuC,aAAoB,EAEpI,OAAyB,KADzB,MAAmC,GAAI,cAAgB,SAAS,eAAe,cAAc,KAAW,qBAAuB,IACtG,WACzB,CACA,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CACA,kBACA,wBACA,CACA,mBACA,KAAyB,IAAN,GACA,GADM,GACA,wBAEzB,OACA,CACA,iBACA,SAAkB,cAAc,GAAG,EAAK,EAExC,uBACA,mDACA,CACA,8BACA,SAgBA,OAfA,SACA,gBAAiC,QAAgB,GAEjD,UACA,iBAAkC,SAAiB,GAEnD,UACA,iBAAkC,SAAiB,GAEnD,UACA,iBAAkC,SAAiB,GAEnD,WACA,kBAAmC,UAAkB,GAErD,WACA,CACA,CEniBO,IAAM,GAAe,CAAK,qBAAL,SAAoC,IAAQ,ECDxE,IAAI,GAAyB,MDC0C,CAAC,ECD3C,SAE7B,GAFa,IAAI,GAEjB,MAFqB,CAErB,GAFyB,IAEzB,KAF6B,OAE7B,KACA,cAAoC,IAAM,aAA+B,SAAY,MACrF,cAAmC,IAAM,cAAmC,SAAY,MACxF,kBAJA,EAIgC,OAJJ,EAII,UAJJ,CAA5B,EAIgC,mBAJJ,sBAA+D,MAAiB,EAI5E,UAChC,8BACA,CAAK,CACL,CAKe,UACf,kBAAiC,IACjC,WACA,2CAAqD,CAAE,IAAe,GACtE,WAAqB,EAAY,EACjC,CAIA,EANsE,WAMtE,CACA,CANiC,MAMlB,GAAS,+BACxB,IAEA,OAAyB,KADzB,MAAmC,GAAG,cAAgB,SAAS,UAAY,qBAAuB,EACzE,WACzB,CACA,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CAMA,aACA,OAAe,GAAS,+BACxB,IAEA,OAAyB,KADzB,MAAmC,GAAG,cAAgB,SAAS,UAAU,EAAG,GAAK,qBAAuB,EAC/E,WACzB,CACA,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CAcA,kBACA,SACA,CAAK,EACL,OAAe,GAAS,+BACxB,IAQA,OAAyB,KAPzB,MAAmC,GAAI,cAAgB,SAAS,UAChE,KACA,OACA,gBACA,gCACA,sCACiB,EAAI,qBAAuB,EACnB,WACzB,CACA,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CAaA,kBACA,OAAe,GAAS,+BACxB,IAQA,OAAyB,KAPzB,MAAmC,GAAG,MJxC/B,WACP,OAAW,GAAS,+BACpB,sBAFO,OAEP,EACA,CAAK,CACL,EIoCsC,cAAgB,SAAS,UAAU,EAAG,GAC5E,KACA,OACA,gBACA,gCACA,qCACA,CAAiB,EAAI,qBAAuB,EACnB,WACzB,CACA,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CAMA,eACA,OAAe,GAAS,+BACxB,IAEA,OAAyB,KADzB,MAAmC,GAAI,cAAgB,SAAS,UAAU,EAAG,UAAW,EAAI,qBAAuB,EAC1F,WACzB,CACA,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CAOA,gBACA,OAAe,GAAS,+BACxB,IAEA,OAAyB,KADzB,MAAmC,GAAM,cAAgB,SAAS,UAAU,EAAG,IAAK,EAAI,qBAAuB,EACtF,WACzB,CACA,SACA,GAAoB,EAAc,GAClC,OAA6B,EADK,GACL,aAE7B,QACA,CACA,CAAS,CACT,CACA,CC/JO,iBAA4B,GACnC,aADmD,CACnD,IAAiC,IACjC,YACA,CAMA,QACA,WAAmB,GAAc,mCACjC,CACA,CETA,IAAI,GAAS,EAAE,CAAL,GAEU,GACZ,QADuB,EAAE,OAAtB,IAAI,CACJ,MAAM,CACc,WAAW,EAAE,OAA1B,QAAQ,CACf,KAAK,CACgB,WAAW,EAAhC,OAAO,SAAS,EAAoB,SAAS,IAA0B,EAAE,WAA3B,OAAO,CACrD,cAAc,CAEd,MAAM,CAKV,IAAM,GAAyB,CACpC,OAAO,CAHsB,CAGpB,SADwB,MAFa,CAAE,aAGxB,EAHuC,MAAM,IAAI,CAAS,CAAE,CAIrF,CAEY,GAAqB,CAChC,MAAM,CAAE,OADqB,CACb,CACjB,CAEY,CAVqE,EAUnB,CAC7D,gBAD+B,EACb,EAClB,EADsB,YACR,EAAE,EAChB,EADoB,gBACF,EAAE,EACpB,EADwB,MAChB,CAAE,UAAU,CACrB,CAEY,GAAkD,EAAE,gBC7B1D,GD6B8B,CC7BxB,GAAe,IAC1B,IAAI,EAQJ,CAT8C,EAAS,CACtC,CADM,EAGrB,EADE,IACI,GAAG,IADI,EAAE,EAEsB,EAA5B,CADW,MACJ,KAAK,CACZ,UAA6B,CAE7B,KAAK,EAET,CAAC,GAAG,IAAuB,CAAI,CAAF,GAAa,CAAJ,CAAC,CAC/C,CADsD,CAAC,EAGf,GAAG,CAC1C,CAD4C,UACV,EAAE,KADA,EACzB,OAAO,CACT,UAAgB,CAGlB,OAAO,CAGH,GAAgB,CAC3B,EACA,EACA,KAHwB,EACL,EAIb,EAFa,CADyB,CAErC,CACoB,CADlB,EAEH,EAAqB,KAE3B,CAHsC,CAAC,GAAb,CAGnB,CAAO,EAAO,EAFG,CAEL,CAAM,EAAE,KAFyB,EAAE,wRAEzB,uCAC3B,IAAM,EAAc,SAAH,MAAU,GAAc,CAAE,CAAC,EAAI,EAC5C,EAAU,GAD2B,CACvB,CAAP,EADgD,KACtB,EAAI,OAAJ,CAAD,CAAO,EAAF,IAAJ,CAAa,CAAC,CAUnD,CAVyC,MAErC,EAAS,GAAG,CAAC,CAAL,OAAa,CAAC,EAAE,EAClB,GAAG,CAAC,CAAL,OAAa,CAAE,GAGpB,EAAS,GAAG,CAAC,CAAL,CAHuB,CAAC,YAGJ,CAAC,EAAE,EACzB,GAAG,CAAC,CAAL,cAAoB,CAAE,UAAU,EAAW,CAAE,CAAC,CAGhD,EAAM,EAAK,CAAN,CAHwC,CAGlC,4BAAO,GAAI,SAAE,CAAO,GAAG,CAC1C,CACH,CAAC,CE/CY,GAAO,SCaP,GAAe,CAAK,eDbb,CCaa,aAA8B,GAAnC,CAA2C,EAKhE,yBAL+D,GAM/D,IACP,cACA,+CACA,iBACA,CAAK,EAEE,iBAAsC,EAAE,gBAAgB,EAAE,aAAa,EAAE,KCzBzE,wBACP,mBACA,SACA,sBACA,sBACA,cACA,WACA,CACA,CACO,eACP,uDACA,CACO,oBACP,mBACA,aACA,yBACA,cACA,WACA,CACA,CAIO,oBACP,iBACA,SACA,6BACA,oBACA,CACA,CACO,oBACP,qBACA,aACA,YACA,aACA,CACA,CACO,oBACP,cACA,mEACA,CACA,CAIO,oBACP,cACA,gFACA,CACA,CACO,oBACP,eACA,iDACA,CACA,CACO,oBACP,sBACA,qDACA,kBACA,cACA,CACA,SACA,OACA,eACA,qBACA,mBACA,qBAEA,CACA,CAIO,oBACP,sBACA,qDACA,kBACA,cACA,CACA,SACA,OACA,eACA,qBACA,mBACA,qBAEA,CACA,CACO,oBACP,iBACA,2CACA,CACA,CACO,eACP,iDAOO,oBACP,mBACA,mDACA,cACA,CACA,CAIO,oBACP,eACA,gDACA,CACA,CCzGA,oFAKA,uBAKA,SACA,iBACA,YAAoB,WAAoB,KACxC,QAEA,YAAoB,YAA6B,KACjD,0BAEA,YAAoB,YAAyB,KAC7C,yBAEA,SACA,CAAC,GAQM,mBACP,YAGA,IAFA,qBACA,gBACA,iBAEA,KADA,2BACA,EACA,qBAGA,kBAGA,IAFA,gCACA,eACA,iBAEA,KADA,2BACA,EACA,eAGA,CAQO,mBACP,YACA,QAIA,IAFA,qBACA,gBACA,iBACA,+BACA,qBAGA,UAEA,YAGA,6CAAyD,uBAA8B,GAEvF,CA0BO,eACP,SACA,MACA,+BACA,EACA,GACA,UACA,WACA,EACA,GAAuB,sBACvB,OACA,SAmEO,OACP,kBACA,iBACA,KAIA,YAAiC,IAAgB,KACjD,kBACA,YACA,KACA,CAEA,iBACA,sBAEA,iBACA,sBAEA,iBACA,qBAGA,qCAEA,aACA,MACA,gBACA,UACA,qCAEA,iCACA,aACA,eACA,cAEA,CACA,EAxGA,MACA,EACA,YAAoB,WAAgB,KACpC,wBAEA,iBACA,CClHO,IAAM,GAAS,6DACtB,IACA,UACA,WACA,EAIO,QACP,IAAS,KACT,SAEA,GAHkB,CAIlB,4CACA,QAEA,CACA,SAEA,QACA,CACA,aACA,mBAEA,cAA8B,cAAc,EAAE,cAAc,EAC5D,IACA,qCACA,sCACA,aACA,cACA,CACA,SAGA,aACA,cACA,CACA,oBAyBa,GAAY,IACzB,MAUA,OARA,EADA,EAFyB,EAKzB,0BACA,QAA8B,sCAA8B,QAAS,UAAgB,YAGrF,OAEA,eACA,EACO,MACP,oBACA,UACA,cACA,UACA,YACA,0BAGO,kBACP,oCACA,EACO,gBACP,yBACA,MACA,YAEA,IACA,oBACA,CACA,SACA,QACA,CACA,EACO,gBACP,qBACA,CAMO,UACP,cAGA,+CAGA,eACA,aACA,CAAS,CACT,CACA,CAEO,eACP,mBACA,gBACA,UAAkB,GAAmB,yBAGrC,YAAoB,WAAkB,IACtC,IAAa,GAAe,WAC5B,CAD4B,KAC5B,IAAsB,GAAmB,+BAazC,MAVA,CAEA,kBAA2B,GAAmB,OAC9C,SAD8C,IAC9C,MAA4B,GAAmB,OAC/C,SAD+C,CDkFxC,YACP,SACA,GAAoB,sBACpB,MACA,SACA,EACA,YAAoB,WAAgB,KACpC,wBAEA,wBACA,EC3FwC,MACxC,KACA,YACA,aACS,CAGT,CAIO,qBACP,6BACA,yBACA,CAAK,CACL,CA8BA,eACA,qCACA,CAiBA,qBAEA,MADA,kBACA,UAGA,kBADA,eADA,0CAGA,+BACA,QACA,CACO,2BACP,4BACA,wBACA,iCAEA,mHACA,GAGA,KADA,aACA,uDACA,CACO,4BACP,iBAlCA,sBADA,IAEA,+BACA,2EACA,WACA,KACA,YAAwB,EANxB,GAM4C,IAC5C,yCAEA,QACA,CAEA,OADA,0BACA,yBACA,IAuBA,GACA,IACA,0BAEA,cAAmC,EAAW,mBAC9C,kBACA,uBACA,YA7GA,8BAgHA,gBAAmC,EAAE,kDA2CrC,cAA8B,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,GAAG,GAC1E,eACP,eACA,0EAEA,CCpSA,OAA0B,OAAZ,EAAY,KAC1B,EADkB,EAClB,EADsB,CACtB,EACA,MAF0B,EAE1B,kEACA,YACA,4DACA,8CAA6D,WAAc,IAC3E,uEACA,kBAEA,QACA,EAIA,IAAM,GAAgB,qEACtB,iBACO,eAAe,GAAW,OACjC,MAQA,CATiC,CAgBjC,EAdA,IAAS,GAAsB,GAC/B,UAAkB,GAAwB,GADX,GAC2B,GAE1D,WAFyC,CAAiB,EAE1D,WAEA,UAAkB,GAAwB,GAAgB,aAG1D,IAHyC,CAAiB,CAI1D,cACA,CACA,SACA,UAAkB,GAAiB,GAAgB,KACnD,CAEA,IAHkC,EAGH,MAHoB,GDuN5C,GACP,WCrNsD,IDqNtD,KAA4C,IAC5C,OAGA,YAJmE,CAEnE,YAKA,IAEA,OADA,YAAiC,EAAW,cAE5C,CACA,SACA,WACA,CACA,ECnOsD,GAWtD,GAVA,GACA,aAAwC,EAAY,0BACpD,oBACA,GACA,wBACA,SAEA,sDACA,iBAEA,EAYA,wBACA,UAAkB,GAAsB,GAAgB,eAAjB,GAAiB,iDAExD,2BAIA,UAAkB,EAClB,MAlBA,eAiByC,KAjBzC,GACA,GACA,kCACA,iBACA,wCACA,gCACA,gEACA,UAAsB,GAAsB,GAAgB,eAAjB,GAAiB,kBAY5D,WAAc,GAAa,GAAgB,MAAjB,KAAiB,QAC3C,CACA,IAAM,GAAiB,YACvB,OADuB,OACF,gDACrB,UACA,GAEA,yBAAqC,iCAAkC,eAAgB,2BACvF,yBACA,8BAAyC,OACzC,EACO,2BACP,MACA,sBAAoC,0BACpC,GAAiB,GAAuB,EACxC,GAAgB,GAAuB,CAAI,EAAY,SADf,KACe,GAAhB,EAAgB,EAEvD,wBACA,2BAA6C,OAAY,EAEzD,2CACA,gCACA,6BAEA,qEACA,QAAuB,GAAc,SACrC,OADqC,CACrC,EACA,6CACK,GAAI,wBACT,0DAAyJ,qBAAsB,eAC/K,CACA,eAAe,GAAc,aAC7B,GAD6B,CAE7B,EADA,EAA0B,GAAiB,SAE3C,IACA,MAH2C,EAG3C,oBAAoD,IACpD,CACA,SAGA,MAFA,iBAEA,IAAkB,GAAwB,GAAgB,KAC1D,CAIA,GAHA,MACA,EAHyC,CAAiB,GAG5C,GAAW,GAEzB,WAFyB,EAEzB,kBACA,SAEA,IACA,qBACA,CACA,SACA,MAAc,GAAW,EACzB,CACA,CACO,UAHkB,EAGlB,OACP,EDtHO,ECiLP,EA1DA,UA2DA,EADA,EAzDA,GA0DA,8CAzDA,kBAAkC,IAClC,cACA,eD3HO,CC2H0B,CAAS,aD1H1C,2BACA,ECyH0C,GAI1C,OAAa,cAAQ,OADrB,oBACqB,CAAe,YACpC,CACO,eACP,YAWA,MAVA,UACA,iBACA,kCACA,wCACA,gCACA,yBACA,0CACA,iEACA,uCAEA,CACA,CACO,eACP,MAEA,OAAa,MAAQ,KADrB,oBACqB,CAAM,YAC3B,CACO,eACP,YAAa,aACb,CACO,eACP,gBAAY,gEAAuE,EASnF,OACA,MACA,WAVA,CACA,cACA,YACA,eACA,cACA,mBACA,EAKA,KAJA,gBAAiC,CARkD,mFAanF,CAAS,CACT,UACA,CACA,CACO,eACP,QACA,CC9KO,mCCAP,IAAI,GAAsB,cAC1B,EADU,EACV,KACA,MAFkB,EAElB,EAFsB,GAEtB,MAF0B,GAE1B,oDACA,YACA,4DACA,8CAA6D,WAAc,IAC3E,uEACA,kBAEA,QACA,CAKe,OAAM,GACrB,iBAAkB,SADiB,IACjB,IAAsB,SAAU,EAClD,WACA,eACA,WAAqB,GAAY,GACjC,UACA,QAFiC,IAEjC,6BACA,0CACA,CACA,CAMA,kBAA+B,EAAe,KAC9C,GAA2B,EAAf,GAAe,WAC3B,CAD2B,KAC3B,2DAAiF,GAAe,WAAY,CAAZ,EAEhG,IAMA,OALA,MAAkB,GAAQ,qBAAwB,SAAS,gBAAgB,EAAM,GACjF,qBACA,MACA,gBACA,CAAa,EACb,CAAqB,qBACrB,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,kBAEzB,QACA,CACA,CAMA,8BAA+C,EAC/C,IACA,aAAyB,GAAQ,qBAAwB,SAAS,UAClE,YAAwB,cAA2B,CACnD,qBACA,wBACA,MAAuB,EACvB,CAAa,CACb,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,UAAY,SAE7C,QACA,CACA,CAQA,sBACA,IACA,YAAoB,GAAU,IAAiB,GAAM,eACrD,CADqD,CACrD,8BAAuD,OAMvD,MALA,iBAEA,sCACA,mBAEA,MAAyB,GAAQ,qBAAwB,SAAS,uBAClE,OACA,qBACA,MAAuB,GACvB,kBAD4C,CAC5C,mBACA,CAAa,CACb,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CACA,MACA,gBACA,SACA,CAAqB,CACrB,OACA,CAEA,QACA,CACA,CAMA,oBACA,IACA,aAAyB,GAAQ,qBAAwB,SAAS,eAClE,OACA,qBACA,MAAuB,EACvB,CAAa,CACb,CACA,QAHoC,CAIpC,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,UAAY,SAE7C,QACA,CACA,CAOA,mBACA,kBACA,IACA,OAAiC,kCACjC,QAAmC,GAAQ,oBAAuB,SAAS,eAC3E,qBACA,iBACA,OACA,wEACA,8EACA,CAAiB,CACjB,MAAuB,EACvB,CAAa,EACb,WACA,cACA,qBACA,+CACA,qEASA,OARA,aACA,cACA,yBAAuD,oCACvD,uBAAwD,oBACxD,MAAkC,EAAI,QACtC,CAAiB,EACjB,qBAEA,CAAqB,mCAAoC,kBACzD,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,SAAW,SAE5C,QACA,CACA,CAQA,qBACQ,GAAY,GACpB,IACA,EAFoB,KAEpB,MAAyB,GAAQ,oBAAuB,SAAS,eAAe,EAAI,GACpF,qBACA,MAAuB,EACvB,CAAa,CACb,CACA,QAHoC,CAIpC,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,UAAY,SAE7C,QACA,CACA,CAQA,0BACQ,GAAY,GACpB,IACA,EAFoB,KAEpB,MAAyB,GAAQ,oBAAuB,SAAS,eAAe,EAAI,GACpF,OACA,qBACA,MAAuB,EACvB,CAAa,CACb,CACA,QAHoC,CAIpC,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,UAAY,SAE7C,QACA,CACA,CAUA,yBACQ,GAAY,GACpB,IACA,EAFoB,KAEpB,MAAyB,GAAQ,uBAA0B,SAAS,eAAe,EAAG,GACtF,qBACA,MACA,oBACA,CAAiB,CACjB,MAAuB,EACvB,CAAa,CACb,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,UAAY,SAE7C,QACA,CACA,CACA,sBACQ,GAAY,UACpB,IACA,SAAoB,WAAc,MAAQ,GAAQ,oBAAuB,SAAS,eAAe,SAAc,WAC/G,qBACA,SACA,EAA6B,cAAQ,EAAS,aAE9C,CAAa,EACb,OAAqB,eACrB,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,kBAEzB,QACA,CACA,CACA,uBACQ,GAAY,UACZ,GAAY,MACpB,GADoB,CAKpB,OAAqB,KAHrB,MAA+B,GAAQ,uBAA0B,SAAS,eAAe,SAAc,WAAW,KAAU,GAC5H,qBACa,EACQ,WACrB,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,kBAEzB,QACA,CACA,CACA,CC7QO,QACP,WACA,KAGA,eAHiC,QAGjC,YAFA,KAIA,gBACa,MAGb,cAHiC,SAGjC,aACA,CAAK,CACL,eACa,MAGb,cAHiC,SAGjC,cACA,CAAK,EAME,gBAA6C,EACpD,OACA,WACA,WAEA,gBACA,MACA,CAAS,CACT,eACA,YACS,CAET,CEpCO,QAIP,qBACQ,MACR,cAD4B,SAC5B,EACA,2EACA,CAMO,wBACP,eACA,SACA,wBACA,CACA,CACO,oBACP,CA4BO,yBACP,UACA,oEAEA,qCAiBA,OAhBA,KACA,gBACA,UACA,UACA,qEAEA,CAAS,IAUT,4EACA,CACA,iBACA,cACA,EACA,CACA,iBACA,gBACS,WACT,MACA,UACA,qEAEA,IACA,gBACA,QACA,CACA,UACA,oEAEA,CACA,CAEA,SAIA,MAHA,UACA,+EAEA,6DAAiH,EAAK,uBAGtH,YACA,IACA,+CACA,2FACA,CACA,SACA,sFACA,CAOA,OADA,wQACA,SAGA,CAAK,EACL,CDtHA,+BAEA,IACA,oDACA,eACA,WACA,CAAa,CACb,eACA,CAAS,EAET,+BAEA,iCACA,CACA,SACA,0BAEA,sBAEA,CEZA,QACA,IVFO,CUEE,UAAU,aACnB,WVFO,CUES,WAAW,UAC3B,oBACA,kBACA,sBACA,QAAa,GACb,oBACA,SACA,IAH4B,yBAG5B,EACA,EACA,yBACA,gBACA,CACe,SAIf,eACA,OACA,yBACA,iCACA,4BACA,oCACA,6BAOA,4BACA,2BACA,qCACA,kCACA,qBACA,sBAIA,2BACA,wBACA,kCACA,qBACA,mBAAmC,MACnC,WAD4C,CAC5C,iNAEA,oCAAuD,QA0DvD,GAzDA,gCACA,4BACA,sBAEA,qCACA,6BACA,yCACA,eAAyB,GAAc,CACvC,UACA,eAFuC,EAEvC,CACA,cACS,EACT,eACA,uBACA,WAAqB,GAAY,SACjC,YADiC,IACjC,KACA,6CACA,yBACA,iEACA,OACA,iBAEiB,MAAS,wEAC1B,UAAwB,GAGxB,UAHqC,GAKrC,WAAsB,SACtB,4CACA,UACA,+BACA,+BACA,mCACA,qCACA,yCACA,uDACA,8EACA,EACA,oBACA,UACA,uBAGoB,KACpB,aAAmC,EADK,EAIxC,eAHsD,GAGtD,IACA,aAAmC,GAAyB,0BAK5D,kBACA,aAA2B,GAAyB,qBAExC,CAFwC,KAE/B,mEACrB,IACA,sEACA,CACA,SACA,yGACA,CACA,wEACA,0EACA,gEACA,CAAa,CACb,CACA,GAH+F,CAG/F,aACA,CACA,aAIA,OAHA,uBACA,4BAAwC,iBAAiB,GAAG,GAAQ,IAAI,wBAAL,CAA8B,QAEjG,KAOA,0BACA,wBAGA,kCACA,oCACA,yBAEA,CAAS,IANT,6BAeA,wBACA,EACA,IACA,MP1GO,YACP,SACA,COwGiD,CPxGjD,WACA,2BACA,IAEA,IADA,qCACA,gBACA,MACA,CAAa,CACb,CACA,SAEA,CAMA,OAHA,+BACA,MACA,CAAK,EACL,CACA,EOuFiD,sBACjD,SAaA,GAZA,iCACA,aAEA,+BACA,WAQgB,MAAS,qCACzB,SAAwB,WAAc,mCACtC,MAEA,GADA,CACwB,GADxB,6BACwD,+BADxD,GT3GA,GS4GwD,IT5GxD,0CS4GwD,CACxD,wCACA,kCACA,0BACA,oCACA,OAAqC,QAErC,CAIA,OADA,4BACA,OAA6B,EAC7B,CACA,YAAwB,kBAAwB,EAWhD,OAVA,4EACA,2BACA,qBACA,eACA,wDAGA,+CAEA,CAAiB,IACjB,CAAyB,WACzB,CAGA,OADA,gCACA,CAAqB,WACrB,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,OAAyB,GAEzB,OACA,UAA2B,GAAgB,2CAC3C,CACA,QACA,CACA,qCACA,mCACA,CACA,CAMA,2BACA,UACA,IASA,SAAoB,WARpB,MAA8B,GAAQ,qBAAwB,SAAS,UACvE,qBACA,MACA,oEAAsM,CACtM,sBAA4C,uEAA0J,CACrL,CACjB,MAAuB,EACvB,CAAa,EAEb,SACA,EAJuC,IAIvC,CAAyB,MAAQ,uBAA2B,UAE5D,gBACA,SAKA,OAJA,YACA,mCACA,iDAEA,CAAqB,MAAQ,iBAAe,YAC5C,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,uBAA2B,SAE5D,QACA,CACA,CAWA,gBACA,UACA,QACA,EACA,gBACA,UAAwB,wBAA2B,EACnD,OACA,MACA,yBAEA,aAAiE,GAAyB,+BAE1F,QAA4B,GAAQ,qBAAwB,SAAS,UACrE,qBACA,4CACA,MACA,QACA,WACA,yCAAuI,CACvI,sBAAgD,4CAAuF,CACvI,iBACA,uBACA,CAAqB,CACrB,MAA2B,EAC3B,CAAiB,CACjB,MACA,MAH2C,OAG3C,GACA,UAAwB,wBAA2B,EACnD,QAA4B,GAAQ,qBAAwB,SAAS,UACrE,qBACA,MACA,QACA,WACA,yCAAuI,CACvI,mDACA,sBAAgD,4CAAuF,CAClH,CACrB,MAA2B,EAC3B,CAAiB,CACjB,MAEA,MAJ2C,IAIjB,GAA2B,mEAErD,SAAoB,WAAc,EAClC,SACA,OAAyB,MAAQ,uBAA2B,UAE5D,gBACA,SAKA,OAJA,YACA,mCACA,iDAEA,CAAqB,WAAQ,YAAe,YAC5C,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,uBAA2B,SAE5D,QACA,CACA,CASA,4BACA,QACA,EACA,gBACA,UAAwB,wBAA2B,EACnD,QAA4B,GAAQ,qBAAwB,SAAS,6BACrE,qBACA,MACA,QACA,WACA,sBAAgD,4CAAuF,CAClH,CACrB,MAA2B,EAC3B,CAAiB,CACjB,MACA,cAHmD,CAGnD,CACA,UAAwB,wBAA2B,EACnD,QAA4B,GAAQ,qBAAwB,SAAS,6BACrE,qBACA,MACA,QACA,WACA,sBAAgD,4CAAuF,CAClH,CACrB,MAA2B,EAC3B,CAAiB,CACjB,MAEA,UAA0B,GAA2B,CAJF,uBAIE,2CAErD,SAAoB,WAAc,EAClC,KACA,OAAyB,MAAQ,uBAA2B,UAE5D,2BACA,OAAyB,MAAQ,uBAA2B,WAAa,EAA6B,EAMtG,OAJA,YACA,MAHsG,IAGtG,yBACA,yDAEA,CACA,oBAAsC,8BAAwC,kBAA0B,8BAAmC,MAC3I,OACA,CACA,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,uBAA2B,SAE5D,QACA,CACA,CAKA,yBACA,YACA,oDACA,mDACA,2CACA,qDACA,qEACS,CACT,CAIA,gCAEA,OADA,6BACA,8BACA,gCAEA,CAKA,wBACA,UAAgB,GAAQ,EACxB,gBACA,qCAEA,sDAAiE,EAAM,GACvE,CACA,8BACA,4BACA,EACA,EACA,iBACA,YACA,kBAEA,CACA,IACA,EADA,CAAoB,wCAAoC,EAExD,GAAiB,KAMjB,YAN0B,CAM1B,SACA,QAEA,CACA,aACA,iBACA,2BACA,0DACA,0BACA,yCACA,gBAGA,2QAAmS,6CAA6C,YAEhV,KArB0B,CAC1B,+CACA,qGAEA,GACA,CAiBA,qEACA,2BACA,IAGA,EAHA,4DAAuG,kCAAoC,qCAE3I,qCAAmE,eAAkB,GAAY,OAEjG,iDACA,YAEA,MACA,oBACA,qBACA,gBACA,SAGA,qFAEA,wBACA,iBACA,mCACA,wCACA,kCACA,EACA,iCACA,gBACA,0CACA,mBAGA,uHAEA,KACA,CACA,yBACA,kCACA,oBACA,oBACA,cACA,6BACA,wCACA,+GAEA,GACA,GAAuB,QAAU,gDACjC,0BACA,iBACA,aACA,QAA4B,OAAS,EACrC,cAAkC,mGAAoM,KACtO,iEACA,gBAA0C,6BAAmC,GAC7E,MACA,sEACA,qBAA+C,kCAAwC,GACvF,MACA,+DACA,cAAwC,2BAAiC,GACzE,MACA,wEAAuK,yBAA+B,SACtM,iEACA,gBAA0C,6BAAmC,GAC7E,MACA,2FACA,CACA,eACA,yCAAyF,EAAS,GAClG,CACA,GACA,YACA,8DACA,kCACA,wFAEA,GACA,CACA,CACA,IACA,SAAoB,WAAc,MAAQ,GAAQ,qBAAwB,SAAS,yBACnF,qBACA,oBAAsC,mCAAqC,SRnSpE,GACP,IQkS2F,ERlS3F,GACA,GAAoB,sBACpB,MACA,SACA,EAIA,OAHA,wBAEA,aACA,UACA,EQyR2F,GAAa,6CACxG,CAAwB,sBAAwB,0DAChD,MACA,MAAuB,EACvB,CAAa,EACb,KACA,QAEA,2BACA,OACA,MAA4B,uBAA2B,CACvD,UAA+B,EAC/B,EAMA,OAJA,YACA,mCACA,yDAEA,CAAqB,qBAAsB,YAC3C,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,uBAA2B,SAE5D,QACA,CACA,CACA,iCACA,YAAkC,GAAY,gBAAkB,gBAAgB,iBAChF,gCACA,IACA,SAAoB,WAAc,MAAQ,GAAQ,qBAAwB,SAAS,yBACnF,qBACA,MACA,YACA,eACA,CAAiB,CACjB,MAAuB,EACvB,CAAa,EAEb,GADA,MAAkB,EAFqB,CAEN,gBAAkB,gBAAgB,iBACnE,EACA,QAEA,2BACA,OACA,MAA4B,yCAA+C,CAC3E,UAA+B,EAC/B,EAMA,OAJA,YACA,mCACA,yDAEA,CAAqB,mCAAoC,KAAW,4BAAsF,UAC1J,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,yCAA+C,SAEhF,QACA,CACA,CAKA,2BACA,IACA,YAAoB,6CAAgD,EAYpE,MAAoB,WAXpB,MAA8B,GAAQ,qBAAwB,SAAS,6BACvE,qBACA,MACA,WACA,WACA,eACA,QACA,sBAA4C,4CAC5C,CAAiB,CACjB,MAAuB,EACvB,CAAa,EAEb,KACA,MAJuC,CAId,MAAQ,uBAA2B,UAE5D,2BACA,OACA,MAA4B,uBAA2B,CACvD,UAA+B,EAC/B,EAMA,OAJA,YACA,MAJ4D,IAI5D,yBACA,yDAEA,CAAqB,eACrB,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,uBAA2B,SAE5D,QACA,CACA,CAkBA,uBACA,cACA,IACA,gBACA,UAAwB,aAAiB,EACzC,OACA,MACA,yBAEA,aAAiE,GAAyB,+BAE1F,UAAwB,GAAQ,MAAQ,GAAQ,qBAAwB,SAAS,OACjF,qBACA,MACA,QACA,yCAAuI,CACvI,2DACA,sBAAgD,4CAAuF,CACvI,iBACA,uBACA,CAAqB,CACrB,4CACiB,EACjB,OAAyB,MAAQ,uBAA2B,SAC5D,CACA,gBACA,UAAwB,aAAiB,EACzC,MAAwB,WAAc,MAAQ,GAAQ,qBAAwB,SAAS,OACvF,qBACA,MACA,QACA,yCAAuI,CACvI,2DACA,sBAAgD,4CAAuF,CACvI,kDACA,CAAqB,CACJ,EACjB,OAAyB,MAAQ,6DAAmG,SACpI,CACA,UAAsB,GAA2B,oDACjD,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,uBAA2B,SAE5D,QACA,CACA,CAIA,mBACA,QACA,QACA,EACA,CACA,iBACA,0CACA,6CAEA,SAAoB,WAAc,MAAQ,GAAQ,qBAAwB,SAAS,UACnF,qBACA,mCAAoD,KAAa,sBAAwB,iBAA+B,EACxH,aACA,MAAuB,EACvB,CAAa,EACb,KACA,QAEA,MACA,wDAEA,gBACA,SAKA,MAJA,kCACA,2BACA,wFAEA,CAAqB,WAAQ,YAAe,YAC5C,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,uBAA2B,SAE5D,QACA,CACA,CAeA,uBACA,UACA,IACA,WACA,OAKA,MAJA,wBAEA,aAA6D,GAAyB,+BAEtF,MAAyB,GAAQ,qBAAwB,SAAS,OAClE,6EAA8F,mBAA8B,0BAAiC,oBAAkC,iBAAwB,OAAY,uEAAyI,6DAC5W,CAAwB,sBAAwB,uCAChD,OAAgC,+DAAqG,EACrI,qBACA,MAAuB,EACvB,CAAa,CACb,CACA,OAHmC,CAGnC,CACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,kBAEzB,QACA,CACA,CAKA,uBAEA,OADA,6BACA,oCACA,6BAEA,CACA,wBACA,IACA,wCACA,IAAwB,cAAQ,EAAS,UAAyB,EAClE,KACA,QACA,MACA,UAA8B,GAC9B,UAAwB,GAAQ,MAAQ,CADa,EACL,oBAAuB,SAAS,kBAChF,qBACA,mBACiB,EACjB,OAAyB,MAAQ,uBAA2B,SAC5D,CAAa,CACb,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,uBAA2B,SAE5D,QACA,CACA,CAIA,gBACA,IACA,SAAgC,SAAS,SACzC,gBACA,UAAwB,oBAAuB,EAC/C,OAAwB,GAAQ,MAAQ,GAAQ,qBAChD,qBACA,MACA,QACA,OACA,sBAAgD,4CAAuF,CAClH,CACrB,4CACiB,EACjB,OAAyB,MAAQ,uBAA2B,SAC5D,CACA,gBACA,UAAwB,oBAAuB,EAC/C,MAAwB,WAAc,MAAQ,GAAQ,qBACtD,qBACA,MACA,QACA,OACA,sBAAgD,4CAAuF,CAClH,CACJ,EACjB,OAAyB,MAAQ,6DAAmG,SACpI,CACA,UAAsB,GAA2B,8DACjD,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,uBAA2B,SAE5D,QACA,CACA,CAYA,mBAOA,OANA,6BACA,oCACA,0BACA,GAIA,CAIA,wBACA,uCACA,IACA,sBACA,gCACA,gDACA,kBACA,aACA,QACA,UACA,CAAiB,GASjB,OARA,mCACA,IACA,OACA,CACA,SAEA,CACA,EAAiB,IACjB,CACA,CACA,+BAA2C,gBAAgB,cAC3D,6EACA,IACA,qBACA,UAWA,IAVA,mCACA,IACA,OACA,CACA,SAEA,CACA,EAAqB,IACrB,QAEA,4BACA,oCACA,eACA,qCACA,CACA,cACA,QACA,CACA,6EACA,oBACA,CACA,CAAa,CACb,QACA,CACA,kCACA,CACA,CAOA,qBACA,oCACA,IAEA,iCACA,iBACA,QACA,CACA,iCACA,CACA,CAMA,sBACA,wCACA,mBACA,kFAEA,IACA,WACA,QAAuC,GAAY,8BAWnD,GAVA,sDACA,WACA,wBACA,KAGA,iEACA,8BAGA,GACA,OAAyB,MAAQ,aAAe,aAOhD,sBACA,4BVz8BO,EUy8B2D,EAGlE,GADA,WAFkF,CAElF,iCAA0D,aAA0B,oCACpF,IACA,0BACA,qCAYA,EAXA,aACA,cACA,gBAEA,gXACA,KACA,IADwD,CACxD,8BAEA,GAFuE,KAEvE,WAEA,CAAqB,CAErB,CACA,OAAyB,MAAQ,UAAyB,YAC1D,CACA,YAAoB,WAAiB,8CACrC,KACA,OAAyB,MAAQ,aAAe,UAEhD,OAAqB,cAAQ,EAAS,YACtC,QACA,CACA,qCACA,CACA,CAQA,wBACA,EACA,wBAEA,6BACA,oCACA,uBAGA,CACA,kBACA,IACA,KACA,aAA6B,GAAQ,oBAAuB,SAAS,QACrE,qBACA,MACA,MAA2B,EAC3B,CAAiB,EAEjB,QAHwC,KAGxC,2BACA,UACA,SAAwB,WAAc,EACtC,KACA,cAGA,+EAGA,MAA6B,GAAQ,oBAAuB,SAAS,QACrE,qBACA,iEACA,MAA2B,EAC3B,CAAiB,EANjB,CAA6B,MAAQ,CAKG,IALH,KAAY,WAAa,EAAuB,CAOrF,CAAa,CACb,CACA,SACA,GAAgB,GAAW,EAV0D,CAiBrF,KAP2B,ETl/B3B,GSm/B6C,ITn/B7C,qCSs/BA,4BACA,MAA0B,GAAe,gBAAkB,gBAAgB,kBAE3E,CAAyB,MAAQ,UAAY,SAE7C,QACA,CACA,CAIA,uBAA6C,EAE7C,OADA,6BACA,oCACA,4BAEA,CACA,wBAA8C,EAC9C,IACA,wCACA,IAAwB,gBAAyC,EACjE,KACA,QAEA,cACA,UAA8B,GAE9B,gBACA,IAHqD,GAIrD,MACA,wCAEA,aAAiE,GAAyB,+BAE1F,SAAwB,WAAyB,MAAQ,GAAQ,oBAAuB,SAAS,QACjG,qBACA,4CACA,mCAAwD,KAAiB,yCAA2E,EACpJ,mBACA,MAA2B,EAC3B,CAAiB,EACjB,KACA,GAHwC,GAGxC,EAIA,OAHA,cACA,2BACA,mDACA,CAAyB,MAAQ,YAAoB,YACrD,CAAa,CACb,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,UAAY,SAE7C,QACA,CACA,CAMA,oBAEA,OADA,6BACA,oCACA,0BAEA,CACA,qBACA,IACA,qCACA,UAA0B,GAE1B,oBAFiD,CAGjD,IACA,KACA,OACA,SAAoB,GAAU,GAAW,gBAKzC,GAJA,OAEA,IADA,UACA,GAEA,GACA,IAAwB,mBAAmC,8CAC3D,KACA,OAA6B,MAAQ,uBAA2B,UAEhE,MACA,OAA6B,MAAQ,uBAA2B,aAEhE,GACA,KACA,CACA,SAAwB,WAAc,oCACtC,KACA,QAEA,GACA,4BACA,8BACA,YACA,oBACA,eACA,YACA,EACA,2BACA,+CACA,CACA,OAAqB,MAAQ,sBAA6B,YAC1D,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,uBAA2B,SAE5D,QACA,CACA,CAOA,wBAEA,OADA,6BACA,oCACA,8BAEA,CACA,yBACA,IACA,wCACA,MACA,OACA,SAA4B,WAAc,EAC1C,KACA,QAEA,8BACA,CACA,qCACA,UAA8B,GAE9B,YAAwB,QAF6B,CAE7B,EAAiB,qDACzC,EACA,CAA6B,MAAQ,uBAA2B,UAEhE,EAGA,CAAyB,MAAQ,sBAA6B,aAF9D,CAA6B,MAAQ,uBAA2B,YAGhE,CAAa,CACb,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,uBAA2B,SAE5D,QACA,CACA,CAIA,8BACA,IACA,IAAiB,KACjB,UAA0B,EADA,CAC8B,wBAExD,8CAGA,UAA0B,GAA8B,wEACxD,mCACA,qCACA,CAAiB,EAGjB,UACA,eACA,0BACA,UAAkC,GAA8B,8BAEhE,KACA,YACA,8BACA,UAAkC,GAA8B,uCAKhE,CAEA,eAEA,GADA,wDACA,QACA,UAA8B,GAA8B,qBAC5D,MAD4D,GACpC,WAAc,2CACtC,KACA,QACA,oCAGA,OAFA,8BACA,kEACA,CAAyB,MAAQ,oCAA2C,YAC5E,CACA,mBAAoB,kGAA2G,EAC/H,kBACA,UAA0B,GAA8B,6BAExD,iCACA,cACA,KACA,IACA,gBAEA,SACA,QVrvCO,CUqvCqC,IAC5C,yBADyE,mDACzE,EAA8F,EAAkB,gCAAgC,EAAU,IAE1J,SACA,UACA,sHAEA,OACA,mIAEA,SAAoB,WAAc,uBAClC,KACA,QACA,OACA,iBACA,yBACA,eACA,aACA,aACA,gBACA,aACA,aAKA,OAFA,wBACA,qEACA,CAAqB,cAAQ,sBAAoC,YACjE,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,+BAAmC,SAEpE,QACA,CACA,CAIA,4BACA,6CACA,CAIA,yBACA,YAA4C,GAAY,gBAAkB,gBAAgB,iBAC1F,mBACA,CASA,iBAA8B,eAAiB,EAE/C,OADA,6BACA,oCACA,uBAEA,CACA,sBAAqB,GAAQ,CAAI,eAAiB,EAClD,4CACA,EACA,SAAoB,WAA4B,EAChD,KACA,OAAyB,SAEzB,gDACA,MACA,IAAwB,SAAQ,8BAChC,MAGA,ET7yCA,GS6yCwC,IT7yCxC,OS6yCwC,UT7yCxC,QS8yCA,kDACA,aAAiC,EAGjC,CAKA,MAJA,eACA,4BACA,MAAsB,GAAe,gBAAkB,gBAAgB,kBAEvE,CAAqB,WACrB,CAAS,CACT,CAKA,qBACA,MP50CA,GO40CmB,YAAI,wBP50CvB,4BACA,yBACA,OADA,gBACA,YACA,CAAK,EO00CL,GACA,KACA,WACA,iBACA,wEACA,kCACA,CAAa,EAUb,OARA,oEACA,kCACA,WACA,6BACA,qCACA,2BACA,CAAa,CACb,EAAS,GACT,CAAiB,mBAAQ,GACzB,CACA,6BACA,wCACA,QACA,IACA,IAAwB,cAAQ,EAAS,UAAW,EACpD,KACA,OACA,0FACA,0DACA,CACA,SACA,4FACA,yDACA,gBACA,CACA,CAAS,CACT,CAQA,kCAAmD,EACnD,WACA,MACA,yBAEA,aAAyD,GAAyB,gCAClF,EAEA,IACA,aAAyB,GAAQ,qBAAwB,SAAS,WAClE,MACA,QACA,iBACA,wBACA,sBAA4C,6BAAqC,CAChE,CACjB,qBACA,wBACa,CACb,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,kBAEzB,QACA,CACA,CAIA,0BACA,MACA,IACA,SAAoB,WAAc,qBAClC,KACA,QACA,OAAqB,MAAQ,4CAA6E,YAC1G,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,kBAEzB,QACA,CACA,CAKA,sBACA,MACA,IACA,IAAoB,gBAAc,iCAClC,cACA,SAAwB,WAAc,EACtC,KACA,QACA,uCAA6D,SAAS,wCACtE,mDACA,2CACA,qDACA,sBACA,CAAiB,EACjB,aAA6B,GAAQ,oBACrC,qBACA,gEACA,CAAiB,CACjB,CAAa,EACb,KACA,QAIA,OAHgB,MAAS,oDACzB,6CAEA,CAAqB,MAAQ,6CAA2F,YACxH,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,6BAA2C,SAE5E,QACA,CACA,CAIA,wBACA,IACA,wCACA,QACA,SAAwB,WAAc,EACtC,KACA,QAEA,aAA6B,GAAQ,uBAA0B,SAAS,mBAAmB,cAAqB,GAChH,qBACA,gEACA,CAAiB,CACjB,CAAa,CACb,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,kBAEzB,QACA,CACA,CAKA,6BACA,8BAAkD,iBAA6B,MAC/E,uBACA,QPv0CO,IOw0CP,iBAEA,aAAyB,CP10ClB,EO00C2B,UAClC,KACA,MAA0B,GAAK,qBAE/B,CAFiE,GAEjE,kCACA,MAA6B,GAAQ,qBAAwB,SAAS,kCACtE,MAA4B,gBAA6B,CACzD,qBACA,MAA2B,EAC3B,CAAiB,GPn1CV,EOo1CM,QAF8B,IAG3C,oBACA,UACoB,GAAyB,IAE7C,eVhgDO,GUigDP,CAAa,CPz1Cb,KOw1CmE,OPx1CnE,QAGA,WACA,GOo1CgG,CPp1ChG,QAAkC,MAAoB,IACtD,IACA,iBACA,4BACA,IAGA,CACA,SACA,uBACA,IAGA,CAEA,EAAS,EACT,CAAK,EOs0CL,CACA,SAEA,GADA,yBACgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,MAAQ,uBAA2B,SAE5D,QACA,QACA,CACA,oBACA,CACA,CACA,mBAMA,MALA,oBACA,UACA,oBACA,qBACA,gBAEA,CACA,iCACA,uCAAqD,SAAS,eAC9D,wBACA,gBACA,0BACS,EAMT,OALA,yEAEY,MAAS,wBACrB,0BAEA,CAAiB,eAAQ,QAAe,YACxC,CAKA,2BACA,MACA,8BACA,uBACA,IACA,YAAyC,GAAY,8BAErD,GADA,wCACA,0BACA,sCACA,UACA,4BAEA,MACA,CACA,oDV9iDO,EU8iDkI,EAEzI,GADA,4BAAiD,aAAiC,yBAAyB,KAAiB,EAC5H,EACA,OAF2H,CAAC,CAE5H,mCACA,UAA4B,GAAQ,8CACpC,IACA,iBAC6B,GAAyB,KACtD,iBADsD,+DACtD,GACA,6BAGA,OAMA,+CAEA,CACA,SACA,yBACA,iBACA,MACA,QACA,CACA,oBACA,CACA,CACA,2BACA,QACA,MACA,UAAsB,GAGtB,oBAH6C,MAG7C,CACA,uCAEA,4BAAgD,iBAA6B,MAC7E,uBACA,IACA,4BAA0C,GAC1C,IAAoB,CAD8B,KAC9B,UAAc,kCAClC,KACA,QACA,cACA,UAA0B,EAC1B,qBADiD,GACjD,YACA,8DACA,OAA6B,8BAE7B,OADA,mCACA,CACA,CACA,SAEA,GADA,yBACgB,GAAW,IAC3B,IAD2B,EAC3B,CAAiC,sBAKjC,OAJA,GAA8C,IAC9C,kBAD8C,OAC9C,GAEA,gDACA,CACA,CAEA,MADA,+CACA,CACA,QACA,CACA,6BACA,oBACA,CACA,CACA,sCACA,gCAAoD,EAAM,GAC1D,uCAAgE,EAAU,GAC1E,IACA,0BACA,yCAAoD,YAAgB,EAEpE,SACA,8DACA,IACA,qBACA,CACA,SACA,SACA,CACA,CAAa,EAEb,GADA,qBACA,YACA,YAAgC,WAAmB,KACnD,mBAEA,YAEA,QACA,CACA,oBACA,CACA,CAKA,sBACA,iCAGA,kCACA,MAAc,GAAY,+BAC1B,CACA,uBACA,iCACA,MAAc,GAAe,8BAC7B,mDACA,CAOA,mCACA,mDACA,qCACA,oCACA,IACA,GAA4B,MAAS,kDACrC,gDAEA,CACA,SACA,4DACA,CACA,CAKA,0BACA,8BACA,oCACA,mDVnsDO,CUmsDgE,GACvE,0BADoG,GAEpG,+CAOA,UAGA,8DAIA,mBAKA,qBACA,6BACA,kCACA,CAAS,GACT,CAKA,yBACA,mCACA,6BACA,4BACA,GACA,gBAEA,CAuBA,yBACA,wCACA,8BACA,CASA,wBACA,wCACA,6BACA,CAIA,8BACA,gDACA,IACA,oCACA,IACA,iBACA,IACA,wCACA,IAAoC,cAAQ,EAAS,EAAI,EACzD,mDACA,qDAIA,sCVhyDO,GUgyD2F,EAClG,2BAD+H,YAC/H,2BAA+F,gBAAgB,sBAAsB,uBAAsF,EAC3N,GV/xDO,CU8xD2J,CAAC,CAEnK,YADkD,cAD4I,EAE9L,WAD6E,KAC7E,CAEA,CAAyB,CACzB,CACA,KANyN,CAMzN,CAN2N,CAM3N,CACA,yFACA,CACA,QACA,CACA,6CACA,CACA,CAAa,CACb,CACA,SACA,oCAAmD,GACnD,oBAD0E,2CAI1E,OAEA,CACA,CAMA,gCAEA,GADA,0CACA,CAAa,MAAS,+CAKtB,OAJA,uBAEA,wBAEA,GAEA,IACA,4EACA,yFAGA,mCACA,CACA,IAFmD,EAEnD,GACA,0CACA,CACA,CAIA,8BACA,+BAAoD,EAAqB,GACzE,0DACA,sCACA,uBAGA,yBAEA,IAKA,6BACA,qCACA,oDACA,yHAKA,gCACA,CAAiB,IAGjB,qCACA,uBACA,uBAGA,CAOA,gCACA,mBAAuC,sBAA6B,GAOpE,GANA,+BACA,sBAA0C,iCAAuC,GAEjF,2BACA,iBAAqC,6BAAmC,GAExE,wBACA,eAA+D,GAAyB,8BACxF,uBACA,kBAAmC,sBAAkC,EACrE,yBAA0C,sBAAwC,EACrE,EACb,oBACA,CACA,iCACA,yCACA,oBACA,CAIA,MAHA,wCACA,6BAAiD,sBAA4B,GAE7E,GAAkB,EAAI,GAAG,YAAoB,EAE7C,mBACA,IACA,wCACA,MACA,IAAwB,gBAAyC,SACjE,EACA,CAA6B,mBAE7B,MAA6B,GAAQ,uBAA0B,SAAS,WAAW,WAAgB,GACnG,qBACA,6DACiB,CACjB,CAAa,CACb,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,kBAEzB,QACA,CACA,CACA,iBACA,IACA,wCACA,QACA,IAAwB,gBAAyC,EACjE,KACA,OAA6B,mBAE7B,qBAA6C,sDAAoE,yBAAqC,eAAsB,CAAI,gBAAuB,EACvM,MAAwB,WAAc,MAAQ,GAAQ,qBAAwB,SAAS,WACvF,OACA,qBACA,6DACiB,SACjB,EACA,CAA6B,oBAE7B,2EACA,oCAA4D,QAAQ,gBAAkB,EAEtF,MAAyB,cACzB,CAAa,CACb,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,kBAEzB,QACA,CACA,CAIA,iBACA,sCACA,IACA,wCACA,MACA,IAA4B,gBAAyC,EACrE,KACA,OAAiC,mBAEjC,SAA4B,WAAc,MAAQ,GAAQ,qBAAwB,SAAS,WAAW,WAAgB,UACtH,MAAgC,uCAAqD,CACrF,qBACA,6DACqB,SACrB,EACA,CAAiC,oBAEjC,uCAA4D,mDAA6D,KACzH,6DACA,MAA6B,WAC7B,CAAiB,CACjB,CACA,SACA,GAAoB,GAAW,GAC/B,KAD+B,CAC/B,CAA6B,kBAE7B,QACA,CACA,CAAS,CACT,CAIA,oBACA,sCACA,IACA,wCACA,MACA,IAA4B,gBAAyC,SACrE,EACA,CAAiC,mBAEjC,MAAiC,GAAQ,qBAAwB,SAAS,WAAW,WAAgB,aACrG,MAAgC,kBAAyB,CACzD,qBACA,6DACqB,CACrB,CAAiB,CACjB,CACA,SACA,GAAoB,GAAW,GAC/B,KAD+B,CAC/B,CAA6B,kBAE7B,QACA,CACA,CAAS,CACT,CAIA,6BAGA,IAAgB,gBAA6C,uBAC7D,mBACA,CAAS,SACT,EACA,CAAqB,mBAErB,oBACA,oBACA,iBACA,YACS,CACT,CAIA,qBAEA,IAAgB,WAAQ,EAAM,UAAsB,qBACpD,KACA,OAAqB,mBAErB,qCACA,6DACA,8DACA,OACA,MACA,MACA,OACA,OACA,CAAa,CACb,UACA,CACA,CAIA,wCACA,qCACA,iCACA,QACA,IAAwB,cAAQ,EAAS,UAAyB,EAClE,KACA,OAA6B,mBAE7B,MACA,OACA,MAAgC,iEAAuE,CACvG,UACA,EAEA,YAAwB,GAAU,GAAW,gBAC7C,MACA,QACA,UAEA,QAMA,MALA,oFACA,UACA,WAGA,CAAyB,MAAQ,wDADjC,UACwF,YACxF,CAAa,EAEb,CACA,oBAAiC,QAAU,EAE3C,gCACA,MAIA,uCAEA,oBV3jEO,EU2jEkC,EAAQ,IV3jEjB,CU2jEiB,MALjD,SASA,SAAgB,WAAc,MAAQ,GAAQ,oBAAuB,SAAS,yBAC9E,qBACS,EACT,KACA,QAEA,8BACA,UAAsB,GAAmB,iBAMzC,GAJA,YACA,+BAGA,CADA,8BAEA,UAAsB,GAAmB,yCAEzC,QACA,CAKA,qBAAkC,QAAU,EAC5C,IACA,QACA,OACA,SAAwB,WAAc,wBACtC,iBACA,OAA6B,mBAE7B,yBAEA,WAAoB,6BAAmC,mBAAwC,EAAI,GAAW,OPp3DvG,EOs3DgB,MPr3DvB,MACA,iCAGA,MADA,2BAEA,+BOk3DA,WACA,iBACA,wDACA,UAAwB,GAAQ,sBAChC,KACA,QAGA,OACA,MACA,SACA,SACA,WACA,CAAqB,CACrB,UACA,CACA,CACA,MPh4DO,YACP,UACA,YACA,OACA,yBACA,MAAwB,eAAiB,CAEzC,aACA,OACA,aACA,mBACA,MAAwB,eACxB,CACA,SACA,gCACA,CACA,EOg3D0C,OAC1C,+BAEA,8CACA,SACA,EAGA,IADA,iCRr6DO,YACP,SAEA,OADA,SA7EO,KACP,YAAoB,WAAgB,MACpC,sBACA,sBAIA,2BAEA,GADA,8BACA,SACA,IACA,EACA,SA3CO,KACP,iBACA,KAGA,YACA,YACA,YACA,MACA,CACA,aACA,aACA,eACA,YACA,MACA,CACA,eACA,aACA,gBACA,eACA,YACA,MACA,CACA,+CAAuD,eAAuB,EAC9E,EAmBA,IACA,CACA,EA+DA,gBACA,iBACA,EQi6D0G,GAAI,EAAU,GAAG,EAAW,IAEtI,UAA0B,GAAmB,yBAG7C,OACA,MACA,SACA,SACA,WACA,CAAiB,CACjB,UACA,CACA,CACA,SACA,GAAgB,GAAW,GAC3B,KAD2B,CAC3B,CAAyB,kBAEzB,QACA,CACA,CACA,CACA,oBEzqEA,OADmB,EEEb,OAAO,GFFkB,MAChB,EECyB,GACtC,IAD8B,CFDP,EAAC,KEEZ,CAAkC,CADE,CAE9C,KAAK,CAAC,EACR,CAAC,CACF,GAFgB,CAAC,ECyBG,GAuCnB,WAvCiC,CAwCrB,CAAmB,CACnB,CAAmB,CAC7B,CAA2C,YAE3C,GAJU,gBAAW,CAAX,EACA,SADW,CAAQ,MACR,CAAX,EAGN,CAAC,EAAa,MAHG,CAAQ,EAGb,EAAiB,CAAC,0BAA0B,CAAC,CAC7D,GAAI,CAAC,EAAa,MAAM,GAAR,EAAiB,CAAC,0BAA0B,CAAC,CAG7D,IAAM,EAAU,IAAI,CAAP,EAAU,CjBnErB,SAA8B,CAAW,EAC7C,CiBkEuC,MjBlEhC,EAAI,CAAD,MADuB,CACd,CAAC,GAAG,CAAC,CAAC,CAAC,CAAO,CAAH,CAAC,CAAC,EAAU,CAC3C,CiBgE4C,GAGzC,KAAI,CAAC,EAH+C,CAAC,QAGrC,CAAG,IAAI,GAAG,CAAC,aAAa,CAAE,GAC1C,IADiD,CAAC,WAClC,CAAC,QAAQ,CAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAE,IAAI,CAAC,CAC3E,IAAI,CAAC,OAAO,CAAG,IAAI,GAAG,CAAC,SAAS,CAAE,GAClC,IADyC,CACpC,UAAU,CAAG,IAAI,GAAG,CAAC,YAAY,CAAE,GACxC,IAD+C,CAAC,YAC/B,CAAG,IAAI,GAAG,CAAC,cAAc,CAAE,GAG5C,IAHmD,CAAC,CAG1B,MAAM,EAAQ,KAAD,EAAhB,CAAyB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAQrE,EjB9EJ,MiB8EY,GjB9EF,CAM4B,CAC1C,CAAoC,QiBuEG,EjBrEvC,GAAM,CACJ,EAAE,CAAE,CAAS,CACb,IAAI,CAAE,CAAW,CACjB,QAAQ,CAAE,CAAe,CACzB,MAAM,CAAE,CAAa,CACtB,CAAG,EACE,CACJ,EAAE,CAAE,CAFK,CAGT,IAAI,CAAE,CAAoB,CAC1B,QAAQ,CAAE,CAAwB,CAClC,MAAM,CAAE,CAAsB,CAC/B,CAAG,EAEE,EAAsD,CAC1D,EAAE,CAHQ,OAGR,wBACG,GACA,GAEL,IAAI,EAFU,CACb,KAFsB,MAGnB,kBACC,GACA,GAEL,QAFgB,CACf,KAFwB,EAGjB,wBACH,GACA,GAEL,MAAM,MAFc,CACnB,CACK,IAHuB,EAGvB,gCACD,GACA,GAAa,CAChB,OAAO,EADS,MACT,wBACD,iBAAsB,OAAtB,EAAwB,SAAO,EAAI,EAAJ,CAAO,CACtC,KADsB,EACvB,EADC,MACA,EAAa,OAAb,EAAe,EAAF,GADS,EACP,EAAO,EAAT,CAAa,CAAE,CAAC,CAA7B,CAEP,CACD,WAHqB,CAGR,GAAS,CAAE,kDAAC,QAAE,QAAH,oQACzB,CASD,OAPI,EAAQ,KAAD,MAAY,CACrB,CADuB,CAChB,IAAD,OAAY,CAAG,EAAQ,KAAD,MAAY,CAGxC,OAAQ,EAAe,IAAD,OAAY,CAG7B,CACT,CAAC,IADc,GiByB2B,IAAW,EAAE,CAAN,CAN3C,CAM2C,CANzC,CAAE,GACJ,EAK2D,CAAC,GAAxB,EAL5B,CAAE,GACV,CAI2C,EANrB,CAElB,iBAD8B,KAC9B,UAAO,IAAoB,CAAE,UAAU,CAAE,CAAiB,EAAE,CAAjC,MACzB,CAAE,GACT,EAID,CANgE,GAM5D,CAAC,UAAU,CAAG,CALc,KAKd,KAAS,IAAI,CAAC,CAAN,SAAM,EAAU,EAAI,EAAJ,CAC1C,IAAI,CAAC,OAAO,CAAG,WAAS,MAAD,CAAQ,SAAO,EAAI,EAAE,CAEvC,EAAS,MAAD,KAAY,EAAE,IAOrB,CAAC,WAAW,CAAG,EAAS,MAAD,KAAY,CAEvC,IAAI,CAAC,IAAI,CAAG,IAAI,KAAK,CAAqB,EAAS,CAAE,CACnD,GAAG,CAAE,CAAC,CAAC,CAAE,IAAI,CACX,CADa,EAAE,GACT,KAAS,CACb,6GAA6G,MAAM,CACjH,GACD,CADK,CACL,eAAkB,CACpB,CACF,CACF,CAAC,EAhBF,IAAI,CAAC,IAAI,CAAG,IAAI,CAAC,uBAAuB,CACtC,WAAS,MAAD,EAAS,EAAJ,CACb,IAAI,CAAC,OAAO,CACZ,EAAS,MAAD,CAAQ,KAAK,CACtB,CAeH,IAAI,CAAC,KAAK,CAAG,GAAc,EAAa,IAAI,CAAC,GAAnB,CAAY,WAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,EAAS,MAAD,CAAQ,KAAK,CAAC,CAC/F,IAAI,CAAC,QAAQ,CAAG,IAAI,CAAC,mBAAmB,gBACtC,OAAO,CAAE,IAAI,CAAC,OAAO,CACrB,WAAW,CAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EACzC,EAAS,MAAD,EAAS,EACpB,CACF,IAAI,CAAC,IAAI,CAAG,IAAI,EAAgB,IAAI,GAAG,CAAC,KAAT,IAAkB,CAAE,GAAS,IAAI,CAAL,CACzD,OAAO,CAAE,IAAI,CAAC,OAAO,CACrB,MAAM,CAAE,EAAS,EAAE,CAAC,GAAJ,GAAU,CAC1B,KAAK,CAAE,IAAI,CAAC,KAAK,CAClB,CAAC,CAEE,EAAU,MAAD,KAAY,EAAE,IACrB,CAAC,oBAAoB,EAAE,CAE9B,IAKG,SAAS,GACX,OAAO,IAAI,EAAgB,IAAI,CAAC,QAAN,IAAkB,CAAC,IAAI,CAAE,CACjD,OAAO,CAAE,IAAI,CAAC,OAAO,CACrB,WAAW,CAAE,IAAI,CAAC,KAAK,CACxB,CACH,CAAC,IAKG,OAAO,GACT,OAAO,IAAI,GAAsB,IAAI,CAAC,KAAN,KAAgB,CAAC,IAAI,CAAE,IAAI,CAAC,OAAO,CAAE,IAAI,CAAC,KAAK,CAAC,CACjF,IAeG,CAAC,CAAgB,EACnB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EACxB,CAAC,KAD+B,CAAC,CAY/B,CAAqB,EAMrB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAgB,EACzC,CAAC,GA0BE,CACD,CAAU,CACV,EAAmB,EAAE,CACrB,EAII,EAAE,EAYN,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAI,EAAM,EACjC,CAAC,IADuC,CAAC,EAUlC,CAAC,CAAY,CAAE,EAA+B,CAAE,MAAM,CAAE,EAAE,CAAE,EACjE,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAM,EACrC,CAAC,CADwC,CAAC,SAM/B,GACT,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CACnC,aAQY,CAAC,CAAwB,EACpC,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,EACrC,CAAC,IAD2C,CAAC,YAM5B,GACf,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAG5B,eAAe,iEAC3B,GAAI,IAAI,CAAC,WAAW,CAClB,CADoB,MACb,MAAM,IAAI,CAAC,WAAW,EAAE,CAGjC,GAAM,MAAE,CAAI,CAAE,CAAG,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAE7C,OAAO,oBAAK,EAAD,KAAC,EAAO,SAAE,cAAY,EAAI,EAAJ,EAAQ,uQAC1C,uBAE8B,CAC7B,kBACE,CAAgB,gBAChB,CAAc,oBACd,CAAkB,SAClB,CAAO,CACP,YAAU,CACV,UAAQ,MACR,CAAI,OACJ,CAAK,CACqB,CAC5B,CAAgC,CAChC,CAAa,EAEb,IAAM,EAAc,CAClB,QADe,KACF,CAAE,UAAU,IAAI,CAAC,WAAW,EAAE,CAC3C,MAAM,CAAE,GAAG,IAAI,CAAC,WAAW,EAAE,CAC9B,CACD,OAAO,IAAI,GAAmB,CAC5B,GAAG,CAAE,IAAI,CAAC,KADiB,EACV,CAAC,IAAI,CACtB,OAAO,gCAAO,GAAgB,GAC9B,IADqC,CAAZ,KACf,CAAE,UAAU,SACtB,EACA,cADgB,sBAEhB,UACA,OAAO,CADW,GAElB,OACA,CADQ,GACJ,IACJ,KAAK,GACL,EAGA,GAHK,yBAGuB,CAAE,eAAe,GAAI,IAAI,CAAC,OAAO,CAC9D,CAAC,CAGI,mBAAmB,CAAC,CAA8B,EACxD,OAAO,IAAI,EAAe,IAAI,CAAC,OAAN,IAAiB,CAAC,IAAI,gCAC1C,GAAO,CACV,GADU,GACJ,eAAO,CAAE,MAAM,CAAE,IAAI,CAAC,WAAW,CAAE,OAAK,EAAO,OAAP,EAAS,KAAF,CAAQ,GAAf,CAC9C,CACH,KAFwD,eAI7B,GAI1B,OAHW,IAGA,CAHK,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAO,GAAF,EAC3C,EADoD,EAAE,CACjD,CADmD,kBAChC,CAAC,EAAO,GAAF,KAAU,OAAE,EAAO,OAAP,EAAS,KAAF,IAAP,GAAqB,CAAC,CACjE,CAEH,CAHqD,mBAK1B,CACzB,CAAsB,CACtB,CAA4B,CAC5B,CAAc,EAGZ,CAAW,iBAAiB,GAA3B,GAAyC,EAApC,YAA0B,CAAU,EAAW,CAAC,CAAjB,GACjC,CAAC,kBAAkB,GAAK,EAE5B,GAFiC,CAE7B,CAAC,kBAAkB,CAAG,EACP,GADY,SACA,EAAE,CAAxB,IACT,CADc,GACV,CAAC,QAAQ,CAAC,OAAO,EAAE,CACT,SAAS,EAAnB,GAAqB,GAAf,CAAmB,CAAC,IAAI,CAAC,OAAO,EAAE,CAC5C,IAAI,CAAC,kBAAkB,CAAG,OAE9B,CAAC,CAFsC,IC7T5B,GAAe,CAS1B,EACA,EACA,IAXuB,GASJ,CAIR,CADmC,EAAE,EACqB,EAAa,OAAf,CAAuB,CAAV,gBCtClF,8CACA,0BAA6C,UAC7C,EACA,qCAA6C,CAAE,SAAa,EAAC,IAC7D,IAAgD,EAAQ,IAAyB,GACjF,IAAiD,EAAQ,EAA0B,EAD5B,CAEvD,EAAoB,EAAQ,IAAa,CAWzC,GAZwD,IAYxD,EAWA,GAtB2B,SAsB3B,WAAuB,IAAY,mBAAmB,EAAI,EAC1D,WACA,2CAAqD,uBACrD,kBACA,YACA,CAMA,QACA,iBAA+B,SAAS,GAAG,EAAS,GACpD,wBACA,wBAAqC,eACrC,uBACA,iBACS,CACT,CAQA,UACA,uBACA,qBACA,SACA,gBACA,CAAS,CACT,CAwBA,UAAqB,OAAI,uBAAoC,EAAI,EAEjE,IADA,EAEA,EADA,aAA+B,SAAS,OAAO,EAAG,EAElD,OACA,iBACA,kBAGA,4BAEA,mCAAwE,EAAE,aAAiB,KAAO,EAAM,IACxG,kBACA,0BACA,CAAa,IAGb,SACA,KAEA,sBAAwC,eAIxC,OAHA,GACA,mBAAyC,EAAM,GAE/C,eACA,SACA,MACA,UACA,uBACA,OACA,iBACA,aACA,CAAS,CACT,CACA,CACA,SAAe,wGC3Gf,MAVA,WAIA,4BAAuC,YACvC,8BAAyC,cACzC,QAAqB,IAAN,GAAM,CAAoB,OAAO,GAAM,OACtD,uCACA,IAIO,cAEP,EAAe,gBAER,UAF6C,CAE7C,CACA,YACA,mCCpBP,8CACA,0BAA6C,UAC7C,EACA,qCAA6C,CAAE,SAAa,EAC5D,QAA2C,EAAQ,GAAoB,EACvE,YADkD,KAClD,UAUA,UAEA,SACA,kBACA,UACA,OACA,iBACA,IAEA,SACA,OAEA,IAEA,SAMA,OALA,sCACA,qBACA,2BAEA,6CACA,KAoBA,SAAoB,gEAA8E,EAAI,EACtG,WAAyC,EAAgB,gBACzD,+BAEA,OADA,+BAA0C,KAAmB,EAAc,MAAQ,EAAE,EAAO,GAAG,eAA2B,EAAE,2CAA0E,GACtM,KAYA,SAAmB,oCAAgD,EAAI,EACvE,4BAA0E,EAAgB,QAE1F,OADA,+BAA0C,EAAM,GAChD,KAiBA,wBAAsB,uBAAgD,EAAI,EAC1E,6BAAiF,EAAgB,SACjG,wBAA+E,EAAgB,QAI/F,OAHA,+BAAgD,EAAK,GAErD,+BAA+C,MAAc,GAC7D,KAOA,eAEA,OADA,cACA,KAQA,SAEA,OADA,wDACA,KAQA,cAUA,MAPA,oBACA,uCAGA,wDAEA,sBACA,KAKA,MAEA,OADA,+BACA,KAKA,UAEA,OADA,2CACA,KA2BA,iBAAc,uEAAqG,EAAI,EACvH,MACA,OACA,iBACA,iBACA,kBACA,iBACA,aACA,CACA,gBACA,UAEA,4DACA,kDAA+D,IAAS,OAAO,EAAa,GAAG,UAAU,GAAS,EAElH,KASA,WACA,MAOA,MANA,qDACA,oCAGA,kCAEA,KAQA,UACA,WACA,CACA,CACA,SAAe,iBC3Nf,qCAA6C,CAAE,SAAa,CAM5D,CAN6D,MAM7D,gBACA,eACA,iBACA,2BACA,uBACA,iBACA,iBAEA,CACA,SAAe,mBCff,qCAA6C,CAAE,SAAa,EAAC,EAC7D,eAAuB,QACvB,MAAkB,EAAQ,GAAW,EACrC,YADyB,KACF,EAAK,gCAAiC,UAAkB,aCF/E,qBACA,YACA,wFAGA,wBCNA,8CACA,0BAA6C,UAC7C,EACA,qCAA6C,CAAE,SAAa,EAC5D,QAAiD,EAAQ,EAA0B,EACnF,SACA,IAFwD,QAExD,WAAuB,IAAY,kBAAkB,EACrD,WACA,eACA,cACA,YACA,CAsBA,eAAsB,cAAuB,EAAI,EAGjD,SACA,kBACA,UACA,OACA,iBACA,IAEA,SACA,OAEA,IAEA,SAKA,OAJA,sCACA,GACA,8BAA8C,GAAM,EAEpD,eACA,OApBA,eAqBA,aACA,qBACA,mBACA,iBACA,aACA,CAAS,CACT,CA2BA,gBAAqB,sBAA+B,EAAI,EAExD,SAWA,GAVA,qBACA,4BAEA,GACA,gBAAyC,EAAM,GAE/C,GACA,0BAEA,gCACA,kBACA,mDACA,eACA,iCAAgF,EAAO,IACvF,gDACA,CACA,CACA,sBACA,OApBA,OAqBA,aACA,qBACA,mBACA,OACA,iBACA,aACA,CAAS,CACT,CAuCA,qBAAqB,oDAAqE,EAAI,EAE9F,qBAA8C,mBAAsC,cAapF,GAZA,YACA,2CACA,qBACA,4BAEA,GACA,gBAAyC,EAAM,GAE/C,GACA,0BAEA,gCACA,kBACA,mDACA,eACA,iCAAgF,EAAO,IACvF,gDACA,CACA,CACA,sBACA,OAtBA,OAuBA,aACA,qBACA,mBACA,OACA,iBACA,aACA,CAAS,CACT,CAsBA,gBAAqB,GAAS,EAAI,EAElC,SAQA,OAPA,qBACA,4BAEA,GACA,gBAAyC,EAAM,GAE/C,gCACA,eACA,OAVA,QAWA,aACA,qBACA,mBACA,OACA,iBACA,aACA,CAAS,CACT,CAoBA,cAAa,GAAS,EAAI,EAE1B,SAQA,OAPA,GACA,gBAAyC,EAAM,GAE/C,qBACA,+BAEA,gCACA,eACA,OAVA,SAWA,aACA,qBACA,mBACA,iBACA,aACA,CAAS,CACT,CACA,CACA,SAAe", "sources": ["webpack://_N_E/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js", "webpack://_N_E/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js", "webpack://_N_E/./node_modules/@supabase/postgrest-js/dist/cjs/index.js", "webpack://_N_E/./node_modules/@supabase/postgrest-js/dist/cjs/version.js", "webpack://_N_E/./node_modules/@supabase/functions-js/dist/module/helper.js", "webpack://_N_E/./node_modules/@supabase/functions-js/dist/module/types.js", "webpack://_N_E/./node_modules/@supabase/functions-js/dist/module/FunctionsClient.js", "webpack://_N_E/./node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs", "webpack://_N_E/./node_modules/@supabase/realtime-js/dist/module/WebSocket.js", "webpack://_N_E/./node_modules/@supabase/realtime-js/dist/module/lib/version.js", "webpack://_N_E/./node_modules/@supabase/realtime-js/dist/module/lib/constants.js", "webpack://_N_E/./node_modules/@supabase/realtime-js/dist/module/lib/serializer.js", "webpack://_N_E/./node_modules/@supabase/realtime-js/dist/module/lib/timer.js", "webpack://_N_E/./node_modules/@supabase/realtime-js/dist/module/lib/transformers.js", "webpack://_N_E/./node_modules/@supabase/realtime-js/dist/module/lib/push.js", "webpack://_N_E/./node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js", "webpack://_N_E/./node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js", "webpack://_N_E/./node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js", "webpack://_N_E/./node_modules/@supabase/realtime-js/dist/module/index.js", "webpack://_N_E/./node_modules/@supabase/storage-js/dist/module/lib/errors.js", "webpack://_N_E/./node_modules/@supabase/storage-js/dist/module/lib/helpers.js", "webpack://_N_E/./node_modules/@supabase/storage-js/dist/module/lib/fetch.js", "webpack://_N_E/./node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js", "webpack://_N_E/./node_modules/@supabase/storage-js/dist/module/lib/version.js", "webpack://_N_E/./node_modules/@supabase/storage-js/dist/module/lib/constants.js", "webpack://_N_E/./node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js", "webpack://_N_E/./node_modules/@supabase/storage-js/dist/module/StorageClient.js", "webpack://_N_E/../../src/lib/version.ts", "webpack://_N_E/../../src/lib/constants.ts", "webpack://_N_E/../../src/lib/fetch.ts", "webpack://_N_E/../../src/lib/helpers.ts", "webpack://_N_E/./node_modules/@supabase/auth-js/dist/module/lib/version.js", "webpack://_N_E/./node_modules/@supabase/auth-js/dist/module/lib/constants.js", "webpack://_N_E/./node_modules/@supabase/auth-js/dist/module/lib/errors.js", "webpack://_N_E/./node_modules/@supabase/auth-js/dist/module/lib/base64url.js", "webpack://_N_E/./node_modules/@supabase/auth-js/dist/module/lib/helpers.js", "webpack://_N_E/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js", "webpack://_N_E/./node_modules/@supabase/auth-js/dist/module/lib/types.js", "webpack://_N_E/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js", "webpack://_N_E/./node_modules/@supabase/auth-js/dist/module/lib/local-storage.js", "webpack://_N_E/./node_modules/@supabase/auth-js/dist/module/lib/polyfills.js", "webpack://_N_E/./node_modules/@supabase/auth-js/dist/module/lib/locks.js", "webpack://_N_E/./node_modules/@supabase/auth-js/dist/module/GoTrueClient.js", "webpack://_N_E/./node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js", "webpack://_N_E/./node_modules/@supabase/auth-js/dist/module/AuthClient.js", "webpack://_N_E/./node_modules/@supabase/auth-js/dist/module/index.js", "webpack://_N_E/../../src/lib/SupabaseAuthClient.ts", "webpack://_N_E/../../src/SupabaseClient.ts", "webpack://_N_E/../../src/index.ts", "webpack://_N_E/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js", "webpack://_N_E/./node_modules/@supabase/node-fetch/browser.js", "webpack://_N_E/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js", "webpack://_N_E/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js", "webpack://_N_E/./node_modules/@supabase/postgrest-js/dist/cjs/constants.js", "webpack://_N_E/./node_modules/ws/browser.js", "webpack://_N_E/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst PostgrestTransformBuilder_1 = __importDefault(require(\"./PostgrestTransformBuilder\"));\nclass PostgrestFilterBuilder extends PostgrestTransformBuilder_1.default {\n    /**\n     * Match only rows where `column` is equal to `value`.\n     *\n     * To check if the value of `column` is NULL, you should use `.is()` instead.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    eq(column, value) {\n        this.url.searchParams.append(column, `eq.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is not equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    neq(column, value) {\n        this.url.searchParams.append(column, `neq.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is greater than `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    gt(column, value) {\n        this.url.searchParams.append(column, `gt.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is greater than or equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    gte(column, value) {\n        this.url.searchParams.append(column, `gte.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is less than `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    lt(column, value) {\n        this.url.searchParams.append(column, `lt.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is less than or equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    lte(column, value) {\n        this.url.searchParams.append(column, `lte.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches `pattern` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param pattern - The pattern to match with\n     */\n    like(column, pattern) {\n        this.url.searchParams.append(column, `like.${pattern}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches all of `patterns` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    likeAllOf(column, patterns) {\n        this.url.searchParams.append(column, `like(all).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches any of `patterns` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    likeAnyOf(column, patterns) {\n        this.url.searchParams.append(column, `like(any).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches `pattern` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param pattern - The pattern to match with\n     */\n    ilike(column, pattern) {\n        this.url.searchParams.append(column, `ilike.${pattern}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches all of `patterns` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    ilikeAllOf(column, patterns) {\n        this.url.searchParams.append(column, `ilike(all).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches any of `patterns` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    ilikeAnyOf(column, patterns) {\n        this.url.searchParams.append(column, `ilike(any).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` IS `value`.\n     *\n     * For non-boolean columns, this is only relevant for checking if the value of\n     * `column` is NULL by setting `value` to `null`.\n     *\n     * For boolean columns, you can also set `value` to `true` or `false` and it\n     * will behave the same way as `.eq()`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    is(column, value) {\n        this.url.searchParams.append(column, `is.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is included in the `values` array.\n     *\n     * @param column - The column to filter on\n     * @param values - The values array to filter with\n     */\n    in(column, values) {\n        const cleanedValues = Array.from(new Set(values))\n            .map((s) => {\n            // handle postgrest reserved characters\n            // https://postgrest.org/en/v7.0.0/api.html#reserved-characters\n            if (typeof s === 'string' && new RegExp('[,()]').test(s))\n                return `\"${s}\"`;\n            else\n                return `${s}`;\n        })\n            .join(',');\n        this.url.searchParams.append(column, `in.(${cleanedValues})`);\n        return this;\n    }\n    /**\n     * Only relevant for jsonb, array, and range columns. Match only rows where\n     * `column` contains every element appearing in `value`.\n     *\n     * @param column - The jsonb, array, or range column to filter on\n     * @param value - The jsonb, array, or range value to filter with\n     */\n    contains(column, value) {\n        if (typeof value === 'string') {\n            // range types can be inclusive '[', ']' or exclusive '(', ')' so just\n            // keep it simple and accept a string\n            this.url.searchParams.append(column, `cs.${value}`);\n        }\n        else if (Array.isArray(value)) {\n            // array\n            this.url.searchParams.append(column, `cs.{${value.join(',')}}`);\n        }\n        else {\n            // json\n            this.url.searchParams.append(column, `cs.${JSON.stringify(value)}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for jsonb, array, and range columns. Match only rows where\n     * every element appearing in `column` is contained by `value`.\n     *\n     * @param column - The jsonb, array, or range column to filter on\n     * @param value - The jsonb, array, or range value to filter with\n     */\n    containedBy(column, value) {\n        if (typeof value === 'string') {\n            // range\n            this.url.searchParams.append(column, `cd.${value}`);\n        }\n        else if (Array.isArray(value)) {\n            // array\n            this.url.searchParams.append(column, `cd.{${value.join(',')}}`);\n        }\n        else {\n            // json\n            this.url.searchParams.append(column, `cd.${JSON.stringify(value)}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is greater than any element in `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeGt(column, range) {\n        this.url.searchParams.append(column, `sr.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is either contained in `range` or greater than any element in\n     * `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeGte(column, range) {\n        this.url.searchParams.append(column, `nxl.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is less than any element in `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeLt(column, range) {\n        this.url.searchParams.append(column, `sl.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is either contained in `range` or less than any element in\n     * `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeLte(column, range) {\n        this.url.searchParams.append(column, `nxr.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where `column` is\n     * mutually exclusive to `range` and there can be no element between the two\n     * ranges.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeAdjacent(column, range) {\n        this.url.searchParams.append(column, `adj.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for array and range columns. Match only rows where\n     * `column` and `value` have an element in common.\n     *\n     * @param column - The array or range column to filter on\n     * @param value - The array or range value to filter with\n     */\n    overlaps(column, value) {\n        if (typeof value === 'string') {\n            // range\n            this.url.searchParams.append(column, `ov.${value}`);\n        }\n        else {\n            // array\n            this.url.searchParams.append(column, `ov.{${value.join(',')}}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for text and tsvector columns. Match only rows where\n     * `column` matches the query string in `query`.\n     *\n     * @param column - The text or tsvector column to filter on\n     * @param query - The query text to match with\n     * @param options - Named parameters\n     * @param options.config - The text search configuration to use\n     * @param options.type - Change how the `query` text is interpreted\n     */\n    textSearch(column, query, { config, type } = {}) {\n        let typePart = '';\n        if (type === 'plain') {\n            typePart = 'pl';\n        }\n        else if (type === 'phrase') {\n            typePart = 'ph';\n        }\n        else if (type === 'websearch') {\n            typePart = 'w';\n        }\n        const configPart = config === undefined ? '' : `(${config})`;\n        this.url.searchParams.append(column, `${typePart}fts${configPart}.${query}`);\n        return this;\n    }\n    /**\n     * Match only rows where each column in `query` keys is equal to its\n     * associated value. Shorthand for multiple `.eq()`s.\n     *\n     * @param query - The object to filter with, with column names as keys mapped\n     * to their filter values\n     */\n    match(query) {\n        Object.entries(query).forEach(([column, value]) => {\n            this.url.searchParams.append(column, `eq.${value}`);\n        });\n        return this;\n    }\n    /**\n     * Match only rows which doesn't satisfy the filter.\n     *\n     * Unlike most filters, `opearator` and `value` are used as-is and need to\n     * follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure they are properly sanitized.\n     *\n     * @param column - The column to filter on\n     * @param operator - The operator to be negated to filter with, following\n     * PostgREST syntax\n     * @param value - The value to filter with, following PostgREST syntax\n     */\n    not(column, operator, value) {\n        this.url.searchParams.append(column, `not.${operator}.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows which satisfy at least one of the filters.\n     *\n     * Unlike most filters, `filters` is used as-is and needs to follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure it's properly sanitized.\n     *\n     * It's currently not possible to do an `.or()` filter across multiple tables.\n     *\n     * @param filters - The filters to use, following PostgREST syntax\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to filter on referenced tables\n     * instead of the parent table\n     * @param options.foreignTable - Deprecated, use `referencedTable` instead\n     */\n    or(filters, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = referencedTable ? `${referencedTable}.or` : 'or';\n        this.url.searchParams.append(key, `(${filters})`);\n        return this;\n    }\n    /**\n     * Match only rows which satisfy the filter. This is an escape hatch - you\n     * should use the specific filter methods wherever possible.\n     *\n     * Unlike most filters, `opearator` and `value` are used as-is and need to\n     * follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure they are properly sanitized.\n     *\n     * @param column - The column to filter on\n     * @param operator - The operator to filter with, following PostgREST syntax\n     * @param value - The value to filter with, following PostgREST syntax\n     */\n    filter(column, operator, value) {\n        this.url.searchParams.append(column, `${operator}.${value}`);\n        return this;\n    }\n}\nexports.default = PostgrestFilterBuilder;\n//# sourceMappingURL=PostgrestFilterBuilder.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\n// @ts-ignore\nconst node_fetch_1 = __importDefault(require(\"@supabase/node-fetch\"));\nconst PostgrestError_1 = __importDefault(require(\"./PostgrestError\"));\nclass PostgrestBuilder {\n    constructor(builder) {\n        this.shouldThrowOnError = false;\n        this.method = builder.method;\n        this.url = builder.url;\n        this.headers = builder.headers;\n        this.schema = builder.schema;\n        this.body = builder.body;\n        this.shouldThrowOnError = builder.shouldThrowOnError;\n        this.signal = builder.signal;\n        this.isMaybeSingle = builder.isMaybeSingle;\n        if (builder.fetch) {\n            this.fetch = builder.fetch;\n        }\n        else if (typeof fetch === 'undefined') {\n            this.fetch = node_fetch_1.default;\n        }\n        else {\n            this.fetch = fetch;\n        }\n    }\n    /**\n     * If there's an error with the query, throwOnError will reject the promise by\n     * throwing the error instead of returning it as part of a successful response.\n     *\n     * {@link https://github.com/supabase/supabase-js/issues/92}\n     */\n    throwOnError() {\n        this.shouldThrowOnError = true;\n        return this;\n    }\n    /**\n     * Set an HTTP header for the request.\n     */\n    setHeader(name, value) {\n        this.headers = Object.assign({}, this.headers);\n        this.headers[name] = value;\n        return this;\n    }\n    then(onfulfilled, onrejected) {\n        // https://postgrest.org/en/stable/api.html#switching-schemas\n        if (this.schema === undefined) {\n            // skip\n        }\n        else if (['GET', 'HEAD'].includes(this.method)) {\n            this.headers['Accept-Profile'] = this.schema;\n        }\n        else {\n            this.headers['Content-Profile'] = this.schema;\n        }\n        if (this.method !== 'GET' && this.method !== 'HEAD') {\n            this.headers['Content-Type'] = 'application/json';\n        }\n        // NOTE: Invoke w/o `this` to avoid illegal invocation error.\n        // https://github.com/supabase/postgrest-js/pull/247\n        const _fetch = this.fetch;\n        let res = _fetch(this.url.toString(), {\n            method: this.method,\n            headers: this.headers,\n            body: JSON.stringify(this.body),\n            signal: this.signal,\n        }).then(async (res) => {\n            var _a, _b, _c;\n            let error = null;\n            let data = null;\n            let count = null;\n            let status = res.status;\n            let statusText = res.statusText;\n            if (res.ok) {\n                if (this.method !== 'HEAD') {\n                    const body = await res.text();\n                    if (body === '') {\n                        // Prefer: return=minimal\n                    }\n                    else if (this.headers['Accept'] === 'text/csv') {\n                        data = body;\n                    }\n                    else if (this.headers['Accept'] &&\n                        this.headers['Accept'].includes('application/vnd.pgrst.plan+text')) {\n                        data = body;\n                    }\n                    else {\n                        data = JSON.parse(body);\n                    }\n                }\n                const countHeader = (_a = this.headers['Prefer']) === null || _a === void 0 ? void 0 : _a.match(/count=(exact|planned|estimated)/);\n                const contentRange = (_b = res.headers.get('content-range')) === null || _b === void 0 ? void 0 : _b.split('/');\n                if (countHeader && contentRange && contentRange.length > 1) {\n                    count = parseInt(contentRange[1]);\n                }\n                // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n                // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n                if (this.isMaybeSingle && this.method === 'GET' && Array.isArray(data)) {\n                    if (data.length > 1) {\n                        error = {\n                            // https://github.com/PostgREST/postgrest/blob/a867d79c42419af16c18c3fb019eba8df992626f/src/PostgREST/Error.hs#L553\n                            code: 'PGRST116',\n                            details: `Results contain ${data.length} rows, application/vnd.pgrst.object+json requires 1 row`,\n                            hint: null,\n                            message: 'JSON object requested, multiple (or no) rows returned',\n                        };\n                        data = null;\n                        count = null;\n                        status = 406;\n                        statusText = 'Not Acceptable';\n                    }\n                    else if (data.length === 1) {\n                        data = data[0];\n                    }\n                    else {\n                        data = null;\n                    }\n                }\n            }\n            else {\n                const body = await res.text();\n                try {\n                    error = JSON.parse(body);\n                    // Workaround for https://github.com/supabase/postgrest-js/issues/295\n                    if (Array.isArray(error) && res.status === 404) {\n                        data = [];\n                        error = null;\n                        status = 200;\n                        statusText = 'OK';\n                    }\n                }\n                catch (_d) {\n                    // Workaround for https://github.com/supabase/postgrest-js/issues/295\n                    if (res.status === 404 && body === '') {\n                        status = 204;\n                        statusText = 'No Content';\n                    }\n                    else {\n                        error = {\n                            message: body,\n                        };\n                    }\n                }\n                if (error && this.isMaybeSingle && ((_c = error === null || error === void 0 ? void 0 : error.details) === null || _c === void 0 ? void 0 : _c.includes('0 rows'))) {\n                    error = null;\n                    status = 200;\n                    statusText = 'OK';\n                }\n                if (error && this.shouldThrowOnError) {\n                    throw new PostgrestError_1.default(error);\n                }\n            }\n            const postgrestResponse = {\n                error,\n                data,\n                count,\n                status,\n                statusText,\n            };\n            return postgrestResponse;\n        });\n        if (!this.shouldThrowOnError) {\n            res = res.catch((fetchError) => {\n                var _a, _b, _c;\n                return ({\n                    error: {\n                        message: `${(_a = fetchError === null || fetchError === void 0 ? void 0 : fetchError.name) !== null && _a !== void 0 ? _a : 'FetchError'}: ${fetchError === null || fetchError === void 0 ? void 0 : fetchError.message}`,\n                        details: `${(_b = fetchError === null || fetchError === void 0 ? void 0 : fetchError.stack) !== null && _b !== void 0 ? _b : ''}`,\n                        hint: '',\n                        code: `${(_c = fetchError === null || fetchError === void 0 ? void 0 : fetchError.code) !== null && _c !== void 0 ? _c : ''}`,\n                    },\n                    data: null,\n                    count: null,\n                    status: 0,\n                    statusText: '',\n                });\n            });\n        }\n        return res.then(onfulfilled, onrejected);\n    }\n    /**\n     * Override the type of the returned `data`.\n     *\n     * @typeParam NewResult - The new result type to override with\n     * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n     */\n    returns() {\n        /* istanbul ignore next */\n        return this;\n    }\n    /**\n     * Override the type of the returned `data` field in the response.\n     *\n     * @typeParam NewResult - The new type to cast the response data to\n     * @typeParam Options - Optional type configuration (defaults to { merge: true })\n     * @typeParam Options.merge - When true, merges the new type with existing return type. When false, replaces the existing types entirely (defaults to true)\n     * @example\n     * ```typescript\n     * // Merge with existing types (default behavior)\n     * const query = supabase\n     *   .from('users')\n     *   .select()\n     *   .overrideTypes<{ custom_field: string }>()\n     *\n     * // Replace existing types completely\n     * const replaceQuery = supabase\n     *   .from('users')\n     *   .select()\n     *   .overrideTypes<{ id: number; name: string }, { merge: false }>()\n     * ```\n     * @returns A PostgrestBuilder instance with the new type\n     */\n    overrideTypes() {\n        return this;\n    }\n}\nexports.default = PostgrestBuilder;\n//# sourceMappingURL=PostgrestBuilder.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.PostgrestError = exports.PostgrestBuilder = exports.PostgrestTransformBuilder = exports.PostgrestFilterBuilder = exports.PostgrestQueryBuilder = exports.PostgrestClient = void 0;\n// Always update wrapper.mjs when updating this file.\nconst PostgrestClient_1 = __importDefault(require(\"./PostgrestClient\"));\nexports.PostgrestClient = PostgrestClient_1.default;\nconst PostgrestQueryBuilder_1 = __importDefault(require(\"./PostgrestQueryBuilder\"));\nexports.PostgrestQueryBuilder = PostgrestQueryBuilder_1.default;\nconst PostgrestFilterBuilder_1 = __importDefault(require(\"./PostgrestFilterBuilder\"));\nexports.PostgrestFilterBuilder = PostgrestFilterBuilder_1.default;\nconst PostgrestTransformBuilder_1 = __importDefault(require(\"./PostgrestTransformBuilder\"));\nexports.PostgrestTransformBuilder = PostgrestTransformBuilder_1.default;\nconst PostgrestBuilder_1 = __importDefault(require(\"./PostgrestBuilder\"));\nexports.PostgrestBuilder = PostgrestBuilder_1.default;\nconst PostgrestError_1 = __importDefault(require(\"./PostgrestError\"));\nexports.PostgrestError = PostgrestError_1.default;\nexports.default = {\n    PostgrestClient: PostgrestClient_1.default,\n    PostgrestQueryBuilder: PostgrestQueryBuilder_1.default,\n    PostgrestFilterBuilder: PostgrestFilterBuilder_1.default,\n    PostgrestTransformBuilder: PostgrestTransformBuilder_1.default,\n    PostgrestBuilder: PostgrestBuilder_1.default,\n    PostgrestError: PostgrestError_1.default,\n};\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.version = void 0;\nexports.version = '0.0.0-automated';\n//# sourceMappingURL=version.js.map", "export const resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => import('@supabase/node-fetch').then(({ default: fetch }) => fetch(...args));\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\n//# sourceMappingURL=helper.js.map", "export class FunctionsError extends Error {\n    constructor(message, name = 'FunctionsError', context) {\n        super(message);\n        this.name = name;\n        this.context = context;\n    }\n}\nexport class FunctionsFetchError extends FunctionsError {\n    constructor(context) {\n        super('Failed to send a request to the Edge Function', 'FunctionsFetchError', context);\n    }\n}\nexport class FunctionsRelayError extends FunctionsError {\n    constructor(context) {\n        super('Relay Error invoking the Edge Function', 'FunctionsRelayError', context);\n    }\n}\nexport class FunctionsHttpError extends FunctionsError {\n    constructor(context) {\n        super('Edge Function returned a non-2xx status code', 'FunctionsHttpError', context);\n    }\n}\n// Define the enum for the 'region' property\nexport var FunctionRegion;\n(function (FunctionRegion) {\n    FunctionRegion[\"Any\"] = \"any\";\n    FunctionRegion[\"ApNortheast1\"] = \"ap-northeast-1\";\n    FunctionRegion[\"ApNortheast2\"] = \"ap-northeast-2\";\n    FunctionRegion[\"ApSouth1\"] = \"ap-south-1\";\n    FunctionRegion[\"ApSoutheast1\"] = \"ap-southeast-1\";\n    FunctionRegion[\"ApSoutheast2\"] = \"ap-southeast-2\";\n    FunctionRegion[\"CaCentral1\"] = \"ca-central-1\";\n    FunctionRegion[\"EuCentral1\"] = \"eu-central-1\";\n    FunctionRegion[\"EuWest1\"] = \"eu-west-1\";\n    FunctionRegion[\"EuWest2\"] = \"eu-west-2\";\n    FunctionRegion[\"EuWest3\"] = \"eu-west-3\";\n    FunctionRegion[\"SaEast1\"] = \"sa-east-1\";\n    FunctionRegion[\"UsEast1\"] = \"us-east-1\";\n    FunctionRegion[\"UsWest1\"] = \"us-west-1\";\n    FunctionRegion[\"UsWest2\"] = \"us-west-2\";\n})(FunctionRegion || (FunctionRegion = {}));\n//# sourceMappingURL=types.js.map", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport { resolveFetch } from './helper';\nimport { FunctionsFetchError, FunctionsHttpError, FunctionsRelayError, FunctionRegion, } from './types';\nexport class FunctionsClient {\n    constructor(url, { headers = {}, customFetch, region = FunctionRegion.Any, } = {}) {\n        this.url = url;\n        this.headers = headers;\n        this.region = region;\n        this.fetch = resolveFetch(customFetch);\n    }\n    /**\n     * Updates the authorization header\n     * @param token - the new jwt token sent in the authorisation header\n     */\n    setAuth(token) {\n        this.headers.Authorization = `Bearer ${token}`;\n    }\n    /**\n     * Invokes a function\n     * @param functionName - The name of the Function to invoke.\n     * @param options - Options for invoking the Function.\n     */\n    invoke(functionName, options = {}) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const { headers, method, body: functionArgs } = options;\n                let _headers = {};\n                let { region } = options;\n                if (!region) {\n                    region = this.region;\n                }\n                if (region && region !== 'any') {\n                    _headers['x-region'] = region;\n                }\n                let body;\n                if (functionArgs &&\n                    ((headers && !Object.prototype.hasOwnProperty.call(headers, 'Content-Type')) || !headers)) {\n                    if ((typeof Blob !== 'undefined' && functionArgs instanceof Blob) ||\n                        functionArgs instanceof ArrayBuffer) {\n                        // will work for File as File inherits Blob\n                        // also works for ArrayBuffer as it is the same underlying structure as a Blob\n                        _headers['Content-Type'] = 'application/octet-stream';\n                        body = functionArgs;\n                    }\n                    else if (typeof functionArgs === 'string') {\n                        // plain string\n                        _headers['Content-Type'] = 'text/plain';\n                        body = functionArgs;\n                    }\n                    else if (typeof FormData !== 'undefined' && functionArgs instanceof FormData) {\n                        // don't set content-type headers\n                        // Request will automatically add the right boundary value\n                        body = functionArgs;\n                    }\n                    else {\n                        // default, assume this is JSON\n                        _headers['Content-Type'] = 'application/json';\n                        body = JSON.stringify(functionArgs);\n                    }\n                }\n                const response = yield this.fetch(`${this.url}/${functionName}`, {\n                    method: method || 'POST',\n                    // headers priority is (high to low):\n                    // 1. invoke-level headers\n                    // 2. client-level headers\n                    // 3. default Content-Type header\n                    headers: Object.assign(Object.assign(Object.assign({}, _headers), this.headers), headers),\n                    body,\n                }).catch((fetchError) => {\n                    throw new FunctionsFetchError(fetchError);\n                });\n                const isRelayError = response.headers.get('x-relay-error');\n                if (isRelayError && isRelayError === 'true') {\n                    throw new FunctionsRelayError(response);\n                }\n                if (!response.ok) {\n                    throw new FunctionsHttpError(response);\n                }\n                let responseType = ((_a = response.headers.get('Content-Type')) !== null && _a !== void 0 ? _a : 'text/plain').split(';')[0].trim();\n                let data;\n                if (responseType === 'application/json') {\n                    data = yield response.json();\n                }\n                else if (responseType === 'application/octet-stream') {\n                    data = yield response.blob();\n                }\n                else if (responseType === 'text/event-stream') {\n                    data = response;\n                }\n                else if (responseType === 'multipart/form-data') {\n                    data = yield response.formData();\n                }\n                else {\n                    // default to text\n                    data = yield response.text();\n                }\n                return { data, error: null };\n            }\n            catch (error) {\n                return { data: null, error };\n            }\n        });\n    }\n}\n//# sourceMappingURL=FunctionsClient.js.map", "import index from '../cjs/index.js'\nconst {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n} = index\n\nexport {\n  PostgrestBuilder,\n  PostgrestClient,\n  PostgrestFilterBuilder,\n  PostgrestQueryBuilder,\n  PostgrestTransformBuilder,\n  PostgrestError,\n}\n\n// compatibility with CJS output\nexport default {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n}\n", "// Node.js WebSocket entry point\nlet WebSocketImpl;\nif (typeof window === 'undefined') {\n    // Node.js environment\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n    WebSocketImpl = require('ws');\n}\nelse {\n    // Browser environment\n    WebSocketImpl = window.WebSocket;\n}\nexport default WebSocketImpl;\n//# sourceMappingURL=WebSocket.js.map", "export const version = '2.11.10';\n//# sourceMappingURL=version.js.map", "import { version } from './version';\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `realtime-js/${version}` };\nexport const VSN = '1.0.0';\nexport const VERSION = version;\nexport const DEFAULT_TIMEOUT = 10000;\nexport const WS_CLOSE_NORMAL = 1000;\nexport var SOCKET_STATES;\n(function (SOCKET_STATES) {\n    SOCKET_STATES[SOCKET_STATES[\"connecting\"] = 0] = \"connecting\";\n    SOCKET_STATES[SOCKET_STATES[\"open\"] = 1] = \"open\";\n    SOCKET_STATES[SOCKET_STATES[\"closing\"] = 2] = \"closing\";\n    SOCKET_STATES[SOCKET_STATES[\"closed\"] = 3] = \"closed\";\n})(SOCKET_STATES || (SOCKET_STATES = {}));\nexport var CHANNEL_STATES;\n(function (CHANNEL_STATES) {\n    CHANNEL_STATES[\"closed\"] = \"closed\";\n    CHANNEL_STATES[\"errored\"] = \"errored\";\n    CHANNEL_STATES[\"joined\"] = \"joined\";\n    CHANNEL_STATES[\"joining\"] = \"joining\";\n    CHANNEL_STATES[\"leaving\"] = \"leaving\";\n})(CHANNEL_STATES || (CHANNEL_STATES = {}));\nexport var CHANNEL_EVENTS;\n(function (CHANNEL_EVENTS) {\n    CHANNEL_EVENTS[\"close\"] = \"phx_close\";\n    CHANNEL_EVENTS[\"error\"] = \"phx_error\";\n    CHANNEL_EVENTS[\"join\"] = \"phx_join\";\n    CHANNEL_EVENTS[\"reply\"] = \"phx_reply\";\n    CHANNEL_EVENTS[\"leave\"] = \"phx_leave\";\n    CHANNEL_EVENTS[\"access_token\"] = \"access_token\";\n})(CHANNEL_EVENTS || (CHANNEL_EVENTS = {}));\nexport var TRANSPORTS;\n(function (TRANSPORTS) {\n    TRANSPORTS[\"websocket\"] = \"websocket\";\n})(TRANSPORTS || (TRANSPORTS = {}));\nexport var CONNECTION_STATE;\n(function (CONNECTION_STATE) {\n    CONNECTION_STATE[\"Connecting\"] = \"connecting\";\n    CONNECTION_STATE[\"Open\"] = \"open\";\n    CONNECTION_STATE[\"Closing\"] = \"closing\";\n    CONNECTION_STATE[\"Closed\"] = \"closed\";\n})(CONNECTION_STATE || (CONNECTION_STATE = {}));\n//# sourceMappingURL=constants.js.map", "// This file draws heavily from https://github.com/phoenixframework/phoenix/commit/cf098e9cf7a44ee6479d31d911a97d3c7430c6fe\n// License: https://github.com/phoenixframework/phoenix/blob/master/LICENSE.md\nexport default class Serializer {\n    constructor() {\n        this.HEADER_LENGTH = 1;\n    }\n    decode(rawPayload, callback) {\n        if (rawPayload.constructor === ArrayBuffer) {\n            return callback(this._binaryDecode(rawPayload));\n        }\n        if (typeof rawPayload === 'string') {\n            return callback(JSON.parse(rawPayload));\n        }\n        return callback({});\n    }\n    _binaryDecode(buffer) {\n        const view = new DataView(buffer);\n        const decoder = new TextDecoder();\n        return this._decodeBroadcast(buffer, view, decoder);\n    }\n    _decodeBroadcast(buffer, view, decoder) {\n        const topicSize = view.getUint8(1);\n        const eventSize = view.getUint8(2);\n        let offset = this.HEADER_LENGTH + 2;\n        const topic = decoder.decode(buffer.slice(offset, offset + topicSize));\n        offset = offset + topicSize;\n        const event = decoder.decode(buffer.slice(offset, offset + eventSize));\n        offset = offset + eventSize;\n        const data = JSON.parse(decoder.decode(buffer.slice(offset, buffer.byteLength)));\n        return { ref: null, topic: topic, event: event, payload: data };\n    }\n}\n//# sourceMappingURL=serializer.js.map", "/**\n * Creates a timer that accepts a `timerCalc` function to perform calculated timeout retries, such as exponential backoff.\n *\n * @example\n *    let reconnectTimer = new Timer(() => this.connect(), function(tries){\n *      return [1000, 5000, 10000][tries - 1] || 10000\n *    })\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n *    reconnectTimer.scheduleTimeout() // fires after 5000\n *    reconnectTimer.reset()\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n */\nexport default class Timer {\n    constructor(callback, timerCalc) {\n        this.callback = callback;\n        this.timerCalc = timerCalc;\n        this.timer = undefined;\n        this.tries = 0;\n        this.callback = callback;\n        this.timerCalc = timerCalc;\n    }\n    reset() {\n        this.tries = 0;\n        clearTimeout(this.timer);\n    }\n    // Cancels any previous scheduleTimeout and schedules callback\n    scheduleTimeout() {\n        clearTimeout(this.timer);\n        this.timer = setTimeout(() => {\n            this.tries = this.tries + 1;\n            this.callback();\n        }, this.timerCalc(this.tries + 1));\n    }\n}\n//# sourceMappingURL=timer.js.map", "/**\n * Helpers to convert the change Payload into native JS types.\n */\n// Adapted from epgsql (src/epgsql_binary.erl), this module licensed under\n// 3-clause BSD found here: https://raw.githubusercontent.com/epgsql/epgsql/devel/LICENSE\nexport var PostgresTypes;\n(function (PostgresTypes) {\n    PostgresTypes[\"abstime\"] = \"abstime\";\n    PostgresTypes[\"bool\"] = \"bool\";\n    PostgresTypes[\"date\"] = \"date\";\n    PostgresTypes[\"daterange\"] = \"daterange\";\n    PostgresTypes[\"float4\"] = \"float4\";\n    PostgresTypes[\"float8\"] = \"float8\";\n    PostgresTypes[\"int2\"] = \"int2\";\n    PostgresTypes[\"int4\"] = \"int4\";\n    PostgresTypes[\"int4range\"] = \"int4range\";\n    PostgresTypes[\"int8\"] = \"int8\";\n    PostgresTypes[\"int8range\"] = \"int8range\";\n    PostgresTypes[\"json\"] = \"json\";\n    PostgresTypes[\"jsonb\"] = \"jsonb\";\n    PostgresTypes[\"money\"] = \"money\";\n    PostgresTypes[\"numeric\"] = \"numeric\";\n    PostgresTypes[\"oid\"] = \"oid\";\n    PostgresTypes[\"reltime\"] = \"reltime\";\n    PostgresTypes[\"text\"] = \"text\";\n    PostgresTypes[\"time\"] = \"time\";\n    PostgresTypes[\"timestamp\"] = \"timestamp\";\n    PostgresTypes[\"timestamptz\"] = \"timestamptz\";\n    PostgresTypes[\"timetz\"] = \"timetz\";\n    PostgresTypes[\"tsrange\"] = \"tsrange\";\n    PostgresTypes[\"tstzrange\"] = \"tstzrange\";\n})(PostgresTypes || (PostgresTypes = {}));\n/**\n * Takes an array of columns and an object of string values then converts each string value\n * to its mapped type.\n *\n * @param {{name: String, type: String}[]} columns\n * @param {Object} record\n * @param {Object} options The map of various options that can be applied to the mapper\n * @param {Array} options.skipTypes The array of types that should not be converted\n *\n * @example convertChangeData([{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age:'33'}, {})\n * //=>{ first_name: 'Paul', age: 33 }\n */\nexport const convertChangeData = (columns, record, options = {}) => {\n    var _a;\n    const skipTypes = (_a = options.skipTypes) !== null && _a !== void 0 ? _a : [];\n    return Object.keys(record).reduce((acc, rec_key) => {\n        acc[rec_key] = convertColumn(rec_key, columns, record, skipTypes);\n        return acc;\n    }, {});\n};\n/**\n * Converts the value of an individual column.\n *\n * @param {String} columnName The column that you want to convert\n * @param {{name: String, type: String}[]} columns All of the columns\n * @param {Object} record The map of string values\n * @param {Array} skipTypes An array of types that should not be converted\n * @return {object} Useless information\n *\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, [])\n * //=> 33\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, ['int4'])\n * //=> \"33\"\n */\nexport const convertColumn = (columnName, columns, record, skipTypes) => {\n    const column = columns.find((x) => x.name === columnName);\n    const colType = column === null || column === void 0 ? void 0 : column.type;\n    const value = record[columnName];\n    if (colType && !skipTypes.includes(colType)) {\n        return convertCell(colType, value);\n    }\n    return noop(value);\n};\n/**\n * If the value of the cell is `null`, returns null.\n * Otherwise converts the string value to the correct type.\n * @param {String} type A postgres column type\n * @param {String} value The cell value\n *\n * @example convertCell('bool', 't')\n * //=> true\n * @example convertCell('int8', '10')\n * //=> 10\n * @example convertCell('_int4', '{1,2,3,4}')\n * //=> [1,2,3,4]\n */\nexport const convertCell = (type, value) => {\n    // if data type is an array\n    if (type.charAt(0) === '_') {\n        const dataType = type.slice(1, type.length);\n        return toArray(value, dataType);\n    }\n    // If not null, convert to correct type.\n    switch (type) {\n        case PostgresTypes.bool:\n            return toBoolean(value);\n        case PostgresTypes.float4:\n        case PostgresTypes.float8:\n        case PostgresTypes.int2:\n        case PostgresTypes.int4:\n        case PostgresTypes.int8:\n        case PostgresTypes.numeric:\n        case PostgresTypes.oid:\n            return toNumber(value);\n        case PostgresTypes.json:\n        case PostgresTypes.jsonb:\n            return toJson(value);\n        case PostgresTypes.timestamp:\n            return toTimestampString(value); // Format to be consistent with PostgREST\n        case PostgresTypes.abstime: // To allow users to cast it based on Timezone\n        case PostgresTypes.date: // To allow users to cast it based on Timezone\n        case PostgresTypes.daterange:\n        case PostgresTypes.int4range:\n        case PostgresTypes.int8range:\n        case PostgresTypes.money:\n        case PostgresTypes.reltime: // To allow users to cast it based on Timezone\n        case PostgresTypes.text:\n        case PostgresTypes.time: // To allow users to cast it based on Timezone\n        case PostgresTypes.timestamptz: // To allow users to cast it based on Timezone\n        case PostgresTypes.timetz: // To allow users to cast it based on Timezone\n        case PostgresTypes.tsrange:\n        case PostgresTypes.tstzrange:\n            return noop(value);\n        default:\n            // Return the value for remaining types\n            return noop(value);\n    }\n};\nconst noop = (value) => {\n    return value;\n};\nexport const toBoolean = (value) => {\n    switch (value) {\n        case 't':\n            return true;\n        case 'f':\n            return false;\n        default:\n            return value;\n    }\n};\nexport const toNumber = (value) => {\n    if (typeof value === 'string') {\n        const parsedValue = parseFloat(value);\n        if (!Number.isNaN(parsedValue)) {\n            return parsedValue;\n        }\n    }\n    return value;\n};\nexport const toJson = (value) => {\n    if (typeof value === 'string') {\n        try {\n            return JSON.parse(value);\n        }\n        catch (error) {\n            console.log(`JSON parse error: ${error}`);\n            return value;\n        }\n    }\n    return value;\n};\n/**\n * Converts a Postgres Array into a native JS array\n *\n * @example toArray('{}', 'int4')\n * //=> []\n * @example toArray('{\"[2021-01-01,2021-12-31)\",\"(2021-01-01,2021-12-32]\"}', 'daterange')\n * //=> ['[2021-01-01,2021-12-31)', '(2021-01-01,2021-12-32]']\n * @example toArray([1,2,3,4], 'int4')\n * //=> [1,2,3,4]\n */\nexport const toArray = (value, type) => {\n    if (typeof value !== 'string') {\n        return value;\n    }\n    const lastIdx = value.length - 1;\n    const closeBrace = value[lastIdx];\n    const openBrace = value[0];\n    // Confirm value is a Postgres array by checking curly brackets\n    if (openBrace === '{' && closeBrace === '}') {\n        let arr;\n        const valTrim = value.slice(1, lastIdx);\n        // TODO: find a better solution to separate Postgres array data\n        try {\n            arr = JSON.parse('[' + valTrim + ']');\n        }\n        catch (_) {\n            // WARNING: splitting on comma does not cover all edge cases\n            arr = valTrim ? valTrim.split(',') : [];\n        }\n        return arr.map((val) => convertCell(type, val));\n    }\n    return value;\n};\n/**\n * Fixes timestamp to be ISO-8601. Swaps the space between the date and time for a 'T'\n * See https://github.com/supabase/supabase/issues/18\n *\n * @example toTimestampString('2019-09-10 00:00:00')\n * //=> '2019-09-10T00:00:00'\n */\nexport const toTimestampString = (value) => {\n    if (typeof value === 'string') {\n        return value.replace(' ', 'T');\n    }\n    return value;\n};\nexport const httpEndpointURL = (socketUrl) => {\n    let url = socketUrl;\n    url = url.replace(/^ws/i, 'http');\n    url = url.replace(/(\\/socket\\/websocket|\\/socket|\\/websocket)\\/?$/i, '');\n    return url.replace(/\\/+$/, '');\n};\n//# sourceMappingURL=transformers.js.map", "import { DEFAULT_TIMEOUT } from '../lib/constants';\nexport default class Push {\n    /**\n     * Initializes the Push\n     *\n     * @param channel The Channel\n     * @param event The event, for example `\"phx_join\"`\n     * @param payload The payload, for example `{user_id: 123}`\n     * @param timeout The push timeout in milliseconds\n     */\n    constructor(channel, event, payload = {}, timeout = DEFAULT_TIMEOUT) {\n        this.channel = channel;\n        this.event = event;\n        this.payload = payload;\n        this.timeout = timeout;\n        this.sent = false;\n        this.timeoutTimer = undefined;\n        this.ref = '';\n        this.receivedResp = null;\n        this.recHooks = [];\n        this.refEvent = null;\n    }\n    resend(timeout) {\n        this.timeout = timeout;\n        this._cancelRefEvent();\n        this.ref = '';\n        this.refEvent = null;\n        this.receivedResp = null;\n        this.sent = false;\n        this.send();\n    }\n    send() {\n        if (this._hasReceived('timeout')) {\n            return;\n        }\n        this.startTimeout();\n        this.sent = true;\n        this.channel.socket.push({\n            topic: this.channel.topic,\n            event: this.event,\n            payload: this.payload,\n            ref: this.ref,\n            join_ref: this.channel._joinRef(),\n        });\n    }\n    updatePayload(payload) {\n        this.payload = Object.assign(Object.assign({}, this.payload), payload);\n    }\n    receive(status, callback) {\n        var _a;\n        if (this._hasReceived(status)) {\n            callback((_a = this.receivedResp) === null || _a === void 0 ? void 0 : _a.response);\n        }\n        this.recHooks.push({ status, callback });\n        return this;\n    }\n    startTimeout() {\n        if (this.timeoutTimer) {\n            return;\n        }\n        this.ref = this.channel.socket._makeRef();\n        this.refEvent = this.channel._replyEventName(this.ref);\n        const callback = (payload) => {\n            this._cancelRefEvent();\n            this._cancelTimeout();\n            this.receivedResp = payload;\n            this._matchReceive(payload);\n        };\n        this.channel._on(this.refEvent, {}, callback);\n        this.timeoutTimer = setTimeout(() => {\n            this.trigger('timeout', {});\n        }, this.timeout);\n    }\n    trigger(status, response) {\n        if (this.refEvent)\n            this.channel._trigger(this.refEvent, { status, response });\n    }\n    destroy() {\n        this._cancelRefEvent();\n        this._cancelTimeout();\n    }\n    _cancelRefEvent() {\n        if (!this.refEvent) {\n            return;\n        }\n        this.channel._off(this.refEvent, {});\n    }\n    _cancelTimeout() {\n        clearTimeout(this.timeoutTimer);\n        this.timeoutTimer = undefined;\n    }\n    _matchReceive({ status, response, }) {\n        this.recHooks\n            .filter((h) => h.status === status)\n            .forEach((h) => h.callback(response));\n    }\n    _hasReceived(status) {\n        return this.receivedResp && this.receivedResp.status === status;\n    }\n}\n//# sourceMappingURL=push.js.map", "/*\n  This file draws heavily from https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/assets/js/phoenix/presence.js\n  License: https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/LICENSE.md\n*/\nexport var REALTIME_PRESENCE_LISTEN_EVENTS;\n(function (REALTIME_PRESENCE_LISTEN_EVENTS) {\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"SYNC\"] = \"sync\";\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"JOIN\"] = \"join\";\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"LEAVE\"] = \"leave\";\n})(REALTIME_PRESENCE_LISTEN_EVENTS || (REALTIME_PRESENCE_LISTEN_EVENTS = {}));\nexport default class RealtimePresence {\n    /**\n     * Initializes the Presence.\n     *\n     * @param channel - The RealtimeChannel\n     * @param opts - The options,\n     *        for example `{events: {state: 'state', diff: 'diff'}}`\n     */\n    constructor(channel, opts) {\n        this.channel = channel;\n        this.state = {};\n        this.pendingDiffs = [];\n        this.joinRef = null;\n        this.caller = {\n            onJoin: () => { },\n            onLeave: () => { },\n            onSync: () => { },\n        };\n        const events = (opts === null || opts === void 0 ? void 0 : opts.events) || {\n            state: 'presence_state',\n            diff: 'presence_diff',\n        };\n        this.channel._on(events.state, {}, (newState) => {\n            const { onJoin, onLeave, onSync } = this.caller;\n            this.joinRef = this.channel._joinRef();\n            this.state = RealtimePresence.syncState(this.state, newState, onJoin, onLeave);\n            this.pendingDiffs.forEach((diff) => {\n                this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n            });\n            this.pendingDiffs = [];\n            onSync();\n        });\n        this.channel._on(events.diff, {}, (diff) => {\n            const { onJoin, onLeave, onSync } = this.caller;\n            if (this.inPendingSyncState()) {\n                this.pendingDiffs.push(diff);\n            }\n            else {\n                this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n                onSync();\n            }\n        });\n        this.onJoin((key, currentPresences, newPresences) => {\n            this.channel._trigger('presence', {\n                event: 'join',\n                key,\n                currentPresences,\n                newPresences,\n            });\n        });\n        this.onLeave((key, currentPresences, leftPresences) => {\n            this.channel._trigger('presence', {\n                event: 'leave',\n                key,\n                currentPresences,\n                leftPresences,\n            });\n        });\n        this.onSync(() => {\n            this.channel._trigger('presence', { event: 'sync' });\n        });\n    }\n    /**\n     * Used to sync the list of presences on the server with the\n     * client's state.\n     *\n     * An optional `onJoin` and `onLeave` callback can be provided to\n     * react to changes in the client's local presences across\n     * disconnects and reconnects with the server.\n     *\n     * @internal\n     */\n    static syncState(currentState, newState, onJoin, onLeave) {\n        const state = this.cloneDeep(currentState);\n        const transformedState = this.transformState(newState);\n        const joins = {};\n        const leaves = {};\n        this.map(state, (key, presences) => {\n            if (!transformedState[key]) {\n                leaves[key] = presences;\n            }\n        });\n        this.map(transformedState, (key, newPresences) => {\n            const currentPresences = state[key];\n            if (currentPresences) {\n                const newPresenceRefs = newPresences.map((m) => m.presence_ref);\n                const curPresenceRefs = currentPresences.map((m) => m.presence_ref);\n                const joinedPresences = newPresences.filter((m) => curPresenceRefs.indexOf(m.presence_ref) < 0);\n                const leftPresences = currentPresences.filter((m) => newPresenceRefs.indexOf(m.presence_ref) < 0);\n                if (joinedPresences.length > 0) {\n                    joins[key] = joinedPresences;\n                }\n                if (leftPresences.length > 0) {\n                    leaves[key] = leftPresences;\n                }\n            }\n            else {\n                joins[key] = newPresences;\n            }\n        });\n        return this.syncDiff(state, { joins, leaves }, onJoin, onLeave);\n    }\n    /**\n     * Used to sync a diff of presence join and leave events from the\n     * server, as they happen.\n     *\n     * Like `syncState`, `syncDiff` accepts optional `onJoin` and\n     * `onLeave` callbacks to react to a user joining or leaving from a\n     * device.\n     *\n     * @internal\n     */\n    static syncDiff(state, diff, onJoin, onLeave) {\n        const { joins, leaves } = {\n            joins: this.transformState(diff.joins),\n            leaves: this.transformState(diff.leaves),\n        };\n        if (!onJoin) {\n            onJoin = () => { };\n        }\n        if (!onLeave) {\n            onLeave = () => { };\n        }\n        this.map(joins, (key, newPresences) => {\n            var _a;\n            const currentPresences = (_a = state[key]) !== null && _a !== void 0 ? _a : [];\n            state[key] = this.cloneDeep(newPresences);\n            if (currentPresences.length > 0) {\n                const joinedPresenceRefs = state[key].map((m) => m.presence_ref);\n                const curPresences = currentPresences.filter((m) => joinedPresenceRefs.indexOf(m.presence_ref) < 0);\n                state[key].unshift(...curPresences);\n            }\n            onJoin(key, currentPresences, newPresences);\n        });\n        this.map(leaves, (key, leftPresences) => {\n            let currentPresences = state[key];\n            if (!currentPresences)\n                return;\n            const presenceRefsToRemove = leftPresences.map((m) => m.presence_ref);\n            currentPresences = currentPresences.filter((m) => presenceRefsToRemove.indexOf(m.presence_ref) < 0);\n            state[key] = currentPresences;\n            onLeave(key, currentPresences, leftPresences);\n            if (currentPresences.length === 0)\n                delete state[key];\n        });\n        return state;\n    }\n    /** @internal */\n    static map(obj, func) {\n        return Object.getOwnPropertyNames(obj).map((key) => func(key, obj[key]));\n    }\n    /**\n     * Remove 'metas' key\n     * Change 'phx_ref' to 'presence_ref'\n     * Remove 'phx_ref' and 'phx_ref_prev'\n     *\n     * @example\n     * // returns {\n     *  abc123: [\n     *    { presence_ref: '2', user_id: 1 },\n     *    { presence_ref: '3', user_id: 2 }\n     *  ]\n     * }\n     * RealtimePresence.transformState({\n     *  abc123: {\n     *    metas: [\n     *      { phx_ref: '2', phx_ref_prev: '1' user_id: 1 },\n     *      { phx_ref: '3', user_id: 2 }\n     *    ]\n     *  }\n     * })\n     *\n     * @internal\n     */\n    static transformState(state) {\n        state = this.cloneDeep(state);\n        return Object.getOwnPropertyNames(state).reduce((newState, key) => {\n            const presences = state[key];\n            if ('metas' in presences) {\n                newState[key] = presences.metas.map((presence) => {\n                    presence['presence_ref'] = presence['phx_ref'];\n                    delete presence['phx_ref'];\n                    delete presence['phx_ref_prev'];\n                    return presence;\n                });\n            }\n            else {\n                newState[key] = presences;\n            }\n            return newState;\n        }, {});\n    }\n    /** @internal */\n    static cloneDeep(obj) {\n        return JSON.parse(JSON.stringify(obj));\n    }\n    /** @internal */\n    onJoin(callback) {\n        this.caller.onJoin = callback;\n    }\n    /** @internal */\n    onLeave(callback) {\n        this.caller.onLeave = callback;\n    }\n    /** @internal */\n    onSync(callback) {\n        this.caller.onSync = callback;\n    }\n    /** @internal */\n    inPendingSyncState() {\n        return !this.joinRef || this.joinRef !== this.channel._joinRef();\n    }\n}\n//# sourceMappingURL=RealtimePresence.js.map", "import { CHANNEL_EVENTS, CHANNEL_STATES } from './lib/constants';\nimport Push from './lib/push';\nimport Timer from './lib/timer';\nimport RealtimePresence from './RealtimePresence';\nimport * as Transformers from './lib/transformers';\nimport { httpEndpointURL } from './lib/transformers';\nexport var REALTIME_POSTGRES_CHANGES_LISTEN_EVENT;\n(function (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT) {\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"ALL\"] = \"*\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"INSERT\"] = \"INSERT\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"UPDATE\"] = \"UPDATE\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"DELETE\"] = \"DELETE\";\n})(REALTIME_POSTGRES_CHANGES_LISTEN_EVENT || (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT = {}));\nexport var REALTIME_LISTEN_TYPES;\n(function (REALTIME_LISTEN_TYPES) {\n    REALTIME_LISTEN_TYPES[\"BROADCAST\"] = \"broadcast\";\n    REALTIME_LISTEN_TYPES[\"PRESENCE\"] = \"presence\";\n    REALTIME_LISTEN_TYPES[\"POSTGRES_CHANGES\"] = \"postgres_changes\";\n    REALTIME_LISTEN_TYPES[\"SYSTEM\"] = \"system\";\n})(REALTIME_LISTEN_TYPES || (REALTIME_LISTEN_TYPES = {}));\nexport var REALTIME_SUBSCRIBE_STATES;\n(function (REALTIME_SUBSCRIBE_STATES) {\n    REALTIME_SUBSCRIBE_STATES[\"SUBSCRIBED\"] = \"SUBSCRIBED\";\n    REALTIME_SUBSCRIBE_STATES[\"TIMED_OUT\"] = \"TIMED_OUT\";\n    REALTIME_SUBSCRIBE_STATES[\"CLOSED\"] = \"CLOSED\";\n    REALTIME_SUBSCRIBE_STATES[\"CHANNEL_ERROR\"] = \"CHANNEL_ERROR\";\n})(REALTIME_SUBSCRIBE_STATES || (REALTIME_SUBSCRIBE_STATES = {}));\nexport const REALTIME_CHANNEL_STATES = CHANNEL_STATES;\n/** A channel is the basic building block of Realtime\n * and narrows the scope of data flow to subscribed clients.\n * You can think of a channel as a chatroom where participants are able to see who's online\n * and send and receive messages.\n */\nexport default class RealtimeChannel {\n    constructor(\n    /** Topic name can be any string. */\n    topic, params = { config: {} }, socket) {\n        this.topic = topic;\n        this.params = params;\n        this.socket = socket;\n        this.bindings = {};\n        this.state = CHANNEL_STATES.closed;\n        this.joinedOnce = false;\n        this.pushBuffer = [];\n        this.subTopic = topic.replace(/^realtime:/i, '');\n        this.params.config = Object.assign({\n            broadcast: { ack: false, self: false },\n            presence: { key: '' },\n            private: false,\n        }, params.config);\n        this.timeout = this.socket.timeout;\n        this.joinPush = new Push(this, CHANNEL_EVENTS.join, this.params, this.timeout);\n        this.rejoinTimer = new Timer(() => this._rejoinUntilConnected(), this.socket.reconnectAfterMs);\n        this.joinPush.receive('ok', () => {\n            this.state = CHANNEL_STATES.joined;\n            this.rejoinTimer.reset();\n            this.pushBuffer.forEach((pushEvent) => pushEvent.send());\n            this.pushBuffer = [];\n        });\n        this._onClose(() => {\n            this.rejoinTimer.reset();\n            this.socket.log('channel', `close ${this.topic} ${this._joinRef()}`);\n            this.state = CHANNEL_STATES.closed;\n            this.socket._remove(this);\n        });\n        this._onError((reason) => {\n            if (this._isLeaving() || this._isClosed()) {\n                return;\n            }\n            this.socket.log('channel', `error ${this.topic}`, reason);\n            this.state = CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this.joinPush.receive('timeout', () => {\n            if (!this._isJoining()) {\n                return;\n            }\n            this.socket.log('channel', `timeout ${this.topic}`, this.joinPush.timeout);\n            this.state = CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this._on(CHANNEL_EVENTS.reply, {}, (payload, ref) => {\n            this._trigger(this._replyEventName(ref), payload);\n        });\n        this.presence = new RealtimePresence(this);\n        this.broadcastEndpointURL =\n            httpEndpointURL(this.socket.endPoint) + '/api/broadcast';\n        this.private = this.params.config.private || false;\n    }\n    /** Subscribe registers your client with the server */\n    subscribe(callback, timeout = this.timeout) {\n        var _a, _b;\n        if (!this.socket.isConnected()) {\n            this.socket.connect();\n        }\n        if (this.joinedOnce) {\n            throw `tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance`;\n        }\n        else {\n            const { config: { broadcast, presence, private: isPrivate }, } = this.params;\n            this._onError((e) => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, e));\n            this._onClose(() => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CLOSED));\n            const accessTokenPayload = {};\n            const config = {\n                broadcast,\n                presence,\n                postgres_changes: (_b = (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.map((r) => r.filter)) !== null && _b !== void 0 ? _b : [],\n                private: isPrivate,\n            };\n            if (this.socket.accessTokenValue) {\n                accessTokenPayload.access_token = this.socket.accessTokenValue;\n            }\n            this.updateJoinPayload(Object.assign({ config }, accessTokenPayload));\n            this.joinedOnce = true;\n            this._rejoin(timeout);\n            this.joinPush\n                .receive('ok', async ({ postgres_changes }) => {\n                var _a;\n                this.socket.setAuth();\n                if (postgres_changes === undefined) {\n                    callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n                    return;\n                }\n                else {\n                    const clientPostgresBindings = this.bindings.postgres_changes;\n                    const bindingsLen = (_a = clientPostgresBindings === null || clientPostgresBindings === void 0 ? void 0 : clientPostgresBindings.length) !== null && _a !== void 0 ? _a : 0;\n                    const newPostgresBindings = [];\n                    for (let i = 0; i < bindingsLen; i++) {\n                        const clientPostgresBinding = clientPostgresBindings[i];\n                        const { filter: { event, schema, table, filter }, } = clientPostgresBinding;\n                        const serverPostgresFilter = postgres_changes && postgres_changes[i];\n                        if (serverPostgresFilter &&\n                            serverPostgresFilter.event === event &&\n                            serverPostgresFilter.schema === schema &&\n                            serverPostgresFilter.table === table &&\n                            serverPostgresFilter.filter === filter) {\n                            newPostgresBindings.push(Object.assign(Object.assign({}, clientPostgresBinding), { id: serverPostgresFilter.id }));\n                        }\n                        else {\n                            this.unsubscribe();\n                            this.state = CHANNEL_STATES.errored;\n                            callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error('mismatch between server and client bindings for postgres changes'));\n                            return;\n                        }\n                    }\n                    this.bindings.postgres_changes = newPostgresBindings;\n                    callback && callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n                    return;\n                }\n            })\n                .receive('error', (error) => {\n                this.state = CHANNEL_STATES.errored;\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error(JSON.stringify(Object.values(error).join(', ') || 'error')));\n                return;\n            })\n                .receive('timeout', () => {\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.TIMED_OUT);\n                return;\n            });\n        }\n        return this;\n    }\n    presenceState() {\n        return this.presence.state;\n    }\n    async track(payload, opts = {}) {\n        return await this.send({\n            type: 'presence',\n            event: 'track',\n            payload,\n        }, opts.timeout || this.timeout);\n    }\n    async untrack(opts = {}) {\n        return await this.send({\n            type: 'presence',\n            event: 'untrack',\n        }, opts);\n    }\n    on(type, filter, callback) {\n        return this._on(type, filter, callback);\n    }\n    /**\n     * Sends a message into the channel.\n     *\n     * @param args Arguments to send to channel\n     * @param args.type The type of event to send\n     * @param args.event The name of the event being sent\n     * @param args.payload Payload to be sent\n     * @param opts Options to be used during the send process\n     */\n    async send(args, opts = {}) {\n        var _a, _b;\n        if (!this._canPush() && args.type === 'broadcast') {\n            const { event, payload: endpoint_payload } = args;\n            const authorization = this.socket.accessTokenValue\n                ? `Bearer ${this.socket.accessTokenValue}`\n                : '';\n            const options = {\n                method: 'POST',\n                headers: {\n                    Authorization: authorization,\n                    apikey: this.socket.apiKey ? this.socket.apiKey : '',\n                    'Content-Type': 'application/json',\n                },\n                body: JSON.stringify({\n                    messages: [\n                        {\n                            topic: this.subTopic,\n                            event,\n                            payload: endpoint_payload,\n                            private: this.private,\n                        },\n                    ],\n                }),\n            };\n            try {\n                const response = await this._fetchWithTimeout(this.broadcastEndpointURL, options, (_a = opts.timeout) !== null && _a !== void 0 ? _a : this.timeout);\n                await ((_b = response.body) === null || _b === void 0 ? void 0 : _b.cancel());\n                return response.ok ? 'ok' : 'error';\n            }\n            catch (error) {\n                if (error.name === 'AbortError') {\n                    return 'timed out';\n                }\n                else {\n                    return 'error';\n                }\n            }\n        }\n        else {\n            return new Promise((resolve) => {\n                var _a, _b, _c;\n                const push = this._push(args.type, args, opts.timeout || this.timeout);\n                if (args.type === 'broadcast' && !((_c = (_b = (_a = this.params) === null || _a === void 0 ? void 0 : _a.config) === null || _b === void 0 ? void 0 : _b.broadcast) === null || _c === void 0 ? void 0 : _c.ack)) {\n                    resolve('ok');\n                }\n                push.receive('ok', () => resolve('ok'));\n                push.receive('error', () => resolve('error'));\n                push.receive('timeout', () => resolve('timed out'));\n            });\n        }\n    }\n    updateJoinPayload(payload) {\n        this.joinPush.updatePayload(payload);\n    }\n    /**\n     * Leaves the channel.\n     *\n     * Unsubscribes from server events, and instructs channel to terminate on server.\n     * Triggers onClose() hooks.\n     *\n     * To receive leave acknowledgements, use the a `receive` hook to bind to the server ack, ie:\n     * channel.unsubscribe().receive(\"ok\", () => alert(\"left!\") )\n     */\n    unsubscribe(timeout = this.timeout) {\n        this.state = CHANNEL_STATES.leaving;\n        const onClose = () => {\n            this.socket.log('channel', `leave ${this.topic}`);\n            this._trigger(CHANNEL_EVENTS.close, 'leave', this._joinRef());\n        };\n        this.joinPush.destroy();\n        return new Promise((resolve) => {\n            const leavePush = new Push(this, CHANNEL_EVENTS.leave, {}, timeout);\n            leavePush\n                .receive('ok', () => {\n                onClose();\n                resolve('ok');\n            })\n                .receive('timeout', () => {\n                onClose();\n                resolve('timed out');\n            })\n                .receive('error', () => {\n                resolve('error');\n            });\n            leavePush.send();\n            if (!this._canPush()) {\n                leavePush.trigger('ok', {});\n            }\n        });\n    }\n    /**\n     * Teardown the channel.\n     *\n     * Destroys and stops related timers.\n     */\n    teardown() {\n        this.pushBuffer.forEach((push) => push.destroy());\n        this.rejoinTimer && clearTimeout(this.rejoinTimer.timer);\n        this.joinPush.destroy();\n    }\n    /** @internal */\n    async _fetchWithTimeout(url, options, timeout) {\n        const controller = new AbortController();\n        const id = setTimeout(() => controller.abort(), timeout);\n        const response = await this.socket.fetch(url, Object.assign(Object.assign({}, options), { signal: controller.signal }));\n        clearTimeout(id);\n        return response;\n    }\n    /** @internal */\n    _push(event, payload, timeout = this.timeout) {\n        if (!this.joinedOnce) {\n            throw `tried to push '${event}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;\n        }\n        let pushEvent = new Push(this, event, payload, timeout);\n        if (this._canPush()) {\n            pushEvent.send();\n        }\n        else {\n            pushEvent.startTimeout();\n            this.pushBuffer.push(pushEvent);\n        }\n        return pushEvent;\n    }\n    /**\n     * Overridable message hook\n     *\n     * Receives all events for specialized message handling before dispatching to the channel callbacks.\n     * Must return the payload, modified or unmodified.\n     *\n     * @internal\n     */\n    _onMessage(_event, payload, _ref) {\n        return payload;\n    }\n    /** @internal */\n    _isMember(topic) {\n        return this.topic === topic;\n    }\n    /** @internal */\n    _joinRef() {\n        return this.joinPush.ref;\n    }\n    /** @internal */\n    _trigger(type, payload, ref) {\n        var _a, _b;\n        const typeLower = type.toLocaleLowerCase();\n        const { close, error, leave, join } = CHANNEL_EVENTS;\n        const events = [close, error, leave, join];\n        if (ref && events.indexOf(typeLower) >= 0 && ref !== this._joinRef()) {\n            return;\n        }\n        let handledPayload = this._onMessage(typeLower, payload, ref);\n        if (payload && !handledPayload) {\n            throw 'channel onMessage callbacks must return the payload, modified or unmodified';\n        }\n        if (['insert', 'update', 'delete'].includes(typeLower)) {\n            (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.filter((bind) => {\n                var _a, _b, _c;\n                return (((_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event) === '*' ||\n                    ((_c = (_b = bind.filter) === null || _b === void 0 ? void 0 : _b.event) === null || _c === void 0 ? void 0 : _c.toLocaleLowerCase()) === typeLower);\n            }).map((bind) => bind.callback(handledPayload, ref));\n        }\n        else {\n            (_b = this.bindings[typeLower]) === null || _b === void 0 ? void 0 : _b.filter((bind) => {\n                var _a, _b, _c, _d, _e, _f;\n                if (['broadcast', 'presence', 'postgres_changes'].includes(typeLower)) {\n                    if ('id' in bind) {\n                        const bindId = bind.id;\n                        const bindEvent = (_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event;\n                        return (bindId &&\n                            ((_b = payload.ids) === null || _b === void 0 ? void 0 : _b.includes(bindId)) &&\n                            (bindEvent === '*' ||\n                                (bindEvent === null || bindEvent === void 0 ? void 0 : bindEvent.toLocaleLowerCase()) ===\n                                    ((_c = payload.data) === null || _c === void 0 ? void 0 : _c.type.toLocaleLowerCase())));\n                    }\n                    else {\n                        const bindEvent = (_e = (_d = bind === null || bind === void 0 ? void 0 : bind.filter) === null || _d === void 0 ? void 0 : _d.event) === null || _e === void 0 ? void 0 : _e.toLocaleLowerCase();\n                        return (bindEvent === '*' ||\n                            bindEvent === ((_f = payload === null || payload === void 0 ? void 0 : payload.event) === null || _f === void 0 ? void 0 : _f.toLocaleLowerCase()));\n                    }\n                }\n                else {\n                    return bind.type.toLocaleLowerCase() === typeLower;\n                }\n            }).map((bind) => {\n                if (typeof handledPayload === 'object' && 'ids' in handledPayload) {\n                    const postgresChanges = handledPayload.data;\n                    const { schema, table, commit_timestamp, type, errors } = postgresChanges;\n                    const enrichedPayload = {\n                        schema: schema,\n                        table: table,\n                        commit_timestamp: commit_timestamp,\n                        eventType: type,\n                        new: {},\n                        old: {},\n                        errors: errors,\n                    };\n                    handledPayload = Object.assign(Object.assign({}, enrichedPayload), this._getPayloadRecords(postgresChanges));\n                }\n                bind.callback(handledPayload, ref);\n            });\n        }\n    }\n    /** @internal */\n    _isClosed() {\n        return this.state === CHANNEL_STATES.closed;\n    }\n    /** @internal */\n    _isJoined() {\n        return this.state === CHANNEL_STATES.joined;\n    }\n    /** @internal */\n    _isJoining() {\n        return this.state === CHANNEL_STATES.joining;\n    }\n    /** @internal */\n    _isLeaving() {\n        return this.state === CHANNEL_STATES.leaving;\n    }\n    /** @internal */\n    _replyEventName(ref) {\n        return `chan_reply_${ref}`;\n    }\n    /** @internal */\n    _on(type, filter, callback) {\n        const typeLower = type.toLocaleLowerCase();\n        const binding = {\n            type: typeLower,\n            filter: filter,\n            callback: callback,\n        };\n        if (this.bindings[typeLower]) {\n            this.bindings[typeLower].push(binding);\n        }\n        else {\n            this.bindings[typeLower] = [binding];\n        }\n        return this;\n    }\n    /** @internal */\n    _off(type, filter) {\n        const typeLower = type.toLocaleLowerCase();\n        this.bindings[typeLower] = this.bindings[typeLower].filter((bind) => {\n            var _a;\n            return !(((_a = bind.type) === null || _a === void 0 ? void 0 : _a.toLocaleLowerCase()) === typeLower &&\n                RealtimeChannel.isEqual(bind.filter, filter));\n        });\n        return this;\n    }\n    /** @internal */\n    static isEqual(obj1, obj2) {\n        if (Object.keys(obj1).length !== Object.keys(obj2).length) {\n            return false;\n        }\n        for (const k in obj1) {\n            if (obj1[k] !== obj2[k]) {\n                return false;\n            }\n        }\n        return true;\n    }\n    /** @internal */\n    _rejoinUntilConnected() {\n        this.rejoinTimer.scheduleTimeout();\n        if (this.socket.isConnected()) {\n            this._rejoin();\n        }\n    }\n    /**\n     * Registers a callback that will be executed when the channel closes.\n     *\n     * @internal\n     */\n    _onClose(callback) {\n        this._on(CHANNEL_EVENTS.close, {}, callback);\n    }\n    /**\n     * Registers a callback that will be executed when the channel encounteres an error.\n     *\n     * @internal\n     */\n    _onError(callback) {\n        this._on(CHANNEL_EVENTS.error, {}, (reason) => callback(reason));\n    }\n    /**\n     * Returns `true` if the socket is connected and the channel has been joined.\n     *\n     * @internal\n     */\n    _canPush() {\n        return this.socket.isConnected() && this._isJoined();\n    }\n    /** @internal */\n    _rejoin(timeout = this.timeout) {\n        if (this._isLeaving()) {\n            return;\n        }\n        this.socket._leaveOpenTopic(this.topic);\n        this.state = CHANNEL_STATES.joining;\n        this.joinPush.resend(timeout);\n    }\n    /** @internal */\n    _getPayloadRecords(payload) {\n        const records = {\n            new: {},\n            old: {},\n        };\n        if (payload.type === 'INSERT' || payload.type === 'UPDATE') {\n            records.new = Transformers.convertChangeData(payload.columns, payload.record);\n        }\n        if (payload.type === 'UPDATE' || payload.type === 'DELETE') {\n            records.old = Transformers.convertChangeData(payload.columns, payload.old_record);\n        }\n        return records;\n    }\n}\n//# sourceMappingURL=RealtimeChannel.js.map", "import WebSocket from './WebSocket';\nimport { CHANNEL_EVENTS, CONNECTION_STATE, DEFAULT_HEADERS, DEFAULT_TIMEOUT, SOCKET_STATES, TRANSPORTS, VSN, WS_CLOSE_NORMAL, } from './lib/constants';\nimport Serializer from './lib/serializer';\nimport Timer from './lib/timer';\nimport { httpEndpointURL } from './lib/transformers';\nimport RealtimeChannel from './RealtimeChannel';\nconst noop = () => { };\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`;\nexport default class RealtimeClient {\n    /**\n     * Initializes the Socket.\n     *\n     * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n     * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n     * @param options.transport The Websocket Transport, for example WebSocket. This can be a custom implementation\n     * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n     * @param options.params The optional params to pass when connecting.\n     * @param options.headers The optional headers to pass when connecting.\n     * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n     * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n     * @param options.logLevel Sets the log level for Realtime\n     * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n     * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n     * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n     * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n     * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n     */\n    constructor(endPoint, options) {\n        var _a;\n        this.accessTokenValue = null;\n        this.apiKey = null;\n        this.channels = new Array();\n        this.endPoint = '';\n        this.httpEndpoint = '';\n        this.headers = DEFAULT_HEADERS;\n        this.params = {};\n        this.timeout = DEFAULT_TIMEOUT;\n        this.heartbeatIntervalMs = 25000;\n        this.heartbeatTimer = undefined;\n        this.pendingHeartbeatRef = null;\n        this.heartbeatCallback = noop;\n        this.ref = 0;\n        this.logger = noop;\n        this.conn = null;\n        this.sendBuffer = [];\n        this.serializer = new Serializer();\n        this.stateChangeCallbacks = {\n            open: [],\n            close: [],\n            error: [],\n            message: [],\n        };\n        this.accessToken = null;\n        /**\n         * Use either custom fetch, if provided, or default fetch to make HTTP requests\n         *\n         * @internal\n         */\n        this._resolveFetch = (customFetch) => {\n            let _fetch;\n            if (customFetch) {\n                _fetch = customFetch;\n            }\n            else if (typeof fetch === 'undefined') {\n                _fetch = (...args) => import('@supabase/node-fetch').then(({ default: fetch }) => fetch(...args));\n            }\n            else {\n                _fetch = fetch;\n            }\n            return (...args) => _fetch(...args);\n        };\n        this.endPoint = `${endPoint}/${TRANSPORTS.websocket}`;\n        this.httpEndpoint = httpEndpointURL(endPoint);\n        if (options === null || options === void 0 ? void 0 : options.transport) {\n            this.transport = options.transport;\n        }\n        else {\n            this.transport = null;\n        }\n        if (options === null || options === void 0 ? void 0 : options.params)\n            this.params = options.params;\n        if (options === null || options === void 0 ? void 0 : options.headers)\n            this.headers = Object.assign(Object.assign({}, this.headers), options.headers);\n        if (options === null || options === void 0 ? void 0 : options.timeout)\n            this.timeout = options.timeout;\n        if (options === null || options === void 0 ? void 0 : options.logger)\n            this.logger = options.logger;\n        if ((options === null || options === void 0 ? void 0 : options.logLevel) || (options === null || options === void 0 ? void 0 : options.log_level)) {\n            this.logLevel = options.logLevel || options.log_level;\n            this.params = Object.assign(Object.assign({}, this.params), { log_level: this.logLevel });\n        }\n        if (options === null || options === void 0 ? void 0 : options.heartbeatIntervalMs)\n            this.heartbeatIntervalMs = options.heartbeatIntervalMs;\n        const accessTokenValue = (_a = options === null || options === void 0 ? void 0 : options.params) === null || _a === void 0 ? void 0 : _a.apikey;\n        if (accessTokenValue) {\n            this.accessTokenValue = accessTokenValue;\n            this.apiKey = accessTokenValue;\n        }\n        this.reconnectAfterMs = (options === null || options === void 0 ? void 0 : options.reconnectAfterMs)\n            ? options.reconnectAfterMs\n            : (tries) => {\n                return [1000, 2000, 5000, 10000][tries - 1] || 10000;\n            };\n        this.encode = (options === null || options === void 0 ? void 0 : options.encode)\n            ? options.encode\n            : (payload, callback) => {\n                return callback(JSON.stringify(payload));\n            };\n        this.decode = (options === null || options === void 0 ? void 0 : options.decode)\n            ? options.decode\n            : this.serializer.decode.bind(this.serializer);\n        this.reconnectTimer = new Timer(async () => {\n            this.disconnect();\n            this.connect();\n        }, this.reconnectAfterMs);\n        this.fetch = this._resolveFetch(options === null || options === void 0 ? void 0 : options.fetch);\n        if (options === null || options === void 0 ? void 0 : options.worker) {\n            if (typeof window !== 'undefined' && !window.Worker) {\n                throw new Error('Web Worker is not supported');\n            }\n            this.worker = (options === null || options === void 0 ? void 0 : options.worker) || false;\n            this.workerUrl = options === null || options === void 0 ? void 0 : options.workerUrl;\n        }\n        this.accessToken = (options === null || options === void 0 ? void 0 : options.accessToken) || null;\n    }\n    /**\n     * Connects the socket, unless already connected.\n     */\n    connect() {\n        if (this.conn) {\n            return;\n        }\n        if (!this.transport) {\n            this.transport = WebSocket;\n        }\n        if (this.transport) {\n            // Detect if using the native browser WebSocket\n            const isBrowser = typeof window !== 'undefined' && this.transport === window.WebSocket;\n            if (isBrowser) {\n                this.conn = new this.transport(this.endpointURL());\n            }\n            else {\n                this.conn = new this.transport(this.endpointURL(), undefined, {\n                    headers: this.headers,\n                });\n            }\n            this.setupConnection();\n            return;\n        }\n        this.conn = new WSWebSocketDummy(this.endpointURL(), undefined, {\n            close: () => {\n                this.conn = null;\n            },\n        });\n    }\n    /**\n     * Returns the URL of the websocket.\n     * @returns string The URL of the websocket.\n     */\n    endpointURL() {\n        return this._appendParams(this.endPoint, Object.assign({}, this.params, { vsn: VSN }));\n    }\n    /**\n     * Disconnects the socket.\n     *\n     * @param code A numeric status code to send on disconnect.\n     * @param reason A custom reason for the disconnect.\n     */\n    disconnect(code, reason) {\n        if (this.conn) {\n            this.conn.onclose = function () { }; // noop\n            if (code) {\n                this.conn.close(code, reason !== null && reason !== void 0 ? reason : '');\n            }\n            else {\n                this.conn.close();\n            }\n            this.conn = null;\n            // remove open handles\n            this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n            this.reconnectTimer.reset();\n            this.channels.forEach((channel) => channel.teardown());\n        }\n    }\n    /**\n     * Returns all created channels\n     */\n    getChannels() {\n        return this.channels;\n    }\n    /**\n     * Unsubscribes and removes a single channel\n     * @param channel A RealtimeChannel instance\n     */\n    async removeChannel(channel) {\n        const status = await channel.unsubscribe();\n        this.channels = this.channels.filter((c) => c._joinRef !== channel._joinRef);\n        if (this.channels.length === 0) {\n            this.disconnect();\n        }\n        return status;\n    }\n    /**\n     * Unsubscribes and removes all channels\n     */\n    async removeAllChannels() {\n        const values_1 = await Promise.all(this.channels.map((channel) => channel.unsubscribe()));\n        this.channels = [];\n        this.disconnect();\n        return values_1;\n    }\n    /**\n     * Logs the message.\n     *\n     * For customized logging, `this.logger` can be overridden.\n     */\n    log(kind, msg, data) {\n        this.logger(kind, msg, data);\n    }\n    /**\n     * Returns the current state of the socket.\n     */\n    connectionState() {\n        switch (this.conn && this.conn.readyState) {\n            case SOCKET_STATES.connecting:\n                return CONNECTION_STATE.Connecting;\n            case SOCKET_STATES.open:\n                return CONNECTION_STATE.Open;\n            case SOCKET_STATES.closing:\n                return CONNECTION_STATE.Closing;\n            default:\n                return CONNECTION_STATE.Closed;\n        }\n    }\n    /**\n     * Returns `true` is the connection is open.\n     */\n    isConnected() {\n        return this.connectionState() === CONNECTION_STATE.Open;\n    }\n    channel(topic, params = { config: {} }) {\n        const realtimeTopic = `realtime:${topic}`;\n        const exists = this.getChannels().find((c) => c.topic === realtimeTopic);\n        if (!exists) {\n            const chan = new RealtimeChannel(`realtime:${topic}`, params, this);\n            this.channels.push(chan);\n            return chan;\n        }\n        else {\n            return exists;\n        }\n    }\n    /**\n     * Push out a message if the socket is connected.\n     *\n     * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n     */\n    push(data) {\n        const { topic, event, payload, ref } = data;\n        const callback = () => {\n            this.encode(data, (result) => {\n                var _a;\n                (_a = this.conn) === null || _a === void 0 ? void 0 : _a.send(result);\n            });\n        };\n        this.log('push', `${topic} ${event} (${ref})`, payload);\n        if (this.isConnected()) {\n            callback();\n        }\n        else {\n            this.sendBuffer.push(callback);\n        }\n    }\n    /**\n     * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n     *\n     * If param is null it will use the `accessToken` callback function or the token set on the client.\n     *\n     * On callback used, it will set the value of the token internal to the client.\n     *\n     * @param token A JWT string to override the token set on the client.\n     */\n    async setAuth(token = null) {\n        let tokenToSend = token ||\n            (this.accessToken && (await this.accessToken())) ||\n            this.accessTokenValue;\n        if (this.accessTokenValue != tokenToSend) {\n            this.accessTokenValue = tokenToSend;\n            this.channels.forEach((channel) => {\n                tokenToSend &&\n                    channel.updateJoinPayload({\n                        access_token: tokenToSend,\n                        version: this.headers && this.headers['X-Client-Info'],\n                    });\n                if (channel.joinedOnce && channel._isJoined()) {\n                    channel._push(CHANNEL_EVENTS.access_token, {\n                        access_token: tokenToSend,\n                    });\n                }\n            });\n        }\n    }\n    /**\n     * Sends a heartbeat message if the socket is connected.\n     */\n    async sendHeartbeat() {\n        var _a;\n        if (!this.isConnected()) {\n            this.heartbeatCallback('disconnected');\n            return;\n        }\n        if (this.pendingHeartbeatRef) {\n            this.pendingHeartbeatRef = null;\n            this.log('transport', 'heartbeat timeout. Attempting to re-establish connection');\n            this.heartbeatCallback('timeout');\n            (_a = this.conn) === null || _a === void 0 ? void 0 : _a.close(WS_CLOSE_NORMAL, 'hearbeat timeout');\n            return;\n        }\n        this.pendingHeartbeatRef = this._makeRef();\n        this.push({\n            topic: 'phoenix',\n            event: 'heartbeat',\n            payload: {},\n            ref: this.pendingHeartbeatRef,\n        });\n        this.heartbeatCallback('sent');\n        await this.setAuth();\n    }\n    onHeartbeat(callback) {\n        this.heartbeatCallback = callback;\n    }\n    /**\n     * Flushes send buffer\n     */\n    flushSendBuffer() {\n        if (this.isConnected() && this.sendBuffer.length > 0) {\n            this.sendBuffer.forEach((callback) => callback());\n            this.sendBuffer = [];\n        }\n    }\n    /**\n     * Return the next message ref, accounting for overflows\n     *\n     * @internal\n     */\n    _makeRef() {\n        let newRef = this.ref + 1;\n        if (newRef === this.ref) {\n            this.ref = 0;\n        }\n        else {\n            this.ref = newRef;\n        }\n        return this.ref.toString();\n    }\n    /**\n     * Unsubscribe from channels with the specified topic.\n     *\n     * @internal\n     */\n    _leaveOpenTopic(topic) {\n        let dupChannel = this.channels.find((c) => c.topic === topic && (c._isJoined() || c._isJoining()));\n        if (dupChannel) {\n            this.log('transport', `leaving duplicate topic \"${topic}\"`);\n            dupChannel.unsubscribe();\n        }\n    }\n    /**\n     * Removes a subscription from the socket.\n     *\n     * @param channel An open subscription.\n     *\n     * @internal\n     */\n    _remove(channel) {\n        this.channels = this.channels.filter((c) => c.topic !== channel.topic);\n    }\n    /**\n     * Sets up connection handlers.\n     *\n     * @internal\n     */\n    setupConnection() {\n        if (this.conn) {\n            this.conn.binaryType = 'arraybuffer';\n            this.conn.onopen = () => this._onConnOpen();\n            this.conn.onerror = (error) => this._onConnError(error);\n            this.conn.onmessage = (event) => this._onConnMessage(event);\n            this.conn.onclose = (event) => this._onConnClose(event);\n        }\n    }\n    /** @internal */\n    _onConnMessage(rawMessage) {\n        this.decode(rawMessage.data, (msg) => {\n            let { topic, event, payload, ref } = msg;\n            if (topic === 'phoenix' && event === 'phx_reply') {\n                this.heartbeatCallback(msg.payload.status == 'ok' ? 'ok' : 'error');\n            }\n            if (ref && ref === this.pendingHeartbeatRef) {\n                this.pendingHeartbeatRef = null;\n            }\n            this.log('receive', `${payload.status || ''} ${topic} ${event} ${(ref && '(' + ref + ')') || ''}`, payload);\n            Array.from(this.channels)\n                .filter((channel) => channel._isMember(topic))\n                .forEach((channel) => channel._trigger(event, payload, ref));\n            this.stateChangeCallbacks.message.forEach((callback) => callback(msg));\n        });\n    }\n    /** @internal */\n    _onConnOpen() {\n        this.log('transport', `connected to ${this.endpointURL()}`);\n        this.flushSendBuffer();\n        this.reconnectTimer.reset();\n        if (!this.worker) {\n            this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n            this.heartbeatTimer = setInterval(() => this.sendHeartbeat(), this.heartbeatIntervalMs);\n        }\n        else {\n            if (this.workerUrl) {\n                this.log('worker', `starting worker for from ${this.workerUrl}`);\n            }\n            else {\n                this.log('worker', `starting default worker`);\n            }\n            const objectUrl = this._workerObjectUrl(this.workerUrl);\n            this.workerRef = new Worker(objectUrl);\n            this.workerRef.onerror = (error) => {\n                this.log('worker', 'worker error', error.message);\n                this.workerRef.terminate();\n            };\n            this.workerRef.onmessage = (event) => {\n                if (event.data.event === 'keepAlive') {\n                    this.sendHeartbeat();\n                }\n            };\n            this.workerRef.postMessage({\n                event: 'start',\n                interval: this.heartbeatIntervalMs,\n            });\n        }\n        this.stateChangeCallbacks.open.forEach((callback) => callback());\n    }\n    /** @internal */\n    _onConnClose(event) {\n        this.log('transport', 'close', event);\n        this._triggerChanError();\n        this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n        this.reconnectTimer.scheduleTimeout();\n        this.stateChangeCallbacks.close.forEach((callback) => callback(event));\n    }\n    /** @internal */\n    _onConnError(error) {\n        this.log('transport', error.message);\n        this._triggerChanError();\n        this.stateChangeCallbacks.error.forEach((callback) => callback(error));\n    }\n    /** @internal */\n    _triggerChanError() {\n        this.channels.forEach((channel) => channel._trigger(CHANNEL_EVENTS.error));\n    }\n    /** @internal */\n    _appendParams(url, params) {\n        if (Object.keys(params).length === 0) {\n            return url;\n        }\n        const prefix = url.match(/\\?/) ? '&' : '?';\n        const query = new URLSearchParams(params);\n        return `${url}${prefix}${query}`;\n    }\n    _workerObjectUrl(url) {\n        let result_url;\n        if (url) {\n            result_url = url;\n        }\n        else {\n            const blob = new Blob([WORKER_SCRIPT], { type: 'application/javascript' });\n            result_url = URL.createObjectURL(blob);\n        }\n        return result_url;\n    }\n}\nclass WSWebSocketDummy {\n    constructor(address, _protocols, options) {\n        this.binaryType = 'arraybuffer';\n        this.onclose = () => { };\n        this.onerror = () => { };\n        this.onmessage = () => { };\n        this.onopen = () => { };\n        this.readyState = SOCKET_STATES.connecting;\n        this.send = () => { };\n        this.url = null;\n        this.url = address;\n        this.close = options.close;\n    }\n}\n//# sourceMappingURL=RealtimeClient.js.map", "import RealtimeClient from './RealtimeClient';\nimport RealtimeChannel, { REALTIME_LISTEN_TYPES, REALTIME_POSTGRES_CHANGES_LISTEN_EVENT, REALTIME_SUBSCRIBE_STATES, REALTIME_CHANNEL_STATES, } from './RealtimeChannel';\nimport RealtimePresence, { REALTIME_PRESENCE_LISTEN_EVENTS, } from './RealtimePresence';\nexport { RealtimePresence, RealtimeChannel, RealtimeClient, REALTIME_LISTEN_TYPES, REALTIME_POSTGRES_CHANGES_LISTEN_EVENT, REALTIME_PRESENCE_LISTEN_EVENTS, REALTIME_SUBSCRIBE_STATES, REALTIME_CHANNEL_STATES, };\n//# sourceMappingURL=index.js.map", "export class StorageError extends Error {\n    constructor(message) {\n        super(message);\n        this.__isStorageError = true;\n        this.name = 'StorageError';\n    }\n}\nexport function isStorageError(error) {\n    return typeof error === 'object' && error !== null && '__isStorageError' in error;\n}\nexport class StorageApiError extends StorageError {\n    constructor(message, status) {\n        super(message);\n        this.name = 'StorageApiError';\n        this.status = status;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n        };\n    }\n}\nexport class StorageUnknownError extends StorageError {\n    constructor(message, originalError) {\n        super(message);\n        this.name = 'StorageUnknownError';\n        this.originalError = originalError;\n    }\n}\n//# sourceMappingURL=errors.js.map", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nexport const resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => import('@supabase/node-fetch').then(({ default: fetch }) => fetch(...args));\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\nexport const resolveResponse = () => __awaiter(void 0, void 0, void 0, function* () {\n    if (typeof Response === 'undefined') {\n        // @ts-ignore\n        return (yield import('@supabase/node-fetch')).Response;\n    }\n    return Response;\n});\nexport const recursiveToCamel = (item) => {\n    if (Array.isArray(item)) {\n        return item.map((el) => recursiveToCamel(el));\n    }\n    else if (typeof item === 'function' || item !== Object(item)) {\n        return item;\n    }\n    const result = {};\n    Object.entries(item).forEach(([key, value]) => {\n        const newKey = key.replace(/([-_][a-z])/gi, (c) => c.toUpperCase().replace(/[-_]/g, ''));\n        result[newKey] = recursiveToCamel(value);\n    });\n    return result;\n};\n//# sourceMappingURL=helpers.js.map", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport { StorageApiError, StorageUnknownError } from './errors';\nimport { resolveResponse } from './helpers';\nconst _getErrorMessage = (err) => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst handleError = (error, reject, options) => __awaiter(void 0, void 0, void 0, function* () {\n    const Res = yield resolveResponse();\n    if (error instanceof Res && !(options === null || options === void 0 ? void 0 : options.noResolveJson)) {\n        error\n            .json()\n            .then((err) => {\n            reject(new StorageApiError(_getErrorMessage(err), error.status || 500));\n        })\n            .catch((err) => {\n            reject(new StorageUnknownError(_getErrorMessage(err), err));\n        });\n    }\n    else {\n        reject(new StorageUnknownError(_getErrorMessage(error), error));\n    }\n});\nconst _getRequestParams = (method, options, parameters, body) => {\n    const params = { method, headers: (options === null || options === void 0 ? void 0 : options.headers) || {} };\n    if (method === 'GET') {\n        return params;\n    }\n    params.headers = Object.assign({ 'Content-Type': 'application/json' }, options === null || options === void 0 ? void 0 : options.headers);\n    if (body) {\n        params.body = JSON.stringify(body);\n    }\n    return Object.assign(Object.assign({}, params), parameters);\n};\nfunction _handleRequest(fetcher, method, url, options, parameters, body) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return new Promise((resolve, reject) => {\n            fetcher(url, _getRequestParams(method, options, parameters, body))\n                .then((result) => {\n                if (!result.ok)\n                    throw result;\n                if (options === null || options === void 0 ? void 0 : options.noResolveJson)\n                    return result;\n                return result.json();\n            })\n                .then((data) => resolve(data))\n                .catch((error) => handleError(error, reject, options));\n        });\n    });\n}\nexport function get(fetcher, url, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'GET', url, options, parameters);\n    });\n}\nexport function post(fetcher, url, body, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'POST', url, options, parameters, body);\n    });\n}\nexport function put(fetcher, url, body, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'PUT', url, options, parameters, body);\n    });\n}\nexport function head(fetcher, url, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'HEAD', url, Object.assign(Object.assign({}, options), { noResolveJson: true }), parameters);\n    });\n}\nexport function remove(fetcher, url, body, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'DELETE', url, options, parameters, body);\n    });\n}\n//# sourceMappingURL=fetch.js.map", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport { isStorageError, StorageError, StorageUnknownError } from '../lib/errors';\nimport { get, head, post, remove } from '../lib/fetch';\nimport { recursiveToCamel, resolveFetch } from '../lib/helpers';\nconst DEFAULT_SEARCH_OPTIONS = {\n    limit: 100,\n    offset: 0,\n    sortBy: {\n        column: 'name',\n        order: 'asc',\n    },\n};\nconst DEFAULT_FILE_OPTIONS = {\n    cacheControl: '3600',\n    contentType: 'text/plain;charset=UTF-8',\n    upsert: false,\n};\nexport default class StorageFileApi {\n    constructor(url, headers = {}, bucketId, fetch) {\n        this.url = url;\n        this.headers = headers;\n        this.bucketId = bucketId;\n        this.fetch = resolveFetch(fetch);\n    }\n    /**\n     * Uploads a file to an existing bucket or replaces an existing file at the specified path with a new one.\n     *\n     * @param method HTTP method.\n     * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n     * @param fileBody The body of the file to be stored in the bucket.\n     */\n    uploadOrUpdate(method, path, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                let body;\n                const options = Object.assign(Object.assign({}, DEFAULT_FILE_OPTIONS), fileOptions);\n                let headers = Object.assign(Object.assign({}, this.headers), (method === 'POST' && { 'x-upsert': String(options.upsert) }));\n                const metadata = options.metadata;\n                if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n                    body = new FormData();\n                    body.append('cacheControl', options.cacheControl);\n                    if (metadata) {\n                        body.append('metadata', this.encodeMetadata(metadata));\n                    }\n                    body.append('', fileBody);\n                }\n                else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n                    body = fileBody;\n                    body.append('cacheControl', options.cacheControl);\n                    if (metadata) {\n                        body.append('metadata', this.encodeMetadata(metadata));\n                    }\n                }\n                else {\n                    body = fileBody;\n                    headers['cache-control'] = `max-age=${options.cacheControl}`;\n                    headers['content-type'] = options.contentType;\n                    if (metadata) {\n                        headers['x-metadata'] = this.toBase64(this.encodeMetadata(metadata));\n                    }\n                }\n                if (fileOptions === null || fileOptions === void 0 ? void 0 : fileOptions.headers) {\n                    headers = Object.assign(Object.assign({}, headers), fileOptions.headers);\n                }\n                const cleanPath = this._removeEmptyFolders(path);\n                const _path = this._getFinalPath(cleanPath);\n                const res = yield this.fetch(`${this.url}/object/${_path}`, Object.assign({ method, body: body, headers }, ((options === null || options === void 0 ? void 0 : options.duplex) ? { duplex: options.duplex } : {})));\n                const data = yield res.json();\n                if (res.ok) {\n                    return {\n                        data: { path: cleanPath, id: data.Id, fullPath: data.Key },\n                        error: null,\n                    };\n                }\n                else {\n                    const error = data;\n                    return { data: null, error };\n                }\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Uploads a file to an existing bucket.\n     *\n     * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n     * @param fileBody The body of the file to be stored in the bucket.\n     */\n    upload(path, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return this.uploadOrUpdate('POST', path, fileBody, fileOptions);\n        });\n    }\n    /**\n     * Upload a file with a token generated from `createSignedUploadUrl`.\n     * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n     * @param token The token generated from `createSignedUploadUrl`\n     * @param fileBody The body of the file to be stored in the bucket.\n     */\n    uploadToSignedUrl(path, token, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const cleanPath = this._removeEmptyFolders(path);\n            const _path = this._getFinalPath(cleanPath);\n            const url = new URL(this.url + `/object/upload/sign/${_path}`);\n            url.searchParams.set('token', token);\n            try {\n                let body;\n                const options = Object.assign({ upsert: DEFAULT_FILE_OPTIONS.upsert }, fileOptions);\n                const headers = Object.assign(Object.assign({}, this.headers), { 'x-upsert': String(options.upsert) });\n                if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n                    body = new FormData();\n                    body.append('cacheControl', options.cacheControl);\n                    body.append('', fileBody);\n                }\n                else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n                    body = fileBody;\n                    body.append('cacheControl', options.cacheControl);\n                }\n                else {\n                    body = fileBody;\n                    headers['cache-control'] = `max-age=${options.cacheControl}`;\n                    headers['content-type'] = options.contentType;\n                }\n                const res = yield this.fetch(url.toString(), {\n                    method: 'PUT',\n                    body: body,\n                    headers,\n                });\n                const data = yield res.json();\n                if (res.ok) {\n                    return {\n                        data: { path: cleanPath, fullPath: data.Key },\n                        error: null,\n                    };\n                }\n                else {\n                    const error = data;\n                    return { data: null, error };\n                }\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates a signed upload URL.\n     * Signed upload URLs can be used to upload files to the bucket without further authentication.\n     * They are valid for 2 hours.\n     * @param path The file path, including the current file name. For example `folder/image.png`.\n     * @param options.upsert If set to true, allows the file to be overwritten if it already exists.\n     */\n    createSignedUploadUrl(path, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                let _path = this._getFinalPath(path);\n                const headers = Object.assign({}, this.headers);\n                if (options === null || options === void 0 ? void 0 : options.upsert) {\n                    headers['x-upsert'] = 'true';\n                }\n                const data = yield post(this.fetch, `${this.url}/object/upload/sign/${_path}`, {}, { headers });\n                const url = new URL(this.url + data.url);\n                const token = url.searchParams.get('token');\n                if (!token) {\n                    throw new StorageError('No token returned by API');\n                }\n                return { data: { signedUrl: url.toString(), path, token }, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Replaces an existing file at the specified path with a new one.\n     *\n     * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to update.\n     * @param fileBody The body of the file to be stored in the bucket.\n     */\n    update(path, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return this.uploadOrUpdate('PUT', path, fileBody, fileOptions);\n        });\n    }\n    /**\n     * Moves an existing file to a new path in the same bucket.\n     *\n     * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n     * @param toPath The new file path, including the new file name. For example `folder/image-new.png`.\n     * @param options The destination options.\n     */\n    move(fromPath, toPath, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield post(this.fetch, `${this.url}/object/move`, {\n                    bucketId: this.bucketId,\n                    sourceKey: fromPath,\n                    destinationKey: toPath,\n                    destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket,\n                }, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Copies an existing file to a new path in the same bucket.\n     *\n     * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n     * @param toPath The new file path, including the new file name. For example `folder/image-copy.png`.\n     * @param options The destination options.\n     */\n    copy(fromPath, toPath, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield post(this.fetch, `${this.url}/object/copy`, {\n                    bucketId: this.bucketId,\n                    sourceKey: fromPath,\n                    destinationKey: toPath,\n                    destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket,\n                }, { headers: this.headers });\n                return { data: { path: data.Key }, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates a signed URL. Use a signed URL to share a file for a fixed amount of time.\n     *\n     * @param path The file path, including the current file name. For example `folder/image.png`.\n     * @param expiresIn The number of seconds until the signed URL expires. For example, `60` for a URL which is valid for one minute.\n     * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n     * @param options.transform Transform the asset before serving it to the client.\n     */\n    createSignedUrl(path, expiresIn, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                let _path = this._getFinalPath(path);\n                let data = yield post(this.fetch, `${this.url}/object/sign/${_path}`, Object.assign({ expiresIn }, ((options === null || options === void 0 ? void 0 : options.transform) ? { transform: options.transform } : {})), { headers: this.headers });\n                const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download)\n                    ? `&download=${options.download === true ? '' : options.download}`\n                    : '';\n                const signedUrl = encodeURI(`${this.url}${data.signedURL}${downloadQueryParam}`);\n                data = { signedUrl };\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates multiple signed URLs. Use a signed URL to share a file for a fixed amount of time.\n     *\n     * @param paths The file paths to be downloaded, including the current file names. For example `['folder/image.png', 'folder2/image2.png']`.\n     * @param expiresIn The number of seconds until the signed URLs expire. For example, `60` for URLs which are valid for one minute.\n     * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n     */\n    createSignedUrls(paths, expiresIn, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield post(this.fetch, `${this.url}/object/sign/${this.bucketId}`, { expiresIn, paths }, { headers: this.headers });\n                const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download)\n                    ? `&download=${options.download === true ? '' : options.download}`\n                    : '';\n                return {\n                    data: data.map((datum) => (Object.assign(Object.assign({}, datum), { signedUrl: datum.signedURL\n                            ? encodeURI(`${this.url}${datum.signedURL}${downloadQueryParam}`)\n                            : null }))),\n                    error: null,\n                };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Downloads a file from a private bucket. For public buckets, make a request to the URL returned from `getPublicUrl` instead.\n     *\n     * @param path The full path and file name of the file to be downloaded. For example `folder/image.png`.\n     * @param options.transform Transform the asset before serving it to the client.\n     */\n    download(path, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== 'undefined';\n            const renderPath = wantsTransformation ? 'render/image/authenticated' : 'object';\n            const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n            const queryString = transformationQuery ? `?${transformationQuery}` : '';\n            try {\n                const _path = this._getFinalPath(path);\n                const res = yield get(this.fetch, `${this.url}/${renderPath}/${_path}${queryString}`, {\n                    headers: this.headers,\n                    noResolveJson: true,\n                });\n                const data = yield res.blob();\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Retrieves the details of an existing file.\n     * @param path\n     */\n    info(path) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const _path = this._getFinalPath(path);\n            try {\n                const data = yield get(this.fetch, `${this.url}/object/info/${_path}`, {\n                    headers: this.headers,\n                });\n                return { data: recursiveToCamel(data), error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Checks the existence of a file.\n     * @param path\n     */\n    exists(path) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const _path = this._getFinalPath(path);\n            try {\n                yield head(this.fetch, `${this.url}/object/${_path}`, {\n                    headers: this.headers,\n                });\n                return { data: true, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error) && error instanceof StorageUnknownError) {\n                    const originalError = error.originalError;\n                    if ([400, 404].includes(originalError === null || originalError === void 0 ? void 0 : originalError.status)) {\n                        return { data: false, error };\n                    }\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * A simple convenience function to get the URL for an asset in a public bucket. If you do not want to use this function, you can construct the public URL by concatenating the bucket URL with the path to the asset.\n     * This function does not verify if the bucket is public. If a public URL is created for a bucket which is not public, you will not be able to download the asset.\n     *\n     * @param path The path and name of the file to generate the public URL for. For example `folder/image.png`.\n     * @param options.download Triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n     * @param options.transform Transform the asset before serving it to the client.\n     */\n    getPublicUrl(path, options) {\n        const _path = this._getFinalPath(path);\n        const _queryString = [];\n        const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download)\n            ? `download=${options.download === true ? '' : options.download}`\n            : '';\n        if (downloadQueryParam !== '') {\n            _queryString.push(downloadQueryParam);\n        }\n        const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== 'undefined';\n        const renderPath = wantsTransformation ? 'render/image' : 'object';\n        const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n        if (transformationQuery !== '') {\n            _queryString.push(transformationQuery);\n        }\n        let queryString = _queryString.join('&');\n        if (queryString !== '') {\n            queryString = `?${queryString}`;\n        }\n        return {\n            data: { publicUrl: encodeURI(`${this.url}/${renderPath}/public/${_path}${queryString}`) },\n        };\n    }\n    /**\n     * Deletes files within the same bucket\n     *\n     * @param paths An array of files to delete, including the path and file name. For example [`'folder/image.png'`].\n     */\n    remove(paths) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield remove(this.fetch, `${this.url}/object/${this.bucketId}`, { prefixes: paths }, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Get file metadata\n     * @param id the file id to retrieve metadata\n     */\n    // async getMetadata(\n    //   id: string\n    // ): Promise<\n    //   | {\n    //       data: Metadata\n    //       error: null\n    //     }\n    //   | {\n    //       data: null\n    //       error: StorageError\n    //     }\n    // > {\n    //   try {\n    //     const data = await get(this.fetch, `${this.url}/metadata/${id}`, { headers: this.headers })\n    //     return { data, error: null }\n    //   } catch (error) {\n    //     if (isStorageError(error)) {\n    //       return { data: null, error }\n    //     }\n    //     throw error\n    //   }\n    // }\n    /**\n     * Update file metadata\n     * @param id the file id to update metadata\n     * @param meta the new file metadata\n     */\n    // async updateMetadata(\n    //   id: string,\n    //   meta: Metadata\n    // ): Promise<\n    //   | {\n    //       data: Metadata\n    //       error: null\n    //     }\n    //   | {\n    //       data: null\n    //       error: StorageError\n    //     }\n    // > {\n    //   try {\n    //     const data = await post(\n    //       this.fetch,\n    //       `${this.url}/metadata/${id}`,\n    //       { ...meta },\n    //       { headers: this.headers }\n    //     )\n    //     return { data, error: null }\n    //   } catch (error) {\n    //     if (isStorageError(error)) {\n    //       return { data: null, error }\n    //     }\n    //     throw error\n    //   }\n    // }\n    /**\n     * Lists all the files within a bucket.\n     * @param path The folder path.\n     */\n    list(path, options, parameters) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const body = Object.assign(Object.assign(Object.assign({}, DEFAULT_SEARCH_OPTIONS), options), { prefix: path || '' });\n                const data = yield post(this.fetch, `${this.url}/object/list/${this.bucketId}`, body, { headers: this.headers }, parameters);\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    encodeMetadata(metadata) {\n        return JSON.stringify(metadata);\n    }\n    toBase64(data) {\n        if (typeof Buffer !== 'undefined') {\n            return Buffer.from(data).toString('base64');\n        }\n        return btoa(data);\n    }\n    _getFinalPath(path) {\n        return `${this.bucketId}/${path}`;\n    }\n    _removeEmptyFolders(path) {\n        return path.replace(/^\\/|\\/$/g, '').replace(/\\/+/g, '/');\n    }\n    transformOptsToQueryString(transform) {\n        const params = [];\n        if (transform.width) {\n            params.push(`width=${transform.width}`);\n        }\n        if (transform.height) {\n            params.push(`height=${transform.height}`);\n        }\n        if (transform.resize) {\n            params.push(`resize=${transform.resize}`);\n        }\n        if (transform.format) {\n            params.push(`format=${transform.format}`);\n        }\n        if (transform.quality) {\n            params.push(`quality=${transform.quality}`);\n        }\n        return params.join('&');\n    }\n}\n//# sourceMappingURL=StorageFileApi.js.map", "// generated by genversion\nexport const version = '2.7.1';\n//# sourceMappingURL=version.js.map", "import { version } from './version';\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `storage-js/${version}` };\n//# sourceMappingURL=constants.js.map", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport { DEFAULT_HEADERS } from '../lib/constants';\nimport { isStorageError } from '../lib/errors';\nimport { get, post, put, remove } from '../lib/fetch';\nimport { resolveFetch } from '../lib/helpers';\nexport default class StorageBucketApi {\n    constructor(url, headers = {}, fetch) {\n        this.url = url;\n        this.headers = Object.assign(Object.assign({}, DEFAULT_HEADERS), headers);\n        this.fetch = resolveFetch(fetch);\n    }\n    /**\n     * Retrieves the details of all Storage buckets within an existing project.\n     */\n    listBuckets() {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield get(this.fetch, `${this.url}/bucket`, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Retrieves the details of an existing Storage bucket.\n     *\n     * @param id The unique identifier of the bucket you would like to retrieve.\n     */\n    getBucket(id) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield get(this.fetch, `${this.url}/bucket/${id}`, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates a new Storage bucket\n     *\n     * @param id A unique identifier for the bucket you are creating.\n     * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private.\n     * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n     * The global file size limit takes precedence over this value.\n     * The default value is null, which doesn't set a per bucket file size limit.\n     * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n     * The default value is null, which allows files with all mime types to be uploaded.\n     * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n     * @returns newly created bucket id\n     */\n    createBucket(id, options = {\n        public: false,\n    }) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield post(this.fetch, `${this.url}/bucket`, {\n                    id,\n                    name: id,\n                    public: options.public,\n                    file_size_limit: options.fileSizeLimit,\n                    allowed_mime_types: options.allowedMimeTypes,\n                }, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Updates a Storage bucket\n     *\n     * @param id A unique identifier for the bucket you are updating.\n     * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations.\n     * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n     * The global file size limit takes precedence over this value.\n     * The default value is null, which doesn't set a per bucket file size limit.\n     * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n     * The default value is null, which allows files with all mime types to be uploaded.\n     * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n     */\n    updateBucket(id, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield put(this.fetch, `${this.url}/bucket/${id}`, {\n                    id,\n                    name: id,\n                    public: options.public,\n                    file_size_limit: options.fileSizeLimit,\n                    allowed_mime_types: options.allowedMimeTypes,\n                }, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Removes all objects inside a single bucket.\n     *\n     * @param id The unique identifier of the bucket you would like to empty.\n     */\n    emptyBucket(id) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield post(this.fetch, `${this.url}/bucket/${id}/empty`, {}, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Deletes an existing bucket. A bucket can't be deleted with existing objects inside it.\n     * You must first `empty()` the bucket.\n     *\n     * @param id The unique identifier of the bucket you would like to delete.\n     */\n    deleteBucket(id) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield remove(this.fetch, `${this.url}/bucket/${id}`, {}, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n}\n//# sourceMappingURL=StorageBucketApi.js.map", "import StorageFileApi from './packages/StorageFileApi';\nimport StorageBucketApi from './packages/StorageBucketApi';\nexport class StorageClient extends StorageBucketApi {\n    constructor(url, headers = {}, fetch) {\n        super(url, headers, fetch);\n    }\n    /**\n     * Perform file operation in a bucket.\n     *\n     * @param id The bucket id to operate on.\n     */\n    from(id) {\n        return new StorageFileApi(this.url, this.headers, id, this.fetch);\n    }\n}\n//# sourceMappingURL=StorageClient.js.map", null, null, null, null, "export const version = '2.70.0';\n//# sourceMappingURL=version.js.map", "import { version } from './version';\n/** Current session will be checked for refresh at this interval. */\nexport const AUTO_REFRESH_TICK_DURATION_MS = 30 * 1000;\n/**\n * A token refresh will be attempted this many ticks before the current session expires. */\nexport const AUTO_REFRESH_TICK_THRESHOLD = 3;\n/*\n * Earliest time before an access token expires that the session should be refreshed.\n */\nexport const EXPIRY_MARGIN_MS = AUTO_REFRESH_TICK_THRESHOLD * AUTO_REFRESH_TICK_DURATION_MS;\nexport const GOTRUE_URL = 'http://localhost:9999';\nexport const STORAGE_KEY = 'supabase.auth.token';\nexport const AUDIENCE = '';\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `gotrue-js/${version}` };\nexport const NETWORK_FAILURE = {\n    MAX_RETRIES: 10,\n    RETRY_INTERVAL: 2, // in deciseconds\n};\nexport const API_VERSION_HEADER_NAME = 'X-Supabase-Api-Version';\nexport const API_VERSIONS = {\n    '2024-01-01': {\n        timestamp: Date.parse('2024-01-01T00:00:00.0Z'),\n        name: '2024-01-01',\n    },\n};\nexport const BASE64URL_REGEX = /^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;\nexport const JWKS_TTL = 600000; // 10 minutes\n//# sourceMappingURL=constants.js.map", "export class AuthError extends Error {\n    constructor(message, status, code) {\n        super(message);\n        this.__isAuthError = true;\n        this.name = 'AuthError';\n        this.status = status;\n        this.code = code;\n    }\n}\nexport function isAuthError(error) {\n    return typeof error === 'object' && error !== null && '__isAuthError' in error;\n}\nexport class AuthApiError extends AuthError {\n    constructor(message, status, code) {\n        super(message, status, code);\n        this.name = 'AuthApiError';\n        this.status = status;\n        this.code = code;\n    }\n}\nexport function isAuthApiError(error) {\n    return isAuthError(error) && error.name === 'AuthApiError';\n}\nexport class AuthUnknownError extends AuthError {\n    constructor(message, originalError) {\n        super(message);\n        this.name = 'AuthUnknownError';\n        this.originalError = originalError;\n    }\n}\nexport class CustomAuthError extends AuthError {\n    constructor(message, name, status, code) {\n        super(message, status, code);\n        this.name = name;\n        this.status = status;\n    }\n}\nexport class AuthSessionMissingError extends CustomAuthError {\n    constructor() {\n        super('Auth session missing!', 'AuthSessionMissingError', 400, undefined);\n    }\n}\nexport function isAuthSessionMissingError(error) {\n    return isAuthError(error) && error.name === 'AuthSessionMissingError';\n}\nexport class AuthInvalidTokenResponseError extends CustomAuthError {\n    constructor() {\n        super('Auth session or user missing', 'AuthInvalidTokenResponseError', 500, undefined);\n    }\n}\nexport class AuthInvalidCredentialsError extends CustomAuthError {\n    constructor(message) {\n        super(message, 'AuthInvalidCredentialsError', 400, undefined);\n    }\n}\nexport class AuthImplicitGrantRedirectError extends CustomAuthError {\n    constructor(message, details = null) {\n        super(message, 'AuthImplicitGrantRedirectError', 500, undefined);\n        this.details = null;\n        this.details = details;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n            details: this.details,\n        };\n    }\n}\nexport function isAuthImplicitGrantRedirectError(error) {\n    return isAuthError(error) && error.name === 'AuthImplicitGrantRedirectError';\n}\nexport class AuthPKCEGrantCodeExchangeError extends CustomAuthError {\n    constructor(message, details = null) {\n        super(message, 'AuthPKCEGrantCodeExchangeError', 500, undefined);\n        this.details = null;\n        this.details = details;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n            details: this.details,\n        };\n    }\n}\nexport class AuthRetryableFetchError extends CustomAuthError {\n    constructor(message, status) {\n        super(message, 'AuthRetryableFetchError', status, undefined);\n    }\n}\nexport function isAuthRetryableFetchError(error) {\n    return isAuthError(error) && error.name === 'AuthRetryableFetchError';\n}\n/**\n * This error is thrown on certain methods when the password used is deemed\n * weak. Inspect the reasons to identify what password strength rules are\n * inadequate.\n */\nexport class AuthWeakPasswordError extends CustomAuthError {\n    constructor(message, status, reasons) {\n        super(message, 'AuthWeakPasswordError', status, 'weak_password');\n        this.reasons = reasons;\n    }\n}\nexport function isAuthWeakPasswordError(error) {\n    return isAuthError(error) && error.name === 'AuthWeakPasswordError';\n}\nexport class AuthInvalidJwtError extends CustomAuthError {\n    constructor(message) {\n        super(message, 'AuthInvalidJwtError', 400, 'invalid_jwt');\n    }\n}\n//# sourceMappingURL=errors.js.map", "/**\n * Avoid modifying this file. It's part of\n * https://github.com/supabase-community/base64url-js.  Submit all fixes on\n * that repo!\n */\n/**\n * An array of characters that encode 6 bits into a Base64-URL alphabet\n * character.\n */\nconst TO_BASE64URL = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'.split('');\n/**\n * An array of characters that can appear in a Base64-URL encoded string but\n * should be ignored.\n */\nconst IGNORE_BASE64URL = ' \\t\\n\\r='.split('');\n/**\n * An array of 128 numbers that map a Base64-URL character to 6 bits, or if -2\n * used to skip the character, or if -1 used to error out.\n */\nconst FROM_BASE64URL = (() => {\n    const charMap = new Array(128);\n    for (let i = 0; i < charMap.length; i += 1) {\n        charMap[i] = -1;\n    }\n    for (let i = 0; i < IGNORE_BASE64URL.length; i += 1) {\n        charMap[IGNORE_BASE64URL[i].charCodeAt(0)] = -2;\n    }\n    for (let i = 0; i < TO_BASE64URL.length; i += 1) {\n        charMap[TO_BASE64URL[i].charCodeAt(0)] = i;\n    }\n    return charMap;\n})();\n/**\n * Converts a byte to a Base64-URL string.\n *\n * @param byte The byte to convert, or null to flush at the end of the byte sequence.\n * @param state The Base64 conversion state. Pass an initial value of `{ queue: 0, queuedBits: 0 }`.\n * @param emit A function called with the next Base64 character when ready.\n */\nexport function byteToBase64URL(byte, state, emit) {\n    if (byte !== null) {\n        state.queue = (state.queue << 8) | byte;\n        state.queuedBits += 8;\n        while (state.queuedBits >= 6) {\n            const pos = (state.queue >> (state.queuedBits - 6)) & 63;\n            emit(TO_BASE64URL[pos]);\n            state.queuedBits -= 6;\n        }\n    }\n    else if (state.queuedBits > 0) {\n        state.queue = state.queue << (6 - state.queuedBits);\n        state.queuedBits = 6;\n        while (state.queuedBits >= 6) {\n            const pos = (state.queue >> (state.queuedBits - 6)) & 63;\n            emit(TO_BASE64URL[pos]);\n            state.queuedBits -= 6;\n        }\n    }\n}\n/**\n * Converts a String char code (extracted using `string.charCodeAt(position)`) to a sequence of Base64-URL characters.\n *\n * @param charCode The char code of the JavaScript string.\n * @param state The Base64 state. Pass an initial value of `{ queue: 0, queuedBits: 0 }`.\n * @param emit A function called with the next byte.\n */\nexport function byteFromBase64URL(charCode, state, emit) {\n    const bits = FROM_BASE64URL[charCode];\n    if (bits > -1) {\n        // valid Base64-URL character\n        state.queue = (state.queue << 6) | bits;\n        state.queuedBits += 6;\n        while (state.queuedBits >= 8) {\n            emit((state.queue >> (state.queuedBits - 8)) & 0xff);\n            state.queuedBits -= 8;\n        }\n    }\n    else if (bits === -2) {\n        // ignore spaces, tabs, newlines, =\n        return;\n    }\n    else {\n        throw new Error(`Invalid Base64-URL character \"${String.fromCharCode(charCode)}\"`);\n    }\n}\n/**\n * Converts a JavaScript string (which may include any valid character) into a\n * Base64-URL encoded string. The string is first encoded in UTF-8 which is\n * then encoded as Base64-URL.\n *\n * @param str The string to convert.\n */\nexport function stringToBase64URL(str) {\n    const base64 = [];\n    const emitter = (char) => {\n        base64.push(char);\n    };\n    const state = { queue: 0, queuedBits: 0 };\n    stringToUTF8(str, (byte) => {\n        byteToBase64URL(byte, state, emitter);\n    });\n    byteToBase64URL(null, state, emitter);\n    return base64.join('');\n}\n/**\n * Converts a Base64-URL encoded string into a JavaScript string. It is assumed\n * that the underlying string has been encoded as UTF-8.\n *\n * @param str The Base64-URL encoded string.\n */\nexport function stringFromBase64URL(str) {\n    const conv = [];\n    const utf8Emit = (codepoint) => {\n        conv.push(String.fromCodePoint(codepoint));\n    };\n    const utf8State = {\n        utf8seq: 0,\n        codepoint: 0,\n    };\n    const b64State = { queue: 0, queuedBits: 0 };\n    const byteEmit = (byte) => {\n        stringFromUTF8(byte, utf8State, utf8Emit);\n    };\n    for (let i = 0; i < str.length; i += 1) {\n        byteFromBase64URL(str.charCodeAt(i), b64State, byteEmit);\n    }\n    return conv.join('');\n}\n/**\n * Converts a Unicode codepoint to a multi-byte UTF-8 sequence.\n *\n * @param codepoint The Unicode codepoint.\n * @param emit      Function which will be called for each UTF-8 byte that represents the codepoint.\n */\nexport function codepointToUTF8(codepoint, emit) {\n    if (codepoint <= 0x7f) {\n        emit(codepoint);\n        return;\n    }\n    else if (codepoint <= 0x7ff) {\n        emit(0xc0 | (codepoint >> 6));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0xffff) {\n        emit(0xe0 | (codepoint >> 12));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0x10ffff) {\n        emit(0xf0 | (codepoint >> 18));\n        emit(0x80 | ((codepoint >> 12) & 0x3f));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    throw new Error(`Unrecognized Unicode codepoint: ${codepoint.toString(16)}`);\n}\n/**\n * Converts a JavaScript string to a sequence of UTF-8 bytes.\n *\n * @param str  The string to convert to UTF-8.\n * @param emit Function which will be called for each UTF-8 byte of the string.\n */\nexport function stringToUTF8(str, emit) {\n    for (let i = 0; i < str.length; i += 1) {\n        let codepoint = str.charCodeAt(i);\n        if (codepoint > 0xd7ff && codepoint <= 0xdbff) {\n            // most UTF-16 codepoints are Unicode codepoints, except values in this\n            // range where the next UTF-16 codepoint needs to be combined with the\n            // current one to get the Unicode codepoint\n            const highSurrogate = ((codepoint - 0xd800) * 0x400) & 0xffff;\n            const lowSurrogate = (str.charCodeAt(i + 1) - 0xdc00) & 0xffff;\n            codepoint = (lowSurrogate | highSurrogate) + 0x10000;\n            i += 1;\n        }\n        codepointToUTF8(codepoint, emit);\n    }\n}\n/**\n * Converts a UTF-8 byte to a Unicode codepoint.\n *\n * @param byte  The UTF-8 byte next in the sequence.\n * @param state The shared state between consecutive UTF-8 bytes in the\n *              sequence, an object with the shape `{ utf8seq: 0, codepoint: 0 }`.\n * @param emit  Function which will be called for each codepoint.\n */\nexport function stringFromUTF8(byte, state, emit) {\n    if (state.utf8seq === 0) {\n        if (byte <= 0x7f) {\n            emit(byte);\n            return;\n        }\n        // count the number of 1 leading bits until you reach 0\n        for (let leadingBit = 1; leadingBit < 6; leadingBit += 1) {\n            if (((byte >> (7 - leadingBit)) & 1) === 0) {\n                state.utf8seq = leadingBit;\n                break;\n            }\n        }\n        if (state.utf8seq === 2) {\n            state.codepoint = byte & 31;\n        }\n        else if (state.utf8seq === 3) {\n            state.codepoint = byte & 15;\n        }\n        else if (state.utf8seq === 4) {\n            state.codepoint = byte & 7;\n        }\n        else {\n            throw new Error('Invalid UTF-8 sequence');\n        }\n        state.utf8seq -= 1;\n    }\n    else if (state.utf8seq > 0) {\n        if (byte <= 0x7f) {\n            throw new Error('Invalid UTF-8 sequence');\n        }\n        state.codepoint = (state.codepoint << 6) | (byte & 63);\n        state.utf8seq -= 1;\n        if (state.utf8seq === 0) {\n            emit(state.codepoint);\n        }\n    }\n}\n/**\n * Helper functions to convert different types of strings to Uint8Array\n */\nexport function base64UrlToUint8Array(str) {\n    const result = [];\n    const state = { queue: 0, queuedBits: 0 };\n    const onByte = (byte) => {\n        result.push(byte);\n    };\n    for (let i = 0; i < str.length; i += 1) {\n        byteFromBase64URL(str.charCodeAt(i), state, onByte);\n    }\n    return new Uint8Array(result);\n}\nexport function stringToUint8Array(str) {\n    const result = [];\n    stringToUTF8(str, (byte) => result.push(byte));\n    return new Uint8Array(result);\n}\nexport function bytesToBase64URL(bytes) {\n    const result = [];\n    const state = { queue: 0, queuedBits: 0 };\n    const onChar = (char) => {\n        result.push(char);\n    };\n    bytes.forEach((byte) => byteToBase64URL(byte, state, onChar));\n    // always call with `null` after processing all bytes\n    byteToBase64URL(null, state, onChar);\n    return result.join('');\n}\n//# sourceMappingURL=base64url.js.map", "import { API_VERSION_HEADER_NAME, BASE64URL_REGEX } from './constants';\nimport { AuthInvalidJwtError } from './errors';\nimport { base64UrlToUint8Array, stringFromBase64URL } from './base64url';\nexport function expiresAt(expiresIn) {\n    const timeNow = Math.round(Date.now() / 1000);\n    return timeNow + expiresIn;\n}\nexport function uuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        const r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n}\nexport const isBrowser = () => typeof window !== 'undefined' && typeof document !== 'undefined';\nconst localStorageWriteTests = {\n    tested: false,\n    writable: false,\n};\n/**\n * Checks whether localStorage is supported on this browser.\n */\nexport const supportsLocalStorage = () => {\n    if (!isBrowser()) {\n        return false;\n    }\n    try {\n        if (typeof globalThis.localStorage !== 'object') {\n            return false;\n        }\n    }\n    catch (e) {\n        // DOM exception when accessing `localStorage`\n        return false;\n    }\n    if (localStorageWriteTests.tested) {\n        return localStorageWriteTests.writable;\n    }\n    const randomKey = `lswt-${Math.random()}${Math.random()}`;\n    try {\n        globalThis.localStorage.setItem(randomKey, randomKey);\n        globalThis.localStorage.removeItem(randomKey);\n        localStorageWriteTests.tested = true;\n        localStorageWriteTests.writable = true;\n    }\n    catch (e) {\n        // localStorage can't be written to\n        // https://www.chromium.org/for-testers/bug-reporting-guidelines/uncaught-securityerror-failed-to-read-the-localstorage-property-from-window-access-is-denied-for-this-document\n        localStorageWriteTests.tested = true;\n        localStorageWriteTests.writable = false;\n    }\n    return localStorageWriteTests.writable;\n};\n/**\n * Extracts parameters encoded in the URL both in the query and fragment.\n */\nexport function parseParametersFromURL(href) {\n    const result = {};\n    const url = new URL(href);\n    if (url.hash && url.hash[0] === '#') {\n        try {\n            const hashSearchParams = new URLSearchParams(url.hash.substring(1));\n            hashSearchParams.forEach((value, key) => {\n                result[key] = value;\n            });\n        }\n        catch (e) {\n            // hash is not a query string\n        }\n    }\n    // search parameters take precedence over hash parameters\n    url.searchParams.forEach((value, key) => {\n        result[key] = value;\n    });\n    return result;\n}\nexport const resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => import('@supabase/node-fetch').then(({ default: fetch }) => fetch(...args));\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\nexport const looksLikeFetchResponse = (maybeResponse) => {\n    return (typeof maybeResponse === 'object' &&\n        maybeResponse !== null &&\n        'status' in maybeResponse &&\n        'ok' in maybeResponse &&\n        'json' in maybeResponse &&\n        typeof maybeResponse.json === 'function');\n};\n// Storage helpers\nexport const setItemAsync = async (storage, key, data) => {\n    await storage.setItem(key, JSON.stringify(data));\n};\nexport const getItemAsync = async (storage, key) => {\n    const value = await storage.getItem(key);\n    if (!value) {\n        return null;\n    }\n    try {\n        return JSON.parse(value);\n    }\n    catch (_a) {\n        return value;\n    }\n};\nexport const removeItemAsync = async (storage, key) => {\n    await storage.removeItem(key);\n};\n/**\n * A deferred represents some asynchronous work that is not yet finished, which\n * may or may not culminate in a value.\n * Taken from: https://github.com/mike-north/types/blob/master/src/async.ts\n */\nexport class Deferred {\n    constructor() {\n        // eslint-disable-next-line @typescript-eslint/no-extra-semi\n        ;\n        this.promise = new Deferred.promiseConstructor((res, rej) => {\n            // eslint-disable-next-line @typescript-eslint/no-extra-semi\n            ;\n            this.resolve = res;\n            this.reject = rej;\n        });\n    }\n}\nDeferred.promiseConstructor = Promise;\nexport function decodeJWT(token) {\n    const parts = token.split('.');\n    if (parts.length !== 3) {\n        throw new AuthInvalidJwtError('Invalid JWT structure');\n    }\n    // Regex checks for base64url format\n    for (let i = 0; i < parts.length; i++) {\n        if (!BASE64URL_REGEX.test(parts[i])) {\n            throw new AuthInvalidJwtError('JWT not in base64url format');\n        }\n    }\n    const data = {\n        // using base64url lib\n        header: JSON.parse(stringFromBase64URL(parts[0])),\n        payload: JSON.parse(stringFromBase64URL(parts[1])),\n        signature: base64UrlToUint8Array(parts[2]),\n        raw: {\n            header: parts[0],\n            payload: parts[1],\n        },\n    };\n    return data;\n}\n/**\n * Creates a promise that resolves to null after some time.\n */\nexport async function sleep(time) {\n    return await new Promise((accept) => {\n        setTimeout(() => accept(null), time);\n    });\n}\n/**\n * Converts the provided async function into a retryable function. Each result\n * or thrown error is sent to the isRetryable function which should return true\n * if the function should run again.\n */\nexport function retryable(fn, isRetryable) {\n    const promise = new Promise((accept, reject) => {\n        // eslint-disable-next-line @typescript-eslint/no-extra-semi\n        ;\n        (async () => {\n            for (let attempt = 0; attempt < Infinity; attempt++) {\n                try {\n                    const result = await fn(attempt);\n                    if (!isRetryable(attempt, null, result)) {\n                        accept(result);\n                        return;\n                    }\n                }\n                catch (e) {\n                    if (!isRetryable(attempt, e)) {\n                        reject(e);\n                        return;\n                    }\n                }\n            }\n        })();\n    });\n    return promise;\n}\nfunction dec2hex(dec) {\n    return ('0' + dec.toString(16)).substr(-2);\n}\n// Functions below taken from: https://stackoverflow.com/questions/63309409/creating-a-code-verifier-and-challenge-for-pkce-auth-on-spotify-api-in-reactjs\nexport function generatePKCEVerifier() {\n    const verifierLength = 56;\n    const array = new Uint32Array(verifierLength);\n    if (typeof crypto === 'undefined') {\n        const charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';\n        const charSetLen = charSet.length;\n        let verifier = '';\n        for (let i = 0; i < verifierLength; i++) {\n            verifier += charSet.charAt(Math.floor(Math.random() * charSetLen));\n        }\n        return verifier;\n    }\n    crypto.getRandomValues(array);\n    return Array.from(array, dec2hex).join('');\n}\nasync function sha256(randomString) {\n    const encoder = new TextEncoder();\n    const encodedData = encoder.encode(randomString);\n    const hash = await crypto.subtle.digest('SHA-256', encodedData);\n    const bytes = new Uint8Array(hash);\n    return Array.from(bytes)\n        .map((c) => String.fromCharCode(c))\n        .join('');\n}\nexport async function generatePKCEChallenge(verifier) {\n    const hasCryptoSupport = typeof crypto !== 'undefined' &&\n        typeof crypto.subtle !== 'undefined' &&\n        typeof TextEncoder !== 'undefined';\n    if (!hasCryptoSupport) {\n        console.warn('WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256.');\n        return verifier;\n    }\n    const hashed = await sha256(verifier);\n    return btoa(hashed).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=+$/, '');\n}\nexport async function getCodeChallengeAndMethod(storage, storageKey, isPasswordRecovery = false) {\n    const codeVerifier = generatePKCEVerifier();\n    let storedCodeVerifier = codeVerifier;\n    if (isPasswordRecovery) {\n        storedCodeVerifier += '/PASSWORD_RECOVERY';\n    }\n    await setItemAsync(storage, `${storageKey}-code-verifier`, storedCodeVerifier);\n    const codeChallenge = await generatePKCEChallenge(codeVerifier);\n    const codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n    return [codeChallenge, codeChallengeMethod];\n}\n/** Parses the API version which is 2YYY-MM-DD. */\nconst API_VERSION_REGEX = /^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;\nexport function parseResponseAPIVersion(response) {\n    const apiVersion = response.headers.get(API_VERSION_HEADER_NAME);\n    if (!apiVersion) {\n        return null;\n    }\n    if (!apiVersion.match(API_VERSION_REGEX)) {\n        return null;\n    }\n    try {\n        const date = new Date(`${apiVersion}T00:00:00.0Z`);\n        return date;\n    }\n    catch (e) {\n        return null;\n    }\n}\nexport function validateExp(exp) {\n    if (!exp) {\n        throw new Error('Missing exp claim');\n    }\n    const timeNow = Math.floor(Date.now() / 1000);\n    if (exp <= timeNow) {\n        throw new Error('JWT has expired');\n    }\n}\nexport function getAlgorithm(alg) {\n    switch (alg) {\n        case 'RS256':\n            return {\n                name: 'RSASSA-PKCS1-v1_5',\n                hash: { name: 'SHA-256' },\n            };\n        case 'ES256':\n            return {\n                name: 'ECDSA',\n                namedCurve: 'P-256',\n                hash: { name: 'SHA-256' },\n            };\n        default:\n            throw new Error('Invalid alg claim');\n    }\n}\nconst UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;\nexport function validateUUID(str) {\n    if (!UUID_REGEX.test(str)) {\n        throw new Error('@supabase/auth-js: Expected parameter to be UUID but is not');\n    }\n}\n//# sourceMappingURL=helpers.js.map", "var __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nimport { API_VERSIONS, API_VERSION_HEADER_NAME } from './constants';\nimport { expiresAt, looksLikeFetchResponse, parseResponseAPIVersion } from './helpers';\nimport { AuthApiError, AuthRetryableFetchError, AuthWeakPasswordError, AuthUnknownError, AuthSessionMissingError, } from './errors';\nconst _getErrorMessage = (err) => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst NETWORK_ERROR_CODES = [502, 503, 504];\nexport async function handleError(error) {\n    var _a;\n    if (!looksLikeFetchResponse(error)) {\n        throw new AuthRetryableFetchError(_getErrorMessage(error), 0);\n    }\n    if (NETWORK_ERROR_CODES.includes(error.status)) {\n        // status in 500...599 range - server had an error, request might be retryed.\n        throw new AuthRetryableFetchError(_getErrorMessage(error), error.status);\n    }\n    let data;\n    try {\n        data = await error.json();\n    }\n    catch (e) {\n        throw new AuthUnknownError(_getErrorMessage(e), e);\n    }\n    let errorCode = undefined;\n    const responseAPIVersion = parseResponseAPIVersion(error);\n    if (responseAPIVersion &&\n        responseAPIVersion.getTime() >= API_VERSIONS['2024-01-01'].timestamp &&\n        typeof data === 'object' &&\n        data &&\n        typeof data.code === 'string') {\n        errorCode = data.code;\n    }\n    else if (typeof data === 'object' && data && typeof data.error_code === 'string') {\n        errorCode = data.error_code;\n    }\n    if (!errorCode) {\n        // Legacy support for weak password errors, when there were no error codes\n        if (typeof data === 'object' &&\n            data &&\n            typeof data.weak_password === 'object' &&\n            data.weak_password &&\n            Array.isArray(data.weak_password.reasons) &&\n            data.weak_password.reasons.length &&\n            data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n            throw new AuthWeakPasswordError(_getErrorMessage(data), error.status, data.weak_password.reasons);\n        }\n    }\n    else if (errorCode === 'weak_password') {\n        throw new AuthWeakPasswordError(_getErrorMessage(data), error.status, ((_a = data.weak_password) === null || _a === void 0 ? void 0 : _a.reasons) || []);\n    }\n    else if (errorCode === 'session_not_found') {\n        // The `session_id` inside the JWT does not correspond to a row in the\n        // `sessions` table. This usually means the user has signed out, has been\n        // deleted, or their session has somehow been terminated.\n        throw new AuthSessionMissingError();\n    }\n    throw new AuthApiError(_getErrorMessage(data), error.status || 500, errorCode);\n}\nconst _getRequestParams = (method, options, parameters, body) => {\n    const params = { method, headers: (options === null || options === void 0 ? void 0 : options.headers) || {} };\n    if (method === 'GET') {\n        return params;\n    }\n    params.headers = Object.assign({ 'Content-Type': 'application/json;charset=UTF-8' }, options === null || options === void 0 ? void 0 : options.headers);\n    params.body = JSON.stringify(body);\n    return Object.assign(Object.assign({}, params), parameters);\n};\nexport async function _request(fetcher, method, url, options) {\n    var _a;\n    const headers = Object.assign({}, options === null || options === void 0 ? void 0 : options.headers);\n    if (!headers[API_VERSION_HEADER_NAME]) {\n        headers[API_VERSION_HEADER_NAME] = API_VERSIONS['2024-01-01'].name;\n    }\n    if (options === null || options === void 0 ? void 0 : options.jwt) {\n        headers['Authorization'] = `Bearer ${options.jwt}`;\n    }\n    const qs = (_a = options === null || options === void 0 ? void 0 : options.query) !== null && _a !== void 0 ? _a : {};\n    if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n        qs['redirect_to'] = options.redirectTo;\n    }\n    const queryString = Object.keys(qs).length ? '?' + new URLSearchParams(qs).toString() : '';\n    const data = await _handleRequest(fetcher, method, url + queryString, {\n        headers,\n        noResolveJson: options === null || options === void 0 ? void 0 : options.noResolveJson,\n    }, {}, options === null || options === void 0 ? void 0 : options.body);\n    return (options === null || options === void 0 ? void 0 : options.xform) ? options === null || options === void 0 ? void 0 : options.xform(data) : { data: Object.assign({}, data), error: null };\n}\nasync function _handleRequest(fetcher, method, url, options, parameters, body) {\n    const requestParams = _getRequestParams(method, options, parameters, body);\n    let result;\n    try {\n        result = await fetcher(url, Object.assign({}, requestParams));\n    }\n    catch (e) {\n        console.error(e);\n        // fetch failed, likely due to a network or CORS error\n        throw new AuthRetryableFetchError(_getErrorMessage(e), 0);\n    }\n    if (!result.ok) {\n        await handleError(result);\n    }\n    if (options === null || options === void 0 ? void 0 : options.noResolveJson) {\n        return result;\n    }\n    try {\n        return await result.json();\n    }\n    catch (e) {\n        await handleError(e);\n    }\n}\nexport function _sessionResponse(data) {\n    var _a;\n    let session = null;\n    if (hasSession(data)) {\n        session = Object.assign({}, data);\n        if (!data.expires_at) {\n            session.expires_at = expiresAt(data.expires_in);\n        }\n    }\n    const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n    return { data: { session, user }, error: null };\n}\nexport function _sessionResponsePassword(data) {\n    const response = _sessionResponse(data);\n    if (!response.error &&\n        data.weak_password &&\n        typeof data.weak_password === 'object' &&\n        Array.isArray(data.weak_password.reasons) &&\n        data.weak_password.reasons.length &&\n        data.weak_password.message &&\n        typeof data.weak_password.message === 'string' &&\n        data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n        response.data.weak_password = data.weak_password;\n    }\n    return response;\n}\nexport function _userResponse(data) {\n    var _a;\n    const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n    return { data: { user }, error: null };\n}\nexport function _ssoResponse(data) {\n    return { data, error: null };\n}\nexport function _generateLinkResponse(data) {\n    const { action_link, email_otp, hashed_token, redirect_to, verification_type } = data, rest = __rest(data, [\"action_link\", \"email_otp\", \"hashed_token\", \"redirect_to\", \"verification_type\"]);\n    const properties = {\n        action_link,\n        email_otp,\n        hashed_token,\n        redirect_to,\n        verification_type,\n    };\n    const user = Object.assign({}, rest);\n    return {\n        data: {\n            properties,\n            user,\n        },\n        error: null,\n    };\n}\nexport function _noResolveJsonResponse(data) {\n    return data;\n}\n/**\n * hasSession checks if the response object contains a valid session\n * @param data A response object\n * @returns true if a session is in the response\n */\nfunction hasSession(data) {\n    return data.access_token && data.refresh_token && data.expires_in;\n}\n//# sourceMappingURL=fetch.js.map", "export const SIGN_OUT_SCOPES = ['global', 'local', 'others'];\n//# sourceMappingURL=types.js.map", "var __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nimport { _generateLinkResponse, _noResolveJsonResponse, _request, _userResponse, } from './lib/fetch';\nimport { resolveFetch, validateUUID } from './lib/helpers';\nimport { SIGN_OUT_SCOPES, } from './lib/types';\nimport { isAuthError } from './lib/errors';\nexport default class GoTrueAdminApi {\n    constructor({ url = '', headers = {}, fetch, }) {\n        this.url = url;\n        this.headers = headers;\n        this.fetch = resolveFetch(fetch);\n        this.mfa = {\n            listFactors: this._listFactors.bind(this),\n            deleteFactor: this._deleteFactor.bind(this),\n        };\n    }\n    /**\n     * Removes a logged-in session.\n     * @param jwt A valid, logged-in JWT.\n     * @param scope The logout sope.\n     */\n    async signOut(jwt, scope = SIGN_OUT_SCOPES[0]) {\n        if (SIGN_OUT_SCOPES.indexOf(scope) < 0) {\n            throw new Error(`@supabase/auth-js: Parameter scope must be one of ${SIGN_OUT_SCOPES.join(', ')}`);\n        }\n        try {\n            await _request(this.fetch, 'POST', `${this.url}/logout?scope=${scope}`, {\n                headers: this.headers,\n                jwt,\n                noResolveJson: true,\n            });\n            return { data: null, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sends an invite link to an email address.\n     * @param email The email address of the user.\n     * @param options Additional options to be included when inviting.\n     */\n    async inviteUserByEmail(email, options = {}) {\n        try {\n            return await _request(this.fetch, 'POST', `${this.url}/invite`, {\n                body: { email, data: options.data },\n                headers: this.headers,\n                redirectTo: options.redirectTo,\n                xform: _userResponse,\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Generates email links and OTPs to be sent via a custom email provider.\n     * @param email The user's email.\n     * @param options.password User password. For signup only.\n     * @param options.data Optional user metadata. For signup only.\n     * @param options.redirectTo The redirect url which should be appended to the generated link\n     */\n    async generateLink(params) {\n        try {\n            const { options } = params, rest = __rest(params, [\"options\"]);\n            const body = Object.assign(Object.assign({}, rest), options);\n            if ('newEmail' in rest) {\n                // replace newEmail with new_email in request body\n                body.new_email = rest === null || rest === void 0 ? void 0 : rest.newEmail;\n                delete body['newEmail'];\n            }\n            return await _request(this.fetch, 'POST', `${this.url}/admin/generate_link`, {\n                body: body,\n                headers: this.headers,\n                xform: _generateLinkResponse,\n                redirectTo: options === null || options === void 0 ? void 0 : options.redirectTo,\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return {\n                    data: {\n                        properties: null,\n                        user: null,\n                    },\n                    error,\n                };\n            }\n            throw error;\n        }\n    }\n    // User Admin API\n    /**\n     * Creates a new user.\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async createUser(attributes) {\n        try {\n            return await _request(this.fetch, 'POST', `${this.url}/admin/users`, {\n                body: attributes,\n                headers: this.headers,\n                xform: _userResponse,\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get a list of users.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     * @param params An object which supports `page` and `perPage` as numbers, to alter the paginated results.\n     */\n    async listUsers(params) {\n        var _a, _b, _c, _d, _e, _f, _g;\n        try {\n            const pagination = { nextPage: null, lastPage: 0, total: 0 };\n            const response = await _request(this.fetch, 'GET', `${this.url}/admin/users`, {\n                headers: this.headers,\n                noResolveJson: true,\n                query: {\n                    page: (_b = (_a = params === null || params === void 0 ? void 0 : params.page) === null || _a === void 0 ? void 0 : _a.toString()) !== null && _b !== void 0 ? _b : '',\n                    per_page: (_d = (_c = params === null || params === void 0 ? void 0 : params.perPage) === null || _c === void 0 ? void 0 : _c.toString()) !== null && _d !== void 0 ? _d : '',\n                },\n                xform: _noResolveJsonResponse,\n            });\n            if (response.error)\n                throw response.error;\n            const users = await response.json();\n            const total = (_e = response.headers.get('x-total-count')) !== null && _e !== void 0 ? _e : 0;\n            const links = (_g = (_f = response.headers.get('link')) === null || _f === void 0 ? void 0 : _f.split(',')) !== null && _g !== void 0 ? _g : [];\n            if (links.length > 0) {\n                links.forEach((link) => {\n                    const page = parseInt(link.split(';')[0].split('=')[1].substring(0, 1));\n                    const rel = JSON.parse(link.split(';')[1].split('=')[1]);\n                    pagination[`${rel}Page`] = page;\n                });\n                pagination.total = parseInt(total);\n            }\n            return { data: Object.assign(Object.assign({}, users), pagination), error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { users: [] }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get user by id.\n     *\n     * @param uid The user's unique identifier\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async getUserById(uid) {\n        validateUUID(uid);\n        try {\n            return await _request(this.fetch, 'GET', `${this.url}/admin/users/${uid}`, {\n                headers: this.headers,\n                xform: _userResponse,\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Updates the user data.\n     *\n     * @param attributes The data you want to update.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async updateUserById(uid, attributes) {\n        validateUUID(uid);\n        try {\n            return await _request(this.fetch, 'PUT', `${this.url}/admin/users/${uid}`, {\n                body: attributes,\n                headers: this.headers,\n                xform: _userResponse,\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Delete a user. Requires a `service_role` key.\n     *\n     * @param id The user id you want to remove.\n     * @param shouldSoftDelete If true, then the user will be soft-deleted from the auth schema. Soft deletion allows user identification from the hashed user ID but is not reversible.\n     * Defaults to false for backward compatibility.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async deleteUser(id, shouldSoftDelete = false) {\n        validateUUID(id);\n        try {\n            return await _request(this.fetch, 'DELETE', `${this.url}/admin/users/${id}`, {\n                headers: this.headers,\n                body: {\n                    should_soft_delete: shouldSoftDelete,\n                },\n                xform: _userResponse,\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    async _listFactors(params) {\n        validateUUID(params.userId);\n        try {\n            const { data, error } = await _request(this.fetch, 'GET', `${this.url}/admin/users/${params.userId}/factors`, {\n                headers: this.headers,\n                xform: (factors) => {\n                    return { data: { factors }, error: null };\n                },\n            });\n            return { data, error };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    async _deleteFactor(params) {\n        validateUUID(params.userId);\n        validateUUID(params.id);\n        try {\n            const data = await _request(this.fetch, 'DELETE', `${this.url}/admin/users/${params.userId}/factors/${params.id}`, {\n                headers: this.headers,\n            });\n            return { data, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n}\n//# sourceMappingURL=GoTrueAdminApi.js.map", "import { supportsLocalStorage } from './helpers';\n/**\n * Provides safe access to the globalThis.localStorage property.\n */\nexport const localStorageAdapter = {\n    getItem: (key) => {\n        if (!supportsLocalStorage()) {\n            return null;\n        }\n        return globalThis.localStorage.getItem(key);\n    },\n    setItem: (key, value) => {\n        if (!supportsLocalStorage()) {\n            return;\n        }\n        globalThis.localStorage.setItem(key, value);\n    },\n    removeItem: (key) => {\n        if (!supportsLocalStorage()) {\n            return;\n        }\n        globalThis.localStorage.removeItem(key);\n    },\n};\n/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nexport function memoryLocalStorageAdapter(store = {}) {\n    return {\n        getItem: (key) => {\n            return store[key] || null;\n        },\n        setItem: (key, value) => {\n            store[key] = value;\n        },\n        removeItem: (key) => {\n            delete store[key];\n        },\n    };\n}\n//# sourceMappingURL=local-storage.js.map", "/**\n * https://mathiasbynens.be/notes/globalthis\n */\nexport function polyfillGlobalThis() {\n    if (typeof globalThis === 'object')\n        return;\n    try {\n        Object.defineProperty(Object.prototype, '__magic__', {\n            get: function () {\n                return this;\n            },\n            configurable: true,\n        });\n        // @ts-expect-error 'Allow access to magic'\n        __magic__.globalThis = __magic__;\n        // @ts-expect-error 'Allow access to magic'\n        delete Object.prototype.__magic__;\n    }\n    catch (e) {\n        if (typeof self !== 'undefined') {\n            // @ts-expect-error 'Allow access to globals'\n            self.globalThis = self;\n        }\n    }\n}\n//# sourceMappingURL=polyfills.js.map", "import { supportsLocalStorage } from './helpers';\n/**\n * @experimental\n */\nexport const internals = {\n    /**\n     * @experimental\n     */\n    debug: !!(globalThis &&\n        supportsLocalStorage() &&\n        globalThis.localStorage &&\n        globalThis.localStorage.getItem('supabase.gotrue-js.locks.debug') === 'true'),\n};\n/**\n * An error thrown when a lock cannot be acquired after some amount of time.\n *\n * Use the {@link #isAcquireTimeout} property instead of checking with `instanceof`.\n */\nexport class LockAcquireTimeoutError extends Error {\n    constructor(message) {\n        super(message);\n        this.isAcquireTimeout = true;\n    }\n}\nexport class NavigatorLockAcquireTimeoutError extends LockAcquireTimeoutError {\n}\nexport class ProcessLockAcquireTimeoutError extends LockAcquireTimeoutError {\n}\n/**\n * Implements a global exclusive lock using the Navigator LockManager API. It\n * is available on all browsers released after 2022-03-15 with <PERSON><PERSON> being the\n * last one to release support. If the API is not available, this function will\n * throw. Make sure you check availablility before configuring {@link\n * GoTrueClient}.\n *\n * You can turn on debugging by setting the `supabase.gotrue-js.locks.debug`\n * local storage item to `true`.\n *\n * Internals:\n *\n * Since the LockManager API does not preserve stack traces for the async\n * function passed in the `request` method, a trick is used where acquiring the\n * lock releases a previously started promise to run the operation in the `fn`\n * function. The lock waits for that promise to finish (with or without error),\n * while the function will finally wait for the result anyway.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nexport async function navigatorLock(name, acquireTimeout, fn) {\n    if (internals.debug) {\n        console.log('@supabase/gotrue-js: navigatorLock: acquire lock', name, acquireTimeout);\n    }\n    const abortController = new globalThis.AbortController();\n    if (acquireTimeout > 0) {\n        setTimeout(() => {\n            abortController.abort();\n            if (internals.debug) {\n                console.log('@supabase/gotrue-js: navigatorLock acquire timed out', name);\n            }\n        }, acquireTimeout);\n    }\n    // MDN article: https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request\n    // Wrapping navigator.locks.request() with a plain Promise is done as some\n    // libraries like zone.js patch the Promise object to track the execution\n    // context. However, it appears that most browsers use an internal promise\n    // implementation when using the navigator.locks.request() API causing them\n    // to lose context and emit confusing log messages or break certain features.\n    // This wrapping is believed to help zone.js track the execution context\n    // better.\n    return await Promise.resolve().then(() => globalThis.navigator.locks.request(name, acquireTimeout === 0\n        ? {\n            mode: 'exclusive',\n            ifAvailable: true,\n        }\n        : {\n            mode: 'exclusive',\n            signal: abortController.signal,\n        }, async (lock) => {\n        if (lock) {\n            if (internals.debug) {\n                console.log('@supabase/gotrue-js: navigatorLock: acquired', name, lock.name);\n            }\n            try {\n                return await fn();\n            }\n            finally {\n                if (internals.debug) {\n                    console.log('@supabase/gotrue-js: navigatorLock: released', name, lock.name);\n                }\n            }\n        }\n        else {\n            if (acquireTimeout === 0) {\n                if (internals.debug) {\n                    console.log('@supabase/gotrue-js: navigatorLock: not immediately available', name);\n                }\n                throw new NavigatorLockAcquireTimeoutError(`Acquiring an exclusive Navigator LockManager lock \"${name}\" immediately failed`);\n            }\n            else {\n                if (internals.debug) {\n                    try {\n                        const result = await globalThis.navigator.locks.query();\n                        console.log('@supabase/gotrue-js: Navigator LockManager state', JSON.stringify(result, null, '  '));\n                    }\n                    catch (e) {\n                        console.warn('@supabase/gotrue-js: Error when querying Navigator LockManager state', e);\n                    }\n                }\n                // Browser is not following the Navigator LockManager spec, it\n                // returned a null lock when we didn't use ifAvailable. So we can\n                // pretend the lock is acquired in the name of backward compatibility\n                // and user experience and just run the function.\n                console.warn('@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request');\n                return await fn();\n            }\n        }\n    }));\n}\nconst PROCESS_LOCKS = {};\n/**\n * Implements a global exclusive lock that works only in the current process.\n * Useful for environments like React Native or other non-browser\n * single-process (i.e. no concept of \"tabs\") environments.\n *\n * Use {@link #navigatorLock} in browser environments.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nexport async function processLock(name, acquireTimeout, fn) {\n    var _a;\n    const previousOperation = (_a = PROCESS_LOCKS[name]) !== null && _a !== void 0 ? _a : Promise.resolve();\n    const currentOperation = Promise.race([\n        previousOperation.catch(() => {\n            // ignore error of previous operation that we're waiting to finish\n            return null;\n        }),\n        acquireTimeout >= 0\n            ? new Promise((_, reject) => {\n                setTimeout(() => {\n                    reject(new ProcessLockAcquireTimeoutError(`Acquring process lock with name \"${name}\" timed out`));\n                }, acquireTimeout);\n            })\n            : null,\n    ].filter((x) => x))\n        .catch((e) => {\n        if (e && e.isAcquireTimeout) {\n            throw e;\n        }\n        return null;\n    })\n        .then(async () => {\n        // previous operations finished and we didn't get a race on the acquire\n        // timeout, so the current operation can finally start\n        return await fn();\n    });\n    PROCESS_LOCKS[name] = currentOperation.catch(async (e) => {\n        if (e && e.isAcquireTimeout) {\n            // if the current operation timed out, it doesn't mean that the previous\n            // operation finished, so we need contnue waiting for it to finish\n            await previousOperation;\n            return null;\n        }\n        throw e;\n    });\n    // finally wait for the current operation to finish successfully, with an\n    // error or with an acquire timeout error\n    return await currentOperation;\n}\n//# sourceMappingURL=locks.js.map", "import GoTrueAdmin<PERSON>pi from './GoTrueAdminApi';\nimport { DEFAULT_HEADERS, EXPIRY_MARGIN_MS, AUTO_REFRESH_TICK_DURATION_MS, AUTO_REFRESH_TICK_THRESHOLD, GOTRUE_URL, STORAGE_KEY, JW<PERSON>_TTL, } from './lib/constants';\nimport { AuthImplicitGrantRedirectError, AuthPKCEGrantCodeExchangeError, AuthInvalidCredentialsError, AuthSessionMissingError, AuthInvalidTokenResponseError, AuthUnknownError, isAuthApiError, isAuthError, isAuthRetryableFetchError, isAuthSessionMissingError, isAuthImplicitGrantRedirectError, AuthInvalidJwtError, } from './lib/errors';\nimport { _request, _sessionResponse, _sessionResponsePassword, _userResponse, _ssoResponse, } from './lib/fetch';\nimport { Deferred, getItemAsync, isBrowser, removeItemAsync, resolveFetch, setItemAsync, uuid, retryable, sleep, supportsLocalStorage, parseParametersFromURL, getCodeChallengeAndMethod, getAlgorithm, validateExp, decodeJWT, } from './lib/helpers';\nimport { localStorageAdapter, memoryLocalStorageAdapter } from './lib/local-storage';\nimport { polyfillGlobalThis } from './lib/polyfills';\nimport { version } from './lib/version';\nimport { LockAcquireTimeoutError, navigatorLock } from './lib/locks';\nimport { stringToUint8Array, bytesToBase64URL } from './lib/base64url';\npolyfillGlobalThis(); // Make \"globalThis\" available\nconst DEFAULT_OPTIONS = {\n    url: GOTRUE_URL,\n    storageKey: STORAGE_KEY,\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n    headers: DEFAULT_HEADERS,\n    flowType: 'implicit',\n    debug: false,\n    hasCustomAuthorizationHeader: false,\n};\nasync function lockNoOp(name, acquireTimeout, fn) {\n    return await fn();\n}\nexport default class GoTrueClient {\n    /**\n     * Create a new client for use in the browser.\n     */\n    constructor(options) {\n        var _a, _b;\n        this.memoryStorage = null;\n        this.stateChangeEmitters = new Map();\n        this.autoRefreshTicker = null;\n        this.visibilityChangedCallback = null;\n        this.refreshingDeferred = null;\n        /**\n         * Keeps track of the async client initialization.\n         * When null or not yet resolved the auth state is `unknown`\n         * Once resolved the the auth state is known and it's save to call any further client methods.\n         * Keep extra care to never reject or throw uncaught errors\n         */\n        this.initializePromise = null;\n        this.detectSessionInUrl = true;\n        this.hasCustomAuthorizationHeader = false;\n        this.suppressGetSessionWarning = false;\n        this.lockAcquired = false;\n        this.pendingInLock = [];\n        /**\n         * Used to broadcast state change events to other tabs listening.\n         */\n        this.broadcastChannel = null;\n        this.logger = console.log;\n        this.instanceID = GoTrueClient.nextInstanceID;\n        GoTrueClient.nextInstanceID += 1;\n        if (this.instanceID > 0 && isBrowser()) {\n            console.warn('Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.');\n        }\n        const settings = Object.assign(Object.assign({}, DEFAULT_OPTIONS), options);\n        this.logDebugMessages = !!settings.debug;\n        if (typeof settings.debug === 'function') {\n            this.logger = settings.debug;\n        }\n        this.persistSession = settings.persistSession;\n        this.storageKey = settings.storageKey;\n        this.autoRefreshToken = settings.autoRefreshToken;\n        this.admin = new GoTrueAdminApi({\n            url: settings.url,\n            headers: settings.headers,\n            fetch: settings.fetch,\n        });\n        this.url = settings.url;\n        this.headers = settings.headers;\n        this.fetch = resolveFetch(settings.fetch);\n        this.lock = settings.lock || lockNoOp;\n        this.detectSessionInUrl = settings.detectSessionInUrl;\n        this.flowType = settings.flowType;\n        this.hasCustomAuthorizationHeader = settings.hasCustomAuthorizationHeader;\n        if (settings.lock) {\n            this.lock = settings.lock;\n        }\n        else if (isBrowser() && ((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.navigator) === null || _a === void 0 ? void 0 : _a.locks)) {\n            this.lock = navigatorLock;\n        }\n        else {\n            this.lock = lockNoOp;\n        }\n        this.jwks = { keys: [] };\n        this.jwks_cached_at = Number.MIN_SAFE_INTEGER;\n        this.mfa = {\n            verify: this._verify.bind(this),\n            enroll: this._enroll.bind(this),\n            unenroll: this._unenroll.bind(this),\n            challenge: this._challenge.bind(this),\n            listFactors: this._listFactors.bind(this),\n            challengeAndVerify: this._challengeAndVerify.bind(this),\n            getAuthenticatorAssuranceLevel: this._getAuthenticatorAssuranceLevel.bind(this),\n        };\n        if (this.persistSession) {\n            if (settings.storage) {\n                this.storage = settings.storage;\n            }\n            else {\n                if (supportsLocalStorage()) {\n                    this.storage = localStorageAdapter;\n                }\n                else {\n                    this.memoryStorage = {};\n                    this.storage = memoryLocalStorageAdapter(this.memoryStorage);\n                }\n            }\n        }\n        else {\n            this.memoryStorage = {};\n            this.storage = memoryLocalStorageAdapter(this.memoryStorage);\n        }\n        if (isBrowser() && globalThis.BroadcastChannel && this.persistSession && this.storageKey) {\n            try {\n                this.broadcastChannel = new globalThis.BroadcastChannel(this.storageKey);\n            }\n            catch (e) {\n                console.error('Failed to create a new BroadcastChannel, multi-tab state changes will not be available', e);\n            }\n            (_b = this.broadcastChannel) === null || _b === void 0 ? void 0 : _b.addEventListener('message', async (event) => {\n                this._debug('received broadcast notification from other tab or client', event);\n                await this._notifyAllSubscribers(event.data.event, event.data.session, false); // broadcast = false so we don't get an endless loop of messages\n            });\n        }\n        this.initialize();\n    }\n    _debug(...args) {\n        if (this.logDebugMessages) {\n            this.logger(`GoTrueClient@${this.instanceID} (${version}) ${new Date().toISOString()}`, ...args);\n        }\n        return this;\n    }\n    /**\n     * Initializes the client session either from the url or from storage.\n     * This method is automatically called when instantiating the client, but should also be called\n     * manually when checking for an error from an auth redirect (oauth, magiclink, password recovery, etc).\n     */\n    async initialize() {\n        if (this.initializePromise) {\n            return await this.initializePromise;\n        }\n        this.initializePromise = (async () => {\n            return await this._acquireLock(-1, async () => {\n                return await this._initialize();\n            });\n        })();\n        return await this.initializePromise;\n    }\n    /**\n     * IMPORTANT:\n     * 1. Never throw in this method, as it is called from the constructor\n     * 2. Never return a session from this method as it would be cached over\n     *    the whole lifetime of the client\n     */\n    async _initialize() {\n        var _a;\n        try {\n            const params = parseParametersFromURL(window.location.href);\n            let callbackUrlType = 'none';\n            if (this._isImplicitGrantCallback(params)) {\n                callbackUrlType = 'implicit';\n            }\n            else if (await this._isPKCECallback(params)) {\n                callbackUrlType = 'pkce';\n            }\n            /**\n             * Attempt to get the session from the URL only if these conditions are fulfilled\n             *\n             * Note: If the URL isn't one of the callback url types (implicit or pkce),\n             * then there could be an existing session so we don't want to prematurely remove it\n             */\n            if (isBrowser() && this.detectSessionInUrl && callbackUrlType !== 'none') {\n                const { data, error } = await this._getSessionFromURL(params, callbackUrlType);\n                if (error) {\n                    this._debug('#_initialize()', 'error detecting session from URL', error);\n                    if (isAuthImplicitGrantRedirectError(error)) {\n                        const errorCode = (_a = error.details) === null || _a === void 0 ? void 0 : _a.code;\n                        if (errorCode === 'identity_already_exists' ||\n                            errorCode === 'identity_not_found' ||\n                            errorCode === 'single_identity_not_deletable') {\n                            return { error };\n                        }\n                    }\n                    // failed login attempt via url,\n                    // remove old session as in verifyOtp, signUp and signInWith*\n                    await this._removeSession();\n                    return { error };\n                }\n                const { session, redirectType } = data;\n                this._debug('#_initialize()', 'detected session in URL', session, 'redirect type', redirectType);\n                await this._saveSession(session);\n                setTimeout(async () => {\n                    if (redirectType === 'recovery') {\n                        await this._notifyAllSubscribers('PASSWORD_RECOVERY', session);\n                    }\n                    else {\n                        await this._notifyAllSubscribers('SIGNED_IN', session);\n                    }\n                }, 0);\n                return { error: null };\n            }\n            // no login attempt via callback url try to recover session from storage\n            await this._recoverAndRefresh();\n            return { error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { error };\n            }\n            return {\n                error: new AuthUnknownError('Unexpected error during initialization', error),\n            };\n        }\n        finally {\n            await this._handleVisibilityChange();\n            this._debug('#_initialize()', 'end');\n        }\n    }\n    /**\n     * Creates a new anonymous user.\n     *\n     * @returns A session where the is_anonymous claim in the access token JWT set to true\n     */\n    async signInAnonymously(credentials) {\n        var _a, _b, _c;\n        try {\n            const res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n                headers: this.headers,\n                body: {\n                    data: (_b = (_a = credentials === null || credentials === void 0 ? void 0 : credentials.options) === null || _a === void 0 ? void 0 : _a.data) !== null && _b !== void 0 ? _b : {},\n                    gotrue_meta_security: { captcha_token: (_c = credentials === null || credentials === void 0 ? void 0 : credentials.options) === null || _c === void 0 ? void 0 : _c.captchaToken },\n                },\n                xform: _sessionResponse,\n            });\n            const { data, error } = res;\n            if (error || !data) {\n                return { data: { user: null, session: null }, error: error };\n            }\n            const session = data.session;\n            const user = data.user;\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Creates a new user.\n     *\n     * Be aware that if a user account exists in the system you may get back an\n     * error message that attempts to hide this information from the user.\n     * This method has support for PKCE via email signups. The PKCE flow cannot be used when autoconfirm is enabled.\n     *\n     * @returns A logged-in session if the server has \"autoconfirm\" ON\n     * @returns A user if the server has \"autoconfirm\" OFF\n     */\n    async signUp(credentials) {\n        var _a, _b, _c;\n        try {\n            let res;\n            if ('email' in credentials) {\n                const { email, password, options } = credentials;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce') {\n                    ;\n                    [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey);\n                }\n                res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n                    headers: this.headers,\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                    body: {\n                        email,\n                        password,\n                        data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        code_challenge: codeChallenge,\n                        code_challenge_method: codeChallengeMethod,\n                    },\n                    xform: _sessionResponse,\n                });\n            }\n            else if ('phone' in credentials) {\n                const { phone, password, options } = credentials;\n                res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        password,\n                        data: (_b = options === null || options === void 0 ? void 0 : options.data) !== null && _b !== void 0 ? _b : {},\n                        channel: (_c = options === null || options === void 0 ? void 0 : options.channel) !== null && _c !== void 0 ? _c : 'sms',\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _sessionResponse,\n                });\n            }\n            else {\n                throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n            }\n            const { data, error } = res;\n            if (error || !data) {\n                return { data: { user: null, session: null }, error: error };\n            }\n            const session = data.session;\n            const user = data.user;\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in an existing user with an email and password or phone and password.\n     *\n     * Be aware that you may get back an error message that will not distinguish\n     * between the cases where the account does not exist or that the\n     * email/phone and password combination is wrong or that the account can only\n     * be accessed via social login.\n     */\n    async signInWithPassword(credentials) {\n        try {\n            let res;\n            if ('email' in credentials) {\n                const { email, password, options } = credentials;\n                res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        password,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _sessionResponsePassword,\n                });\n            }\n            else if ('phone' in credentials) {\n                const { phone, password, options } = credentials;\n                res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        password,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _sessionResponsePassword,\n                });\n            }\n            else {\n                throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n            }\n            const { data, error } = res;\n            if (error) {\n                return { data: { user: null, session: null }, error };\n            }\n            else if (!data || !data.session || !data.user) {\n                return { data: { user: null, session: null }, error: new AuthInvalidTokenResponseError() };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return {\n                data: Object.assign({ user: data.user, session: data.session }, (data.weak_password ? { weakPassword: data.weak_password } : null)),\n                error,\n            };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in an existing user via a third-party provider.\n     * This method supports the PKCE flow.\n     */\n    async signInWithOAuth(credentials) {\n        var _a, _b, _c, _d;\n        return await this._handleProviderSignIn(credentials.provider, {\n            redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n            scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n            queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n            skipBrowserRedirect: (_d = credentials.options) === null || _d === void 0 ? void 0 : _d.skipBrowserRedirect,\n        });\n    }\n    /**\n     * Log in an existing user by exchanging an Auth Code issued during the PKCE flow.\n     */\n    async exchangeCodeForSession(authCode) {\n        await this.initializePromise;\n        return this._acquireLock(-1, async () => {\n            return this._exchangeCodeForSession(authCode);\n        });\n    }\n    /**\n     * Signs in a user by verifying a message signed by the user's private key.\n     * Only Solana supported at this time, using the Sign in with Solana standard.\n     */\n    async signInWithWeb3(credentials) {\n        const { chain } = credentials;\n        if (chain === 'solana') {\n            return await this.signInWithSolana(credentials);\n        }\n        throw new Error(`@supabase/auth-js: Unsupported chain \"${chain}\"`);\n    }\n    async signInWithSolana(credentials) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n        let message;\n        let signature;\n        if ('message' in credentials) {\n            message = credentials.message;\n            signature = credentials.signature;\n        }\n        else {\n            const { chain, wallet, statement, options } = credentials;\n            let resolvedWallet;\n            if (!isBrowser()) {\n                if (typeof wallet !== 'object' || !(options === null || options === void 0 ? void 0 : options.url)) {\n                    throw new Error('@supabase/auth-js: Both wallet and url must be specified in non-browser environments.');\n                }\n                resolvedWallet = wallet;\n            }\n            else if (typeof wallet === 'object') {\n                resolvedWallet = wallet;\n            }\n            else {\n                const windowAny = window;\n                if ('solana' in windowAny &&\n                    typeof windowAny.solana === 'object' &&\n                    (('signIn' in windowAny.solana && typeof windowAny.solana.signIn === 'function') ||\n                        ('signMessage' in windowAny.solana &&\n                            typeof windowAny.solana.signMessage === 'function'))) {\n                    resolvedWallet = windowAny.solana;\n                }\n                else {\n                    throw new Error(`@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.`);\n                }\n            }\n            const url = new URL((_a = options === null || options === void 0 ? void 0 : options.url) !== null && _a !== void 0 ? _a : window.location.href);\n            if ('signIn' in resolvedWallet && resolvedWallet.signIn) {\n                const output = await resolvedWallet.signIn(Object.assign(Object.assign(Object.assign({ issuedAt: new Date().toISOString() }, options === null || options === void 0 ? void 0 : options.signInWithSolana), { \n                    // non-overridable properties\n                    version: '1', domain: url.host, uri: url.href }), (statement ? { statement } : null)));\n                let outputToProcess;\n                if (Array.isArray(output) && output[0] && typeof output[0] === 'object') {\n                    outputToProcess = output[0];\n                }\n                else if (output &&\n                    typeof output === 'object' &&\n                    'signedMessage' in output &&\n                    'signature' in output) {\n                    outputToProcess = output;\n                }\n                else {\n                    throw new Error('@supabase/auth-js: Wallet method signIn() returned unrecognized value');\n                }\n                if ('signedMessage' in outputToProcess &&\n                    'signature' in outputToProcess &&\n                    (typeof outputToProcess.signedMessage === 'string' ||\n                        outputToProcess.signedMessage instanceof Uint8Array) &&\n                    outputToProcess.signature instanceof Uint8Array) {\n                    message =\n                        typeof outputToProcess.signedMessage === 'string'\n                            ? outputToProcess.signedMessage\n                            : new TextDecoder().decode(outputToProcess.signedMessage);\n                    signature = outputToProcess.signature;\n                }\n                else {\n                    throw new Error('@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields');\n                }\n            }\n            else {\n                if (!('signMessage' in resolvedWallet) ||\n                    typeof resolvedWallet.signMessage !== 'function' ||\n                    !('publicKey' in resolvedWallet) ||\n                    typeof resolvedWallet !== 'object' ||\n                    !resolvedWallet.publicKey ||\n                    !('toBase58' in resolvedWallet.publicKey) ||\n                    typeof resolvedWallet.publicKey.toBase58 !== 'function') {\n                    throw new Error('@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API');\n                }\n                message = [\n                    `${url.host} wants you to sign in with your Solana account:`,\n                    resolvedWallet.publicKey.toBase58(),\n                    ...(statement ? ['', statement, ''] : ['']),\n                    'Version: 1',\n                    `URI: ${url.href}`,\n                    `Issued At: ${(_c = (_b = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _b === void 0 ? void 0 : _b.issuedAt) !== null && _c !== void 0 ? _c : new Date().toISOString()}`,\n                    ...(((_d = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _d === void 0 ? void 0 : _d.notBefore)\n                        ? [`Not Before: ${options.signInWithSolana.notBefore}`]\n                        : []),\n                    ...(((_e = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _e === void 0 ? void 0 : _e.expirationTime)\n                        ? [`Expiration Time: ${options.signInWithSolana.expirationTime}`]\n                        : []),\n                    ...(((_f = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _f === void 0 ? void 0 : _f.chainId)\n                        ? [`Chain ID: ${options.signInWithSolana.chainId}`]\n                        : []),\n                    ...(((_g = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _g === void 0 ? void 0 : _g.nonce) ? [`Nonce: ${options.signInWithSolana.nonce}`] : []),\n                    ...(((_h = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _h === void 0 ? void 0 : _h.requestId)\n                        ? [`Request ID: ${options.signInWithSolana.requestId}`]\n                        : []),\n                    ...(((_k = (_j = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _j === void 0 ? void 0 : _j.resources) === null || _k === void 0 ? void 0 : _k.length)\n                        ? [\n                            'Resources',\n                            ...options.signInWithSolana.resources.map((resource) => `- ${resource}`),\n                        ]\n                        : []),\n                ].join('\\n');\n                const maybeSignature = await resolvedWallet.signMessage(new TextEncoder().encode(message), 'utf8');\n                if (!maybeSignature || !(maybeSignature instanceof Uint8Array)) {\n                    throw new Error('@supabase/auth-js: Wallet signMessage() API returned an recognized value');\n                }\n                signature = maybeSignature;\n            }\n        }\n        try {\n            const { data, error } = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=web3`, {\n                headers: this.headers,\n                body: Object.assign({ chain: 'solana', message, signature: bytesToBase64URL(signature) }, (((_l = credentials.options) === null || _l === void 0 ? void 0 : _l.captchaToken)\n                    ? { gotrue_meta_security: { captcha_token: (_m = credentials.options) === null || _m === void 0 ? void 0 : _m.captchaToken } }\n                    : null)),\n                xform: _sessionResponse,\n            });\n            if (error) {\n                throw error;\n            }\n            if (!data || !data.session || !data.user) {\n                return {\n                    data: { user: null, session: null },\n                    error: new AuthInvalidTokenResponseError(),\n                };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return { data: Object.assign({}, data), error };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    async _exchangeCodeForSession(authCode) {\n        const storageItem = await getItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n        const [codeVerifier, redirectType] = (storageItem !== null && storageItem !== void 0 ? storageItem : '').split('/');\n        try {\n            const { data, error } = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=pkce`, {\n                headers: this.headers,\n                body: {\n                    auth_code: authCode,\n                    code_verifier: codeVerifier,\n                },\n                xform: _sessionResponse,\n            });\n            await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n            if (error) {\n                throw error;\n            }\n            if (!data || !data.session || !data.user) {\n                return {\n                    data: { user: null, session: null, redirectType: null },\n                    error: new AuthInvalidTokenResponseError(),\n                };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return { data: Object.assign(Object.assign({}, data), { redirectType: redirectType !== null && redirectType !== void 0 ? redirectType : null }), error };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null, redirectType: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Allows signing in with an OIDC ID token. The authentication provider used\n     * should be enabled and configured.\n     */\n    async signInWithIdToken(credentials) {\n        try {\n            const { options, provider, token, access_token, nonce } = credentials;\n            const res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=id_token`, {\n                headers: this.headers,\n                body: {\n                    provider,\n                    id_token: token,\n                    access_token,\n                    nonce,\n                    gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                },\n                xform: _sessionResponse,\n            });\n            const { data, error } = res;\n            if (error) {\n                return { data: { user: null, session: null }, error };\n            }\n            else if (!data || !data.session || !data.user) {\n                return {\n                    data: { user: null, session: null },\n                    error: new AuthInvalidTokenResponseError(),\n                };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return { data, error };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in a user using magiclink or a one-time password (OTP).\n     *\n     * If the `{{ .ConfirmationURL }}` variable is specified in the email template, a magiclink will be sent.\n     * If the `{{ .Token }}` variable is specified in the email template, an OTP will be sent.\n     * If you're using phone sign-ins, only an OTP will be sent. You won't be able to send a magiclink for phone sign-ins.\n     *\n     * Be aware that you may get back an error message that will not distinguish\n     * between the cases where the account does not exist or, that the account\n     * can only be accessed via social login.\n     *\n     * Do note that you will need to configure a Whatsapp sender on Twilio\n     * if you are using phone sign in with the 'whatsapp' channel. The whatsapp\n     * channel is not supported on other providers\n     * at this time.\n     * This method supports PKCE when an email is passed.\n     */\n    async signInWithOtp(credentials) {\n        var _a, _b, _c, _d, _e;\n        try {\n            if ('email' in credentials) {\n                const { email, options } = credentials;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce') {\n                    ;\n                    [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey);\n                }\n                const { error } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n                        create_user: (_b = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _b !== void 0 ? _b : true,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        code_challenge: codeChallenge,\n                        code_challenge_method: codeChallengeMethod,\n                    },\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                });\n                return { data: { user: null, session: null }, error };\n            }\n            if ('phone' in credentials) {\n                const { phone, options } = credentials;\n                const { data, error } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        data: (_c = options === null || options === void 0 ? void 0 : options.data) !== null && _c !== void 0 ? _c : {},\n                        create_user: (_d = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _d !== void 0 ? _d : true,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        channel: (_e = options === null || options === void 0 ? void 0 : options.channel) !== null && _e !== void 0 ? _e : 'sms',\n                    },\n                });\n                return { data: { user: null, session: null, messageId: data === null || data === void 0 ? void 0 : data.message_id }, error };\n            }\n            throw new AuthInvalidCredentialsError('You must provide either an email or phone number.');\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in a user given a User supplied OTP or TokenHash received through mobile or email.\n     */\n    async verifyOtp(params) {\n        var _a, _b;\n        try {\n            let redirectTo = undefined;\n            let captchaToken = undefined;\n            if ('options' in params) {\n                redirectTo = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo;\n                captchaToken = (_b = params.options) === null || _b === void 0 ? void 0 : _b.captchaToken;\n            }\n            const { data, error } = await _request(this.fetch, 'POST', `${this.url}/verify`, {\n                headers: this.headers,\n                body: Object.assign(Object.assign({}, params), { gotrue_meta_security: { captcha_token: captchaToken } }),\n                redirectTo,\n                xform: _sessionResponse,\n            });\n            if (error) {\n                throw error;\n            }\n            if (!data) {\n                throw new Error('An error occurred on token verification.');\n            }\n            const session = data.session;\n            const user = data.user;\n            if (session === null || session === void 0 ? void 0 : session.access_token) {\n                await this._saveSession(session);\n                await this._notifyAllSubscribers(params.type == 'recovery' ? 'PASSWORD_RECOVERY' : 'SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Attempts a single-sign on using an enterprise Identity Provider. A\n     * successful SSO attempt will redirect the current page to the identity\n     * provider authorization page. The redirect URL is implementation and SSO\n     * protocol specific.\n     *\n     * You can use it by providing a SSO domain. Typically you can extract this\n     * domain by asking users for their email address. If this domain is\n     * registered on the Auth instance the redirect will use that organization's\n     * currently active SSO Identity Provider for the login.\n     *\n     * If you have built an organization-specific login page, you can use the\n     * organization's SSO Identity Provider UUID directly instead.\n     */\n    async signInWithSSO(params) {\n        var _a, _b, _c;\n        try {\n            let codeChallenge = null;\n            let codeChallengeMethod = null;\n            if (this.flowType === 'pkce') {\n                ;\n                [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey);\n            }\n            return await _request(this.fetch, 'POST', `${this.url}/sso`, {\n                body: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, ('providerId' in params ? { provider_id: params.providerId } : null)), ('domain' in params ? { domain: params.domain } : null)), { redirect_to: (_b = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo) !== null && _b !== void 0 ? _b : undefined }), (((_c = params === null || params === void 0 ? void 0 : params.options) === null || _c === void 0 ? void 0 : _c.captchaToken)\n                    ? { gotrue_meta_security: { captcha_token: params.options.captchaToken } }\n                    : null)), { skip_http_redirect: true, code_challenge: codeChallenge, code_challenge_method: codeChallengeMethod }),\n                headers: this.headers,\n                xform: _ssoResponse,\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sends a reauthentication OTP to the user's email or phone number.\n     * Requires the user to be signed-in.\n     */\n    async reauthenticate() {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._reauthenticate();\n        });\n    }\n    async _reauthenticate() {\n        try {\n            return await this._useSession(async (result) => {\n                const { data: { session }, error: sessionError, } = result;\n                if (sessionError)\n                    throw sessionError;\n                if (!session)\n                    throw new AuthSessionMissingError();\n                const { error } = await _request(this.fetch, 'GET', `${this.url}/reauthenticate`, {\n                    headers: this.headers,\n                    jwt: session.access_token,\n                });\n                return { data: { user: null, session: null }, error };\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Resends an existing signup confirmation email, email change email, SMS OTP or phone change OTP.\n     */\n    async resend(credentials) {\n        try {\n            const endpoint = `${this.url}/resend`;\n            if ('email' in credentials) {\n                const { email, type, options } = credentials;\n                const { error } = await _request(this.fetch, 'POST', endpoint, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        type,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                });\n                return { data: { user: null, session: null }, error };\n            }\n            else if ('phone' in credentials) {\n                const { phone, type, options } = credentials;\n                const { data, error } = await _request(this.fetch, 'POST', endpoint, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        type,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                });\n                return { data: { user: null, session: null, messageId: data === null || data === void 0 ? void 0 : data.message_id }, error };\n            }\n            throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a type');\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Returns the session, refreshing it if necessary.\n     *\n     * The session returned can be null if the session is not detected which can happen in the event a user is not signed-in or has logged out.\n     *\n     * **IMPORTANT:** This method loads values directly from the storage attached\n     * to the client. If that storage is based on request cookies for example,\n     * the values in it may not be authentic and therefore it's strongly advised\n     * against using this method and its results in such circumstances. A warning\n     * will be emitted if this is detected. Use {@link #getUser()} instead.\n     */\n    async getSession() {\n        await this.initializePromise;\n        const result = await this._acquireLock(-1, async () => {\n            return this._useSession(async (result) => {\n                return result;\n            });\n        });\n        return result;\n    }\n    /**\n     * Acquires a global lock based on the storage key.\n     */\n    async _acquireLock(acquireTimeout, fn) {\n        this._debug('#_acquireLock', 'begin', acquireTimeout);\n        try {\n            if (this.lockAcquired) {\n                const last = this.pendingInLock.length\n                    ? this.pendingInLock[this.pendingInLock.length - 1]\n                    : Promise.resolve();\n                const result = (async () => {\n                    await last;\n                    return await fn();\n                })();\n                this.pendingInLock.push((async () => {\n                    try {\n                        await result;\n                    }\n                    catch (e) {\n                        // we just care if it finished\n                    }\n                })());\n                return result;\n            }\n            return await this.lock(`lock:${this.storageKey}`, acquireTimeout, async () => {\n                this._debug('#_acquireLock', 'lock acquired for storage key', this.storageKey);\n                try {\n                    this.lockAcquired = true;\n                    const result = fn();\n                    this.pendingInLock.push((async () => {\n                        try {\n                            await result;\n                        }\n                        catch (e) {\n                            // we just care if it finished\n                        }\n                    })());\n                    await result;\n                    // keep draining the queue until there's nothing to wait on\n                    while (this.pendingInLock.length) {\n                        const waitOn = [...this.pendingInLock];\n                        await Promise.all(waitOn);\n                        this.pendingInLock.splice(0, waitOn.length);\n                    }\n                    return await result;\n                }\n                finally {\n                    this._debug('#_acquireLock', 'lock released for storage key', this.storageKey);\n                    this.lockAcquired = false;\n                }\n            });\n        }\n        finally {\n            this._debug('#_acquireLock', 'end');\n        }\n    }\n    /**\n     * Use instead of {@link #getSession} inside the library. It is\n     * semantically usually what you want, as getting a session involves some\n     * processing afterwards that requires only one client operating on the\n     * session at once across multiple tabs or processes.\n     */\n    async _useSession(fn) {\n        this._debug('#_useSession', 'begin');\n        try {\n            // the use of __loadSession here is the only correct use of the function!\n            const result = await this.__loadSession();\n            return await fn(result);\n        }\n        finally {\n            this._debug('#_useSession', 'end');\n        }\n    }\n    /**\n     * NEVER USE DIRECTLY!\n     *\n     * Always use {@link #_useSession}.\n     */\n    async __loadSession() {\n        this._debug('#__loadSession()', 'begin');\n        if (!this.lockAcquired) {\n            this._debug('#__loadSession()', 'used outside of an acquired lock!', new Error().stack);\n        }\n        try {\n            let currentSession = null;\n            const maybeSession = await getItemAsync(this.storage, this.storageKey);\n            this._debug('#getSession()', 'session from storage', maybeSession);\n            if (maybeSession !== null) {\n                if (this._isValidSession(maybeSession)) {\n                    currentSession = maybeSession;\n                }\n                else {\n                    this._debug('#getSession()', 'session from storage is not valid');\n                    await this._removeSession();\n                }\n            }\n            if (!currentSession) {\n                return { data: { session: null }, error: null };\n            }\n            // A session is considered expired before the access token _actually_\n            // expires. When the autoRefreshToken option is off (or when the tab is\n            // in the background), very eager users of getSession() -- like\n            // realtime-js -- might send a valid JWT which will expire by the time it\n            // reaches the server.\n            const hasExpired = currentSession.expires_at\n                ? currentSession.expires_at * 1000 - Date.now() < EXPIRY_MARGIN_MS\n                : false;\n            this._debug('#__loadSession()', `session has${hasExpired ? '' : ' not'} expired`, 'expires_at', currentSession.expires_at);\n            if (!hasExpired) {\n                if (this.storage.isServer) {\n                    let suppressWarning = this.suppressGetSessionWarning;\n                    const proxySession = new Proxy(currentSession, {\n                        get: (target, prop, receiver) => {\n                            if (!suppressWarning && prop === 'user') {\n                                // only show warning when the user object is being accessed from the server\n                                console.warn('Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.');\n                                suppressWarning = true; // keeps this proxy instance from logging additional warnings\n                                this.suppressGetSessionWarning = true; // keeps this client's future proxy instances from warning\n                            }\n                            return Reflect.get(target, prop, receiver);\n                        },\n                    });\n                    currentSession = proxySession;\n                }\n                return { data: { session: currentSession }, error: null };\n            }\n            const { session, error } = await this._callRefreshToken(currentSession.refresh_token);\n            if (error) {\n                return { data: { session: null }, error };\n            }\n            return { data: { session }, error: null };\n        }\n        finally {\n            this._debug('#__loadSession()', 'end');\n        }\n    }\n    /**\n     * Gets the current user details if there is an existing session. This method\n     * performs a network request to the Supabase Auth server, so the returned\n     * value is authentic and can be used to base authorization rules on.\n     *\n     * @param jwt Takes in an optional access token JWT. If no JWT is provided, the JWT from the current session is used.\n     */\n    async getUser(jwt) {\n        if (jwt) {\n            return await this._getUser(jwt);\n        }\n        await this.initializePromise;\n        const result = await this._acquireLock(-1, async () => {\n            return await this._getUser();\n        });\n        return result;\n    }\n    async _getUser(jwt) {\n        try {\n            if (jwt) {\n                return await _request(this.fetch, 'GET', `${this.url}/user`, {\n                    headers: this.headers,\n                    jwt: jwt,\n                    xform: _userResponse,\n                });\n            }\n            return await this._useSession(async (result) => {\n                var _a, _b, _c;\n                const { data, error } = result;\n                if (error) {\n                    throw error;\n                }\n                // returns an error if there is no access_token or custom authorization header\n                if (!((_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) && !this.hasCustomAuthorizationHeader) {\n                    return { data: { user: null }, error: new AuthSessionMissingError() };\n                }\n                return await _request(this.fetch, 'GET', `${this.url}/user`, {\n                    headers: this.headers,\n                    jwt: (_c = (_b = data.session) === null || _b === void 0 ? void 0 : _b.access_token) !== null && _c !== void 0 ? _c : undefined,\n                    xform: _userResponse,\n                });\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                if (isAuthSessionMissingError(error)) {\n                    // JWT contains a `session_id` which does not correspond to an active\n                    // session in the database, indicating the user is signed out.\n                    await this._removeSession();\n                    await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n                }\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Updates user data for a logged in user.\n     */\n    async updateUser(attributes, options = {}) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._updateUser(attributes, options);\n        });\n    }\n    async _updateUser(attributes, options = {}) {\n        try {\n            return await this._useSession(async (result) => {\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    throw sessionError;\n                }\n                if (!sessionData.session) {\n                    throw new AuthSessionMissingError();\n                }\n                const session = sessionData.session;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce' && attributes.email != null) {\n                    ;\n                    [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey);\n                }\n                const { data, error: userError } = await _request(this.fetch, 'PUT', `${this.url}/user`, {\n                    headers: this.headers,\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                    body: Object.assign(Object.assign({}, attributes), { code_challenge: codeChallenge, code_challenge_method: codeChallengeMethod }),\n                    jwt: session.access_token,\n                    xform: _userResponse,\n                });\n                if (userError)\n                    throw userError;\n                session.user = data.user;\n                await this._saveSession(session);\n                await this._notifyAllSubscribers('USER_UPDATED', session);\n                return { data: { user: session.user }, error: null };\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sets the session data from the current session. If the current session is expired, setSession will take care of refreshing it to obtain a new session.\n     * If the refresh token or access token in the current session is invalid, an error will be thrown.\n     * @param currentSession The current session that minimally contains an access token and refresh token.\n     */\n    async setSession(currentSession) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._setSession(currentSession);\n        });\n    }\n    async _setSession(currentSession) {\n        try {\n            if (!currentSession.access_token || !currentSession.refresh_token) {\n                throw new AuthSessionMissingError();\n            }\n            const timeNow = Date.now() / 1000;\n            let expiresAt = timeNow;\n            let hasExpired = true;\n            let session = null;\n            const { payload } = decodeJWT(currentSession.access_token);\n            if (payload.exp) {\n                expiresAt = payload.exp;\n                hasExpired = expiresAt <= timeNow;\n            }\n            if (hasExpired) {\n                const { session: refreshedSession, error } = await this._callRefreshToken(currentSession.refresh_token);\n                if (error) {\n                    return { data: { user: null, session: null }, error: error };\n                }\n                if (!refreshedSession) {\n                    return { data: { user: null, session: null }, error: null };\n                }\n                session = refreshedSession;\n            }\n            else {\n                const { data, error } = await this._getUser(currentSession.access_token);\n                if (error) {\n                    throw error;\n                }\n                session = {\n                    access_token: currentSession.access_token,\n                    refresh_token: currentSession.refresh_token,\n                    user: data.user,\n                    token_type: 'bearer',\n                    expires_in: expiresAt - timeNow,\n                    expires_at: expiresAt,\n                };\n                await this._saveSession(session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user: session.user, session }, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { session: null, user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Returns a new session, regardless of expiry status.\n     * Takes in an optional current session. If not passed in, then refreshSession() will attempt to retrieve it from getSession().\n     * If the current session's refresh token is invalid, an error will be thrown.\n     * @param currentSession The current session. If passed in, it must contain a refresh token.\n     */\n    async refreshSession(currentSession) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._refreshSession(currentSession);\n        });\n    }\n    async _refreshSession(currentSession) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a;\n                if (!currentSession) {\n                    const { data, error } = result;\n                    if (error) {\n                        throw error;\n                    }\n                    currentSession = (_a = data.session) !== null && _a !== void 0 ? _a : undefined;\n                }\n                if (!(currentSession === null || currentSession === void 0 ? void 0 : currentSession.refresh_token)) {\n                    throw new AuthSessionMissingError();\n                }\n                const { session, error } = await this._callRefreshToken(currentSession.refresh_token);\n                if (error) {\n                    return { data: { user: null, session: null }, error: error };\n                }\n                if (!session) {\n                    return { data: { user: null, session: null }, error: null };\n                }\n                return { data: { user: session.user, session }, error: null };\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Gets the session data from a URL string\n     */\n    async _getSessionFromURL(params, callbackUrlType) {\n        try {\n            if (!isBrowser())\n                throw new AuthImplicitGrantRedirectError('No browser detected.');\n            // If there's an error in the URL, it doesn't matter what flow it is, we just return the error.\n            if (params.error || params.error_description || params.error_code) {\n                // The error class returned implies that the redirect is from an implicit grant flow\n                // but it could also be from a redirect error from a PKCE flow.\n                throw new AuthImplicitGrantRedirectError(params.error_description || 'Error in URL with unspecified error_description', {\n                    error: params.error || 'unspecified_error',\n                    code: params.error_code || 'unspecified_code',\n                });\n            }\n            // Checks for mismatches between the flowType initialised in the client and the URL parameters\n            switch (callbackUrlType) {\n                case 'implicit':\n                    if (this.flowType === 'pkce') {\n                        throw new AuthPKCEGrantCodeExchangeError('Not a valid PKCE flow url.');\n                    }\n                    break;\n                case 'pkce':\n                    if (this.flowType === 'implicit') {\n                        throw new AuthImplicitGrantRedirectError('Not a valid implicit grant flow url.');\n                    }\n                    break;\n                default:\n                // there's no mismatch so we continue\n            }\n            // Since this is a redirect for PKCE, we attempt to retrieve the code from the URL for the code exchange\n            if (callbackUrlType === 'pkce') {\n                this._debug('#_initialize()', 'begin', 'is PKCE flow', true);\n                if (!params.code)\n                    throw new AuthPKCEGrantCodeExchangeError('No code detected.');\n                const { data, error } = await this._exchangeCodeForSession(params.code);\n                if (error)\n                    throw error;\n                const url = new URL(window.location.href);\n                url.searchParams.delete('code');\n                window.history.replaceState(window.history.state, '', url.toString());\n                return { data: { session: data.session, redirectType: null }, error: null };\n            }\n            const { provider_token, provider_refresh_token, access_token, refresh_token, expires_in, expires_at, token_type, } = params;\n            if (!access_token || !expires_in || !refresh_token || !token_type) {\n                throw new AuthImplicitGrantRedirectError('No session defined in URL');\n            }\n            const timeNow = Math.round(Date.now() / 1000);\n            const expiresIn = parseInt(expires_in);\n            let expiresAt = timeNow + expiresIn;\n            if (expires_at) {\n                expiresAt = parseInt(expires_at);\n            }\n            const actuallyExpiresIn = expiresAt - timeNow;\n            if (actuallyExpiresIn * 1000 <= AUTO_REFRESH_TICK_DURATION_MS) {\n                console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${actuallyExpiresIn}s, should have been closer to ${expiresIn}s`);\n            }\n            const issuedAt = expiresAt - expiresIn;\n            if (timeNow - issuedAt >= 120) {\n                console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale', issuedAt, expiresAt, timeNow);\n            }\n            else if (timeNow - issuedAt < 0) {\n                console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew', issuedAt, expiresAt, timeNow);\n            }\n            const { data, error } = await this._getUser(access_token);\n            if (error)\n                throw error;\n            const session = {\n                provider_token,\n                provider_refresh_token,\n                access_token,\n                expires_in: expiresIn,\n                expires_at: expiresAt,\n                refresh_token,\n                token_type,\n                user: data.user,\n            };\n            // Remove tokens from URL\n            window.location.hash = '';\n            this._debug('#_getSessionFromURL()', 'clearing window.location.hash');\n            return { data: { session, redirectType: params.type }, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { session: null, redirectType: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Checks if the current URL contains parameters given by an implicit oauth grant flow (https://www.rfc-editor.org/rfc/rfc6749.html#section-4.2)\n     */\n    _isImplicitGrantCallback(params) {\n        return Boolean(params.access_token || params.error_description);\n    }\n    /**\n     * Checks if the current URL and backing storage contain parameters given by a PKCE flow\n     */\n    async _isPKCECallback(params) {\n        const currentStorageContent = await getItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n        return !!(params.code && currentStorageContent);\n    }\n    /**\n     * Inside a browser context, `signOut()` will remove the logged in user from the browser session and log them out - removing all items from localstorage and then trigger a `\"SIGNED_OUT\"` event.\n     *\n     * For server-side management, you can revoke all refresh tokens for a user by passing a user's JWT through to `auth.api.signOut(JWT: string)`.\n     * There is no way to revoke a user's access token jwt until it expires. It is recommended to set a shorter expiry on the jwt for this reason.\n     *\n     * If using `others` scope, no `SIGNED_OUT` event is fired!\n     */\n    async signOut(options = { scope: 'global' }) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._signOut(options);\n        });\n    }\n    async _signOut({ scope } = { scope: 'global' }) {\n        return await this._useSession(async (result) => {\n            var _a;\n            const { data, error: sessionError } = result;\n            if (sessionError) {\n                return { error: sessionError };\n            }\n            const accessToken = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token;\n            if (accessToken) {\n                const { error } = await this.admin.signOut(accessToken, scope);\n                if (error) {\n                    // ignore 404s since user might not exist anymore\n                    // ignore 401s since an invalid or expired JWT should sign out the current session\n                    if (!(isAuthApiError(error) &&\n                        (error.status === 404 || error.status === 401 || error.status === 403))) {\n                        return { error };\n                    }\n                }\n            }\n            if (scope !== 'others') {\n                await this._removeSession();\n                await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n            }\n            return { error: null };\n        });\n    }\n    /**\n     * Receive a notification every time an auth event happens.\n     * @param callback A callback function to be invoked when an auth event happens.\n     */\n    onAuthStateChange(callback) {\n        const id = uuid();\n        const subscription = {\n            id,\n            callback,\n            unsubscribe: () => {\n                this._debug('#unsubscribe()', 'state change callback with id removed', id);\n                this.stateChangeEmitters.delete(id);\n            },\n        };\n        this._debug('#onAuthStateChange()', 'registered callback with id', id);\n        this.stateChangeEmitters.set(id, subscription);\n        (async () => {\n            await this.initializePromise;\n            await this._acquireLock(-1, async () => {\n                this._emitInitialSession(id);\n            });\n        })();\n        return { data: { subscription } };\n    }\n    async _emitInitialSession(id) {\n        return await this._useSession(async (result) => {\n            var _a, _b;\n            try {\n                const { data: { session }, error, } = result;\n                if (error)\n                    throw error;\n                await ((_a = this.stateChangeEmitters.get(id)) === null || _a === void 0 ? void 0 : _a.callback('INITIAL_SESSION', session));\n                this._debug('INITIAL_SESSION', 'callback id', id, 'session', session);\n            }\n            catch (err) {\n                await ((_b = this.stateChangeEmitters.get(id)) === null || _b === void 0 ? void 0 : _b.callback('INITIAL_SESSION', null));\n                this._debug('INITIAL_SESSION', 'callback id', id, 'error', err);\n                console.error(err);\n            }\n        });\n    }\n    /**\n     * Sends a password reset request to an email address. This method supports the PKCE flow.\n     *\n     * @param email The email address of the user.\n     * @param options.redirectTo The URL to send the user to after they click the password reset link.\n     * @param options.captchaToken Verification token received when the user completes the captcha on the site.\n     */\n    async resetPasswordForEmail(email, options = {}) {\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (this.flowType === 'pkce') {\n            ;\n            [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey, true // isPasswordRecovery\n            );\n        }\n        try {\n            return await _request(this.fetch, 'POST', `${this.url}/recover`, {\n                body: {\n                    email,\n                    code_challenge: codeChallenge,\n                    code_challenge_method: codeChallengeMethod,\n                    gotrue_meta_security: { captcha_token: options.captchaToken },\n                },\n                headers: this.headers,\n                redirectTo: options.redirectTo,\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Gets all the identities linked to a user.\n     */\n    async getUserIdentities() {\n        var _a;\n        try {\n            const { data, error } = await this.getUser();\n            if (error)\n                throw error;\n            return { data: { identities: (_a = data.user.identities) !== null && _a !== void 0 ? _a : [] }, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Links an oauth identity to an existing user.\n     * This method supports the PKCE flow.\n     */\n    async linkIdentity(credentials) {\n        var _a;\n        try {\n            const { data, error } = await this._useSession(async (result) => {\n                var _a, _b, _c, _d, _e;\n                const { data, error } = result;\n                if (error)\n                    throw error;\n                const url = await this._getUrlForProvider(`${this.url}/user/identities/authorize`, credentials.provider, {\n                    redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n                    scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n                    queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n                    skipBrowserRedirect: true,\n                });\n                return await _request(this.fetch, 'GET', url, {\n                    headers: this.headers,\n                    jwt: (_e = (_d = data.session) === null || _d === void 0 ? void 0 : _d.access_token) !== null && _e !== void 0 ? _e : undefined,\n                });\n            });\n            if (error)\n                throw error;\n            if (isBrowser() && !((_a = credentials.options) === null || _a === void 0 ? void 0 : _a.skipBrowserRedirect)) {\n                window.location.assign(data === null || data === void 0 ? void 0 : data.url);\n            }\n            return { data: { provider: credentials.provider, url: data === null || data === void 0 ? void 0 : data.url }, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { provider: credentials.provider, url: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Unlinks an identity from a user by deleting it. The user will no longer be able to sign in with that identity once it's unlinked.\n     */\n    async unlinkIdentity(identity) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data, error } = result;\n                if (error) {\n                    throw error;\n                }\n                return await _request(this.fetch, 'DELETE', `${this.url}/user/identities/${identity.identity_id}`, {\n                    headers: this.headers,\n                    jwt: (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : undefined,\n                });\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Generates a new JWT.\n     * @param refreshToken A valid refresh token that was returned on login.\n     */\n    async _refreshAccessToken(refreshToken) {\n        const debugName = `#_refreshAccessToken(${refreshToken.substring(0, 5)}...)`;\n        this._debug(debugName, 'begin');\n        try {\n            const startedAt = Date.now();\n            // will attempt to refresh the token with exponential backoff\n            return await retryable(async (attempt) => {\n                if (attempt > 0) {\n                    await sleep(200 * Math.pow(2, attempt - 1)); // 200, 400, 800, ...\n                }\n                this._debug(debugName, 'refreshing attempt', attempt);\n                return await _request(this.fetch, 'POST', `${this.url}/token?grant_type=refresh_token`, {\n                    body: { refresh_token: refreshToken },\n                    headers: this.headers,\n                    xform: _sessionResponse,\n                });\n            }, (attempt, error) => {\n                const nextBackOffInterval = 200 * Math.pow(2, attempt);\n                return (error &&\n                    isAuthRetryableFetchError(error) &&\n                    // retryable only if the request can be sent before the backoff overflows the tick duration\n                    Date.now() + nextBackOffInterval - startedAt < AUTO_REFRESH_TICK_DURATION_MS);\n            });\n        }\n        catch (error) {\n            this._debug(debugName, 'error', error);\n            if (isAuthError(error)) {\n                return { data: { session: null, user: null }, error };\n            }\n            throw error;\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    _isValidSession(maybeSession) {\n        const isValidSession = typeof maybeSession === 'object' &&\n            maybeSession !== null &&\n            'access_token' in maybeSession &&\n            'refresh_token' in maybeSession &&\n            'expires_at' in maybeSession;\n        return isValidSession;\n    }\n    async _handleProviderSignIn(provider, options) {\n        const url = await this._getUrlForProvider(`${this.url}/authorize`, provider, {\n            redirectTo: options.redirectTo,\n            scopes: options.scopes,\n            queryParams: options.queryParams,\n        });\n        this._debug('#_handleProviderSignIn()', 'provider', provider, 'options', options, 'url', url);\n        // try to open on the browser\n        if (isBrowser() && !options.skipBrowserRedirect) {\n            window.location.assign(url);\n        }\n        return { data: { provider, url }, error: null };\n    }\n    /**\n     * Recovers the session from LocalStorage and refreshes the token\n     * Note: this method is async to accommodate for AsyncStorage e.g. in React native.\n     */\n    async _recoverAndRefresh() {\n        var _a;\n        const debugName = '#_recoverAndRefresh()';\n        this._debug(debugName, 'begin');\n        try {\n            const currentSession = await getItemAsync(this.storage, this.storageKey);\n            this._debug(debugName, 'session from storage', currentSession);\n            if (!this._isValidSession(currentSession)) {\n                this._debug(debugName, 'session is not valid');\n                if (currentSession !== null) {\n                    await this._removeSession();\n                }\n                return;\n            }\n            const expiresWithMargin = ((_a = currentSession.expires_at) !== null && _a !== void 0 ? _a : Infinity) * 1000 - Date.now() < EXPIRY_MARGIN_MS;\n            this._debug(debugName, `session has${expiresWithMargin ? '' : ' not'} expired with margin of ${EXPIRY_MARGIN_MS}s`);\n            if (expiresWithMargin) {\n                if (this.autoRefreshToken && currentSession.refresh_token) {\n                    const { error } = await this._callRefreshToken(currentSession.refresh_token);\n                    if (error) {\n                        console.error(error);\n                        if (!isAuthRetryableFetchError(error)) {\n                            this._debug(debugName, 'refresh failed with a non-retryable error, removing the session', error);\n                            await this._removeSession();\n                        }\n                    }\n                }\n            }\n            else {\n                // no need to persist currentSession again, as we just loaded it from\n                // local storage; persisting it again may overwrite a value saved by\n                // another client with access to the same local storage\n                await this._notifyAllSubscribers('SIGNED_IN', currentSession);\n            }\n        }\n        catch (err) {\n            this._debug(debugName, 'error', err);\n            console.error(err);\n            return;\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    async _callRefreshToken(refreshToken) {\n        var _a, _b;\n        if (!refreshToken) {\n            throw new AuthSessionMissingError();\n        }\n        // refreshing is already in progress\n        if (this.refreshingDeferred) {\n            return this.refreshingDeferred.promise;\n        }\n        const debugName = `#_callRefreshToken(${refreshToken.substring(0, 5)}...)`;\n        this._debug(debugName, 'begin');\n        try {\n            this.refreshingDeferred = new Deferred();\n            const { data, error } = await this._refreshAccessToken(refreshToken);\n            if (error)\n                throw error;\n            if (!data.session)\n                throw new AuthSessionMissingError();\n            await this._saveSession(data.session);\n            await this._notifyAllSubscribers('TOKEN_REFRESHED', data.session);\n            const result = { session: data.session, error: null };\n            this.refreshingDeferred.resolve(result);\n            return result;\n        }\n        catch (error) {\n            this._debug(debugName, 'error', error);\n            if (isAuthError(error)) {\n                const result = { session: null, error };\n                if (!isAuthRetryableFetchError(error)) {\n                    await this._removeSession();\n                }\n                (_a = this.refreshingDeferred) === null || _a === void 0 ? void 0 : _a.resolve(result);\n                return result;\n            }\n            (_b = this.refreshingDeferred) === null || _b === void 0 ? void 0 : _b.reject(error);\n            throw error;\n        }\n        finally {\n            this.refreshingDeferred = null;\n            this._debug(debugName, 'end');\n        }\n    }\n    async _notifyAllSubscribers(event, session, broadcast = true) {\n        const debugName = `#_notifyAllSubscribers(${event})`;\n        this._debug(debugName, 'begin', session, `broadcast = ${broadcast}`);\n        try {\n            if (this.broadcastChannel && broadcast) {\n                this.broadcastChannel.postMessage({ event, session });\n            }\n            const errors = [];\n            const promises = Array.from(this.stateChangeEmitters.values()).map(async (x) => {\n                try {\n                    await x.callback(event, session);\n                }\n                catch (e) {\n                    errors.push(e);\n                }\n            });\n            await Promise.all(promises);\n            if (errors.length > 0) {\n                for (let i = 0; i < errors.length; i += 1) {\n                    console.error(errors[i]);\n                }\n                throw errors[0];\n            }\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    /**\n     * set currentSession and currentUser\n     * process to _startAutoRefreshToken if possible\n     */\n    async _saveSession(session) {\n        this._debug('#_saveSession()', session);\n        // _saveSession is always called whenever a new session has been acquired\n        // so we can safely suppress the warning returned by future getSession calls\n        this.suppressGetSessionWarning = true;\n        await setItemAsync(this.storage, this.storageKey, session);\n    }\n    async _removeSession() {\n        this._debug('#_removeSession()');\n        await removeItemAsync(this.storage, this.storageKey);\n        await this._notifyAllSubscribers('SIGNED_OUT', null);\n    }\n    /**\n     * Removes any registered visibilitychange callback.\n     *\n     * {@see #startAutoRefresh}\n     * {@see #stopAutoRefresh}\n     */\n    _removeVisibilityChangedCallback() {\n        this._debug('#_removeVisibilityChangedCallback()');\n        const callback = this.visibilityChangedCallback;\n        this.visibilityChangedCallback = null;\n        try {\n            if (callback && isBrowser() && (window === null || window === void 0 ? void 0 : window.removeEventListener)) {\n                window.removeEventListener('visibilitychange', callback);\n            }\n        }\n        catch (e) {\n            console.error('removing visibilitychange callback failed', e);\n        }\n    }\n    /**\n     * This is the private implementation of {@link #startAutoRefresh}. Use this\n     * within the library.\n     */\n    async _startAutoRefresh() {\n        await this._stopAutoRefresh();\n        this._debug('#_startAutoRefresh()');\n        const ticker = setInterval(() => this._autoRefreshTokenTick(), AUTO_REFRESH_TICK_DURATION_MS);\n        this.autoRefreshTicker = ticker;\n        if (ticker && typeof ticker === 'object' && typeof ticker.unref === 'function') {\n            // ticker is a NodeJS Timeout object that has an `unref` method\n            // https://nodejs.org/api/timers.html#timeoutunref\n            // When auto refresh is used in NodeJS (like for testing) the\n            // `setInterval` is preventing the process from being marked as\n            // finished and tests run endlessly. This can be prevented by calling\n            // `unref()` on the returned object.\n            ticker.unref();\n            // @ts-expect-error TS has no context of Deno\n        }\n        else if (typeof Deno !== 'undefined' && typeof Deno.unrefTimer === 'function') {\n            // similar like for NodeJS, but with the Deno API\n            // https://deno.land/api@latest?unstable&s=Deno.unrefTimer\n            // @ts-expect-error TS has no context of Deno\n            Deno.unrefTimer(ticker);\n        }\n        // run the tick immediately, but in the next pass of the event loop so that\n        // #_initialize can be allowed to complete without recursively waiting on\n        // itself\n        setTimeout(async () => {\n            await this.initializePromise;\n            await this._autoRefreshTokenTick();\n        }, 0);\n    }\n    /**\n     * This is the private implementation of {@link #stopAutoRefresh}. Use this\n     * within the library.\n     */\n    async _stopAutoRefresh() {\n        this._debug('#_stopAutoRefresh()');\n        const ticker = this.autoRefreshTicker;\n        this.autoRefreshTicker = null;\n        if (ticker) {\n            clearInterval(ticker);\n        }\n    }\n    /**\n     * Starts an auto-refresh process in the background. The session is checked\n     * every few seconds. Close to the time of expiration a process is started to\n     * refresh the session. If refreshing fails it will be retried for as long as\n     * necessary.\n     *\n     * If you set the {@link GoTrueClientOptions#autoRefreshToken} you don't need\n     * to call this function, it will be called for you.\n     *\n     * On browsers the refresh process works only when the tab/window is in the\n     * foreground to conserve resources as well as prevent race conditions and\n     * flooding auth with requests. If you call this method any managed\n     * visibility change callback will be removed and you must manage visibility\n     * changes on your own.\n     *\n     * On non-browser platforms the refresh process works *continuously* in the\n     * background, which may not be desirable. You should hook into your\n     * platform's foreground indication mechanism and call these methods\n     * appropriately to conserve resources.\n     *\n     * {@see #stopAutoRefresh}\n     */\n    async startAutoRefresh() {\n        this._removeVisibilityChangedCallback();\n        await this._startAutoRefresh();\n    }\n    /**\n     * Stops an active auto refresh process running in the background (if any).\n     *\n     * If you call this method any managed visibility change callback will be\n     * removed and you must manage visibility changes on your own.\n     *\n     * See {@link #startAutoRefresh} for more details.\n     */\n    async stopAutoRefresh() {\n        this._removeVisibilityChangedCallback();\n        await this._stopAutoRefresh();\n    }\n    /**\n     * Runs the auto refresh token tick.\n     */\n    async _autoRefreshTokenTick() {\n        this._debug('#_autoRefreshTokenTick()', 'begin');\n        try {\n            await this._acquireLock(0, async () => {\n                try {\n                    const now = Date.now();\n                    try {\n                        return await this._useSession(async (result) => {\n                            const { data: { session }, } = result;\n                            if (!session || !session.refresh_token || !session.expires_at) {\n                                this._debug('#_autoRefreshTokenTick()', 'no session');\n                                return;\n                            }\n                            // session will expire in this many ticks (or has already expired if <= 0)\n                            const expiresInTicks = Math.floor((session.expires_at * 1000 - now) / AUTO_REFRESH_TICK_DURATION_MS);\n                            this._debug('#_autoRefreshTokenTick()', `access token expires in ${expiresInTicks} ticks, a tick lasts ${AUTO_REFRESH_TICK_DURATION_MS}ms, refresh threshold is ${AUTO_REFRESH_TICK_THRESHOLD} ticks`);\n                            if (expiresInTicks <= AUTO_REFRESH_TICK_THRESHOLD) {\n                                await this._callRefreshToken(session.refresh_token);\n                            }\n                        });\n                    }\n                    catch (e) {\n                        console.error('Auto refresh tick failed with error. This is likely a transient error.', e);\n                    }\n                }\n                finally {\n                    this._debug('#_autoRefreshTokenTick()', 'end');\n                }\n            });\n        }\n        catch (e) {\n            if (e.isAcquireTimeout || e instanceof LockAcquireTimeoutError) {\n                this._debug('auto refresh token tick lock not available');\n            }\n            else {\n                throw e;\n            }\n        }\n    }\n    /**\n     * Registers callbacks on the browser / platform, which in-turn run\n     * algorithms when the browser window/tab are in foreground. On non-browser\n     * platforms it assumes always foreground.\n     */\n    async _handleVisibilityChange() {\n        this._debug('#_handleVisibilityChange()');\n        if (!isBrowser() || !(window === null || window === void 0 ? void 0 : window.addEventListener)) {\n            if (this.autoRefreshToken) {\n                // in non-browser environments the refresh token ticker runs always\n                this.startAutoRefresh();\n            }\n            return false;\n        }\n        try {\n            this.visibilityChangedCallback = async () => await this._onVisibilityChanged(false);\n            window === null || window === void 0 ? void 0 : window.addEventListener('visibilitychange', this.visibilityChangedCallback);\n            // now immediately call the visbility changed callback to setup with the\n            // current visbility state\n            await this._onVisibilityChanged(true); // initial call\n        }\n        catch (error) {\n            console.error('_handleVisibilityChange', error);\n        }\n    }\n    /**\n     * Callback registered with `window.addEventListener('visibilitychange')`.\n     */\n    async _onVisibilityChanged(calledFromInitialize) {\n        const methodName = `#_onVisibilityChanged(${calledFromInitialize})`;\n        this._debug(methodName, 'visibilityState', document.visibilityState);\n        if (document.visibilityState === 'visible') {\n            if (this.autoRefreshToken) {\n                // in browser environments the refresh token ticker runs only on focused tabs\n                // which prevents race conditions\n                this._startAutoRefresh();\n            }\n            if (!calledFromInitialize) {\n                // called when the visibility has changed, i.e. the browser\n                // transitioned from hidden -> visible so we need to see if the session\n                // should be recovered immediately... but to do that we need to acquire\n                // the lock first asynchronously\n                await this.initializePromise;\n                await this._acquireLock(-1, async () => {\n                    if (document.visibilityState !== 'visible') {\n                        this._debug(methodName, 'acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting');\n                        // visibility has changed while waiting for the lock, abort\n                        return;\n                    }\n                    // recover the session\n                    await this._recoverAndRefresh();\n                });\n            }\n        }\n        else if (document.visibilityState === 'hidden') {\n            if (this.autoRefreshToken) {\n                this._stopAutoRefresh();\n            }\n        }\n    }\n    /**\n     * Generates the relevant login URL for a third-party provider.\n     * @param options.redirectTo A URL or mobile address to send the user to after they are confirmed.\n     * @param options.scopes A space-separated list of scopes granted to the OAuth application.\n     * @param options.queryParams An object of key-value pairs containing query parameters granted to the OAuth application.\n     */\n    async _getUrlForProvider(url, provider, options) {\n        const urlParams = [`provider=${encodeURIComponent(provider)}`];\n        if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n            urlParams.push(`redirect_to=${encodeURIComponent(options.redirectTo)}`);\n        }\n        if (options === null || options === void 0 ? void 0 : options.scopes) {\n            urlParams.push(`scopes=${encodeURIComponent(options.scopes)}`);\n        }\n        if (this.flowType === 'pkce') {\n            const [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey);\n            const flowParams = new URLSearchParams({\n                code_challenge: `${encodeURIComponent(codeChallenge)}`,\n                code_challenge_method: `${encodeURIComponent(codeChallengeMethod)}`,\n            });\n            urlParams.push(flowParams.toString());\n        }\n        if (options === null || options === void 0 ? void 0 : options.queryParams) {\n            const query = new URLSearchParams(options.queryParams);\n            urlParams.push(query.toString());\n        }\n        if (options === null || options === void 0 ? void 0 : options.skipBrowserRedirect) {\n            urlParams.push(`skip_http_redirect=${options.skipBrowserRedirect}`);\n        }\n        return `${url}?${urlParams.join('&')}`;\n    }\n    async _unenroll(params) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a;\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                return await _request(this.fetch, 'DELETE', `${this.url}/factors/${params.factorId}`, {\n                    headers: this.headers,\n                    jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                });\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    async _enroll(params) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                const body = Object.assign({ friendly_name: params.friendlyName, factor_type: params.factorType }, (params.factorType === 'phone' ? { phone: params.phone } : { issuer: params.issuer }));\n                const { data, error } = await _request(this.fetch, 'POST', `${this.url}/factors`, {\n                    body,\n                    headers: this.headers,\n                    jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                });\n                if (error) {\n                    return { data: null, error };\n                }\n                if (params.factorType === 'totp' && ((_b = data === null || data === void 0 ? void 0 : data.totp) === null || _b === void 0 ? void 0 : _b.qr_code)) {\n                    data.totp.qr_code = `data:image/svg+xml;utf-8,${data.totp.qr_code}`;\n                }\n                return { data, error: null };\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * {@see GoTrueMFAApi#verify}\n     */\n    async _verify(params) {\n        return this._acquireLock(-1, async () => {\n            try {\n                return await this._useSession(async (result) => {\n                    var _a;\n                    const { data: sessionData, error: sessionError } = result;\n                    if (sessionError) {\n                        return { data: null, error: sessionError };\n                    }\n                    const { data, error } = await _request(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/verify`, {\n                        body: { code: params.code, challenge_id: params.challengeId },\n                        headers: this.headers,\n                        jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                    });\n                    if (error) {\n                        return { data: null, error };\n                    }\n                    await this._saveSession(Object.assign({ expires_at: Math.round(Date.now() / 1000) + data.expires_in }, data));\n                    await this._notifyAllSubscribers('MFA_CHALLENGE_VERIFIED', data);\n                    return { data, error };\n                });\n            }\n            catch (error) {\n                if (isAuthError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#challenge}\n     */\n    async _challenge(params) {\n        return this._acquireLock(-1, async () => {\n            try {\n                return await this._useSession(async (result) => {\n                    var _a;\n                    const { data: sessionData, error: sessionError } = result;\n                    if (sessionError) {\n                        return { data: null, error: sessionError };\n                    }\n                    return await _request(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/challenge`, {\n                        body: { channel: params.channel },\n                        headers: this.headers,\n                        jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                    });\n                });\n            }\n            catch (error) {\n                if (isAuthError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#challengeAndVerify}\n     */\n    async _challengeAndVerify(params) {\n        // both _challenge and _verify independently acquire the lock, so no need\n        // to acquire it here\n        const { data: challengeData, error: challengeError } = await this._challenge({\n            factorId: params.factorId,\n        });\n        if (challengeError) {\n            return { data: null, error: challengeError };\n        }\n        return await this._verify({\n            factorId: params.factorId,\n            challengeId: challengeData.id,\n            code: params.code,\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#listFactors}\n     */\n    async _listFactors() {\n        // use #getUser instead of #_getUser as the former acquires a lock\n        const { data: { user }, error: userError, } = await this.getUser();\n        if (userError) {\n            return { data: null, error: userError };\n        }\n        const factors = (user === null || user === void 0 ? void 0 : user.factors) || [];\n        const totp = factors.filter((factor) => factor.factor_type === 'totp' && factor.status === 'verified');\n        const phone = factors.filter((factor) => factor.factor_type === 'phone' && factor.status === 'verified');\n        return {\n            data: {\n                all: factors,\n                totp,\n                phone,\n            },\n            error: null,\n        };\n    }\n    /**\n     * {@see GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n     */\n    async _getAuthenticatorAssuranceLevel() {\n        return this._acquireLock(-1, async () => {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data: { session }, error: sessionError, } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                if (!session) {\n                    return {\n                        data: { currentLevel: null, nextLevel: null, currentAuthenticationMethods: [] },\n                        error: null,\n                    };\n                }\n                const { payload } = decodeJWT(session.access_token);\n                let currentLevel = null;\n                if (payload.aal) {\n                    currentLevel = payload.aal;\n                }\n                let nextLevel = currentLevel;\n                const verifiedFactors = (_b = (_a = session.user.factors) === null || _a === void 0 ? void 0 : _a.filter((factor) => factor.status === 'verified')) !== null && _b !== void 0 ? _b : [];\n                if (verifiedFactors.length > 0) {\n                    nextLevel = 'aal2';\n                }\n                const currentAuthenticationMethods = payload.amr || [];\n                return { data: { currentLevel, nextLevel, currentAuthenticationMethods }, error: null };\n            });\n        });\n    }\n    async fetchJwk(kid, jwks = { keys: [] }) {\n        // try fetching from the supplied jwks\n        let jwk = jwks.keys.find((key) => key.kid === kid);\n        if (jwk) {\n            return jwk;\n        }\n        // try fetching from cache\n        jwk = this.jwks.keys.find((key) => key.kid === kid);\n        // jwk exists and jwks isn't stale\n        if (jwk && this.jwks_cached_at + JWKS_TTL > Date.now()) {\n            return jwk;\n        }\n        // jwk isn't cached in memory so we need to fetch it from the well-known endpoint\n        const { data, error } = await _request(this.fetch, 'GET', `${this.url}/.well-known/jwks.json`, {\n            headers: this.headers,\n        });\n        if (error) {\n            throw error;\n        }\n        if (!data.keys || data.keys.length === 0) {\n            throw new AuthInvalidJwtError('JWKS is empty');\n        }\n        this.jwks = data;\n        this.jwks_cached_at = Date.now();\n        // Find the signing key\n        jwk = data.keys.find((key) => key.kid === kid);\n        if (!jwk) {\n            throw new AuthInvalidJwtError('No matching signing key found in JWKS');\n        }\n        return jwk;\n    }\n    /**\n     * @experimental This method may change in future versions.\n     * @description Gets the claims from a JWT. If the JWT is symmetric JWTs, it will call getUser() to verify against the server. If the JWT is asymmetric, it will be verified against the JWKS using the WebCrypto API.\n     */\n    async getClaims(jwt, jwks = { keys: [] }) {\n        try {\n            let token = jwt;\n            if (!token) {\n                const { data, error } = await this.getSession();\n                if (error || !data.session) {\n                    return { data: null, error };\n                }\n                token = data.session.access_token;\n            }\n            const { header, payload, signature, raw: { header: rawHeader, payload: rawPayload }, } = decodeJWT(token);\n            // Reject expired JWTs\n            validateExp(payload.exp);\n            // If symmetric algorithm or WebCrypto API is unavailable, fallback to getUser()\n            if (!header.kid ||\n                header.alg === 'HS256' ||\n                !('crypto' in globalThis && 'subtle' in globalThis.crypto)) {\n                const { error } = await this.getUser(token);\n                if (error) {\n                    throw error;\n                }\n                // getUser succeeds so the claims in the JWT can be trusted\n                return {\n                    data: {\n                        claims: payload,\n                        header,\n                        signature,\n                    },\n                    error: null,\n                };\n            }\n            const algorithm = getAlgorithm(header.alg);\n            const signingKey = await this.fetchJwk(header.kid, jwks);\n            // Convert JWK to CryptoKey\n            const publicKey = await crypto.subtle.importKey('jwk', signingKey, algorithm, true, [\n                'verify',\n            ]);\n            // Verify the signature\n            const isValid = await crypto.subtle.verify(algorithm, publicKey, signature, stringToUint8Array(`${rawHeader}.${rawPayload}`));\n            if (!isValid) {\n                throw new AuthInvalidJwtError('Invalid JWT signature');\n            }\n            // If verification succeeds, decode and return claims\n            return {\n                data: {\n                    claims: payload,\n                    header,\n                    signature,\n                },\n                error: null,\n            };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n}\nGoTrueClient.nextInstanceID = 0;\n//# sourceMappingURL=GoTrueClient.js.map", "import GoTrueAdminApi from './GoTrueAdminApi';\nconst AuthAdminApi = GoTrueAdminApi;\nexport default AuthAdminApi;\n//# sourceMappingURL=AuthAdminApi.js.map", "import GoTrueClient from './GoTrueClient';\nconst AuthClient = GoTrueClient;\nexport default AuthClient;\n//# sourceMappingURL=AuthClient.js.map", "import GoTrueAdminApi from './GoTrueAdminApi';\nimport GoTrueClient from './GoTrueClient';\nimport AuthAdminApi from './AuthAdminApi';\nimport AuthClient from './AuthClient';\nexport { GoTrueAdminApi, GoTrueClient, AuthAdminApi, AuthClient };\nexport * from './lib/types';\nexport * from './lib/errors';\nexport { navigatorLock, NavigatorLockAcquireTimeoutError, internals as lockInternals, processLock, } from './lib/locks';\n//# sourceMappingURL=index.js.map", null, null, null, "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst PostgrestQueryBuilder_1 = __importDefault(require(\"./PostgrestQueryBuilder\"));\nconst PostgrestFilterBuilder_1 = __importDefault(require(\"./PostgrestFilterBuilder\"));\nconst constants_1 = require(\"./constants\");\n/**\n * PostgREST client.\n *\n * @typeParam Database - Types for the schema from the [type\n * generator](https://supabase.com/docs/reference/javascript/next/typescript-support)\n *\n * @typeParam SchemaName - Postgres schema to switch to. Must be a string\n * literal, the same one passed to the constructor. If the schema is not\n * `\"public\"`, this must be supplied manually.\n */\nclass PostgrestClient {\n    // TODO: Add back shouldThrowOnError once we figure out the typings\n    /**\n     * Creates a PostgREST client.\n     *\n     * @param url - URL of the PostgREST endpoint\n     * @param options - Named parameters\n     * @param options.headers - Custom headers\n     * @param options.schema - Postgres schema to switch to\n     * @param options.fetch - Custom fetch\n     */\n    constructor(url, { headers = {}, schema, fetch, } = {}) {\n        this.url = url;\n        this.headers = Object.assign(Object.assign({}, constants_1.DEFAULT_HEADERS), headers);\n        this.schemaName = schema;\n        this.fetch = fetch;\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */\n    from(relation) {\n        const url = new URL(`${this.url}/${relation}`);\n        return new PostgrestQueryBuilder_1.default(url, {\n            headers: Object.assign({}, this.headers),\n            schema: this.schemaName,\n            fetch: this.fetch,\n        });\n    }\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */\n    schema(schema) {\n        return new PostgrestClient(this.url, {\n            headers: this.headers,\n            schema,\n            fetch: this.fetch,\n        });\n    }\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    rpc(fn, args = {}, { head = false, get = false, count, } = {}) {\n        let method;\n        const url = new URL(`${this.url}/rpc/${fn}`);\n        let body;\n        if (head || get) {\n            method = head ? 'HEAD' : 'GET';\n            Object.entries(args)\n                // params with undefined value needs to be filtered out, otherwise it'll\n                // show up as `?param=undefined`\n                .filter(([_, value]) => value !== undefined)\n                // array values need special syntax\n                .map(([name, value]) => [name, Array.isArray(value) ? `{${value.join(',')}}` : `${value}`])\n                .forEach(([name, value]) => {\n                url.searchParams.append(name, value);\n            });\n        }\n        else {\n            method = 'POST';\n            body = args;\n        }\n        const headers = Object.assign({}, this.headers);\n        if (count) {\n            headers['Prefer'] = `count=${count}`;\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url,\n            headers,\n            schema: this.schemaName,\n            body,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n}\nexports.default = PostgrestClient;\n//# sourceMappingURL=PostgrestClient.js.map", "\"use strict\";\n\n// ref: https://github.com/tc39/proposal-global\nvar getGlobal = function() {\n    // the only reliable means to get the global object is\n    // `Function('return this')()`\n    // However, this causes CSP violations in Chrome apps.\n    if (typeof self !== 'undefined') { return self; }\n    if (typeof window !== 'undefined') { return window; }\n    if (typeof global !== 'undefined') { return global; }\n    throw new Error('unable to locate global object');\n}\n\nvar globalObject = getGlobal();\n\nexport const fetch = globalObject.fetch;\n\nexport default globalObject.fetch.bind(globalObject);\n\nexport const Headers = globalObject.Headers;\nexport const Request = globalObject.Request;\nexport const Response = globalObject.Response;\n", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst PostgrestBuilder_1 = __importDefault(require(\"./PostgrestBuilder\"));\nclass PostgrestTransformBuilder extends PostgrestBuilder_1.default {\n    /**\n     * Perform a SELECT on the query result.\n     *\n     * By default, `.insert()`, `.update()`, `.upsert()`, and `.delete()` do not\n     * return modified rows. By calling this method, modified rows are returned in\n     * `data`.\n     *\n     * @param columns - The columns to retrieve, separated by commas\n     */\n    select(columns) {\n        // Remove whitespaces except when quoted\n        let quoted = false;\n        const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*')\n            .split('')\n            .map((c) => {\n            if (/\\s/.test(c) && !quoted) {\n                return '';\n            }\n            if (c === '\"') {\n                quoted = !quoted;\n            }\n            return c;\n        })\n            .join('');\n        this.url.searchParams.set('select', cleanedColumns);\n        if (this.headers['Prefer']) {\n            this.headers['Prefer'] += ',';\n        }\n        this.headers['Prefer'] += 'return=representation';\n        return this;\n    }\n    /**\n     * Order the query result by `column`.\n     *\n     * You can call this method multiple times to order by multiple columns.\n     *\n     * You can order referenced tables, but it only affects the ordering of the\n     * parent table if you use `!inner` in the query.\n     *\n     * @param column - The column to order by\n     * @param options - Named parameters\n     * @param options.ascending - If `true`, the result will be in ascending order\n     * @param options.nullsFirst - If `true`, `null`s appear first. If `false`,\n     * `null`s appear last.\n     * @param options.referencedTable - Set this to order a referenced table by\n     * its columns\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    order(column, { ascending = true, nullsFirst, foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = referencedTable ? `${referencedTable}.order` : 'order';\n        const existingOrder = this.url.searchParams.get(key);\n        this.url.searchParams.set(key, `${existingOrder ? `${existingOrder},` : ''}${column}.${ascending ? 'asc' : 'desc'}${nullsFirst === undefined ? '' : nullsFirst ? '.nullsfirst' : '.nullslast'}`);\n        return this;\n    }\n    /**\n     * Limit the query result by `count`.\n     *\n     * @param count - The maximum number of rows to return\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to limit rows of referenced\n     * tables instead of the parent table\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    limit(count, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`;\n        this.url.searchParams.set(key, `${count}`);\n        return this;\n    }\n    /**\n     * Limit the query result by starting at an offset `from` and ending at the offset `to`.\n     * Only records within this range are returned.\n     * This respects the query order and if there is no order clause the range could behave unexpectedly.\n     * The `from` and `to` values are 0-based and inclusive: `range(1, 3)` will include the second, third\n     * and fourth rows of the query.\n     *\n     * @param from - The starting index from which to limit the result\n     * @param to - The last index to which to limit the result\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to limit rows of referenced\n     * tables instead of the parent table\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    range(from, to, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const keyOffset = typeof referencedTable === 'undefined' ? 'offset' : `${referencedTable}.offset`;\n        const keyLimit = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`;\n        this.url.searchParams.set(keyOffset, `${from}`);\n        // Range is inclusive, so add 1\n        this.url.searchParams.set(keyLimit, `${to - from + 1}`);\n        return this;\n    }\n    /**\n     * Set the AbortSignal for the fetch request.\n     *\n     * @param signal - The AbortSignal to use for the fetch request\n     */\n    abortSignal(signal) {\n        this.signal = signal;\n        return this;\n    }\n    /**\n     * Return `data` as a single object instead of an array of objects.\n     *\n     * Query result must be one row (e.g. using `.limit(1)`), otherwise this\n     * returns an error.\n     */\n    single() {\n        this.headers['Accept'] = 'application/vnd.pgrst.object+json';\n        return this;\n    }\n    /**\n     * Return `data` as a single object instead of an array of objects.\n     *\n     * Query result must be zero or one row (e.g. using `.limit(1)`), otherwise\n     * this returns an error.\n     */\n    maybeSingle() {\n        // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n        // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n        if (this.method === 'GET') {\n            this.headers['Accept'] = 'application/json';\n        }\n        else {\n            this.headers['Accept'] = 'application/vnd.pgrst.object+json';\n        }\n        this.isMaybeSingle = true;\n        return this;\n    }\n    /**\n     * Return `data` as a string in CSV format.\n     */\n    csv() {\n        this.headers['Accept'] = 'text/csv';\n        return this;\n    }\n    /**\n     * Return `data` as an object in [GeoJSON](https://geojson.org) format.\n     */\n    geojson() {\n        this.headers['Accept'] = 'application/geo+json';\n        return this;\n    }\n    /**\n     * Return `data` as the EXPLAIN plan for the query.\n     *\n     * You need to enable the\n     * [db_plan_enabled](https://supabase.com/docs/guides/database/debugging-performance#enabling-explain)\n     * setting before using this method.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.analyze - If `true`, the query will be executed and the\n     * actual run time will be returned\n     *\n     * @param options.verbose - If `true`, the query identifier will be returned\n     * and `data` will include the output columns of the query\n     *\n     * @param options.settings - If `true`, include information on configuration\n     * parameters that affect query planning\n     *\n     * @param options.buffers - If `true`, include information on buffer usage\n     *\n     * @param options.wal - If `true`, include information on WAL record generation\n     *\n     * @param options.format - The format of the output, can be `\"text\"` (default)\n     * or `\"json\"`\n     */\n    explain({ analyze = false, verbose = false, settings = false, buffers = false, wal = false, format = 'text', } = {}) {\n        var _a;\n        const options = [\n            analyze ? 'analyze' : null,\n            verbose ? 'verbose' : null,\n            settings ? 'settings' : null,\n            buffers ? 'buffers' : null,\n            wal ? 'wal' : null,\n        ]\n            .filter(Boolean)\n            .join('|');\n        // An Accept header can carry multiple media types but postgrest-js always sends one\n        const forMediatype = (_a = this.headers['Accept']) !== null && _a !== void 0 ? _a : 'application/json';\n        this.headers['Accept'] = `application/vnd.pgrst.plan+${format}; for=\"${forMediatype}\"; options=${options};`;\n        if (format === 'json')\n            return this;\n        else\n            return this;\n    }\n    /**\n     * Rollback the query.\n     *\n     * `data` will still be returned, but the query is not committed.\n     */\n    rollback() {\n        var _a;\n        if (((_a = this.headers['Prefer']) !== null && _a !== void 0 ? _a : '').trim().length > 0) {\n            this.headers['Prefer'] += ',tx=rollback';\n        }\n        else {\n            this.headers['Prefer'] = 'tx=rollback';\n        }\n        return this;\n    }\n    /**\n     * Override the type of the returned `data`.\n     *\n     * @typeParam NewResult - The new result type to override with\n     * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n     */\n    returns() {\n        return this;\n    }\n}\nexports.default = PostgrestTransformBuilder;\n//# sourceMappingURL=PostgrestTransformBuilder.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n/**\n * Error format\n *\n * {@link https://postgrest.org/en/stable/api.html?highlight=options#errors-and-http-status-codes}\n */\nclass PostgrestError extends Error {\n    constructor(context) {\n        super(context.message);\n        this.name = 'PostgrestError';\n        this.details = context.details;\n        this.hint = context.hint;\n        this.code = context.code;\n    }\n}\nexports.default = PostgrestError;\n//# sourceMappingURL=PostgrestError.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DEFAULT_HEADERS = void 0;\nconst version_1 = require(\"./version\");\nexports.DEFAULT_HEADERS = { 'X-Client-Info': `postgrest-js/${version_1.version}` };\n//# sourceMappingURL=constants.js.map", "'use strict';\n\nmodule.exports = function () {\n  throw new Error(\n    'ws does not work in the browser. Browser clients must use the native ' +\n      'WebSocket object'\n  );\n};\n", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst PostgrestFilterBuilder_1 = __importDefault(require(\"./PostgrestFilterBuilder\"));\nclass PostgrestQueryBuilder {\n    constructor(url, { headers = {}, schema, fetch, }) {\n        this.url = url;\n        this.headers = headers;\n        this.schema = schema;\n        this.fetch = fetch;\n    }\n    /**\n     * Perform a SELECT query on the table or view.\n     *\n     * @param columns - The columns to retrieve, separated by commas. Columns can be renamed when returned with `customName:columnName`\n     *\n     * @param options - Named parameters\n     *\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     *\n     * @param options.count - Count algorithm to use to count rows in the table or view.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    select(columns, { head = false, count, } = {}) {\n        const method = head ? 'HEAD' : 'GET';\n        // Remove whitespaces except when quoted\n        let quoted = false;\n        const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*')\n            .split('')\n            .map((c) => {\n            if (/\\s/.test(c) && !quoted) {\n                return '';\n            }\n            if (c === '\"') {\n                quoted = !quoted;\n            }\n            return c;\n        })\n            .join('');\n        this.url.searchParams.set('select', cleanedColumns);\n        if (count) {\n            this.headers['Prefer'] = `count=${count}`;\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n    /**\n     * Perform an INSERT into the table or view.\n     *\n     * By default, inserted rows are not returned. To return it, chain the call\n     * with `.select()`.\n     *\n     * @param values - The values to insert. Pass an object to insert a single row\n     * or an array to insert multiple rows.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count inserted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     *\n     * @param options.defaultToNull - Make missing fields default to `null`.\n     * Otherwise, use the default value for the column. Only applies for bulk\n     * inserts.\n     */\n    insert(values, { count, defaultToNull = true, } = {}) {\n        const method = 'POST';\n        const prefersHeaders = [];\n        if (this.headers['Prefer']) {\n            prefersHeaders.push(this.headers['Prefer']);\n        }\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        if (!defaultToNull) {\n            prefersHeaders.push('missing=default');\n        }\n        this.headers['Prefer'] = prefersHeaders.join(',');\n        if (Array.isArray(values)) {\n            const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n            if (columns.length > 0) {\n                const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`);\n                this.url.searchParams.set('columns', uniqueColumns.join(','));\n            }\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n    /**\n     * Perform an UPSERT on the table or view. Depending on the column(s) passed\n     * to `onConflict`, `.upsert()` allows you to perform the equivalent of\n     * `.insert()` if a row with the corresponding `onConflict` columns doesn't\n     * exist, or if it does exist, perform an alternative action depending on\n     * `ignoreDuplicates`.\n     *\n     * By default, upserted rows are not returned. To return it, chain the call\n     * with `.select()`.\n     *\n     * @param values - The values to upsert with. Pass an object to upsert a\n     * single row or an array to upsert multiple rows.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.onConflict - Comma-separated UNIQUE column(s) to specify how\n     * duplicate rows are determined. Two rows are duplicates if all the\n     * `onConflict` columns are equal.\n     *\n     * @param options.ignoreDuplicates - If `true`, duplicate rows are ignored. If\n     * `false`, duplicate rows are merged with existing rows.\n     *\n     * @param options.count - Count algorithm to use to count upserted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     *\n     * @param options.defaultToNull - Make missing fields default to `null`.\n     * Otherwise, use the default value for the column. This only applies when\n     * inserting new rows, not when merging with existing rows under\n     * `ignoreDuplicates: false`. This also only applies when doing bulk upserts.\n     */\n    upsert(values, { onConflict, ignoreDuplicates = false, count, defaultToNull = true, } = {}) {\n        const method = 'POST';\n        const prefersHeaders = [`resolution=${ignoreDuplicates ? 'ignore' : 'merge'}-duplicates`];\n        if (onConflict !== undefined)\n            this.url.searchParams.set('on_conflict', onConflict);\n        if (this.headers['Prefer']) {\n            prefersHeaders.push(this.headers['Prefer']);\n        }\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        if (!defaultToNull) {\n            prefersHeaders.push('missing=default');\n        }\n        this.headers['Prefer'] = prefersHeaders.join(',');\n        if (Array.isArray(values)) {\n            const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n            if (columns.length > 0) {\n                const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`);\n                this.url.searchParams.set('columns', uniqueColumns.join(','));\n            }\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n    /**\n     * Perform an UPDATE on the table or view.\n     *\n     * By default, updated rows are not returned. To return it, chain the call\n     * with `.select()` after filters.\n     *\n     * @param values - The values to update with\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count updated rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    update(values, { count, } = {}) {\n        const method = 'PATCH';\n        const prefersHeaders = [];\n        if (this.headers['Prefer']) {\n            prefersHeaders.push(this.headers['Prefer']);\n        }\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        this.headers['Prefer'] = prefersHeaders.join(',');\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n    /**\n     * Perform a DELETE on the table or view.\n     *\n     * By default, deleted rows are not returned. To return it, chain the call\n     * with `.select()` after filters.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count deleted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    delete({ count, } = {}) {\n        const method = 'DELETE';\n        const prefersHeaders = [];\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        if (this.headers['Prefer']) {\n            prefersHeaders.unshift(this.headers['Prefer']);\n        }\n        this.headers['Prefer'] = prefersHeaders.join(',');\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n}\nexports.default = PostgrestQueryBuilder;\n//# sourceMappingURL=PostgrestQueryBuilder.js.map"], "names": [], "sourceRoot": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 49, 50, 51, 52, 53, 54, 55]}