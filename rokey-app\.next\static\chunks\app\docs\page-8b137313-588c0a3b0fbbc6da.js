(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1505],{94522:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var a=t(95155),r=t(12115),i=t(55020),l=t(83161),n=t(56075);function d(e){var s;let{children:t,language:i="javascript",title:n}=e,[d,c]=(0,r.useState)(!1),o=async()=>{await navigator.clipboard.writeText(t),c(!0),setTimeout(()=>c(!1),2e3)};return(0,a.jsxs)("div",{className:"relative bg-gray-900 rounded-xl overflow-hidden border border-gray-300 shadow-lg",children:[n&&(0,a.jsxs)("div",{className:"bg-gray-100 px-4 py-3 text-sm text-gray-700 border-b border-gray-300 flex items-center justify-between",children:[(0,a.jsx)("span",{className:"font-medium",children:n}),(0,a.jsx)("span",{className:"text-xs text-gray-500 uppercase tracking-wide",children:i})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("pre",{className:"p-4 overflow-x-auto text-sm font-mono leading-relaxed",children:(0,a.jsx)("code",{className:"table w-full",children:(s=i||"javascript",t.split("\n").map((e,t)=>{let r=e;return"javascript"===s||"typescript"===s?r=(r=(r=(r=r.replace(/\b(const|let|var|function|async|await|import|export|from|return|if|else|for|while|try|catch|throw|new)\b/g,'<span class="text-purple-400">$1</span>')).replace(/(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,'<span class="text-green-400">$1$2$1</span>')).replace(/(\/\/.*$)/g,'<span class="text-gray-500">$1</span>')).replace(/\b(\d+\.?\d*)\b/g,'<span class="text-yellow-400">$1</span>'):"python"===s?r=(r=(r=r.replace(/\b(import|from|def|class|if|else|elif|for|while|try|except|with|as|return|yield|lambda|and|or|not|in|is|None|True|False)\b/g,'<span class="text-purple-400">$1</span>')).replace(/(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,'<span class="text-green-400">$1$2$1</span>')).replace(/(#.*$)/g,'<span class="text-gray-500">$1</span>'):"bash"===s||"shell"===s?r=(r=(r=r.replace(/\b(curl|echo|export|cd|ls|mkdir|rm|cp|mv|grep|awk|sed)\b/g,'<span class="text-blue-400">$1</span>')).replace(/(-[a-zA-Z]+)/g,'<span class="text-yellow-400">$1</span>')).replace(/(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,'<span class="text-green-400">$1$2$1</span>'):"json"===s&&(r=(r=(r=(r=r.replace(/"([^"]+)":/g,'<span class="text-blue-400">"$1"</span>:')).replace(/:\s*"([^"]*)"/g,': <span class="text-green-400">"$1"</span>')).replace(/:\s*(\d+\.?\d*)/g,': <span class="text-yellow-400">$1</span>')).replace(/:\s*(true|false|null)/g,': <span class="text-purple-400">$1</span>')),(0,a.jsxs)("div",{className:"table-row",children:[(0,a.jsx)("span",{className:"table-cell text-gray-500 text-right pr-4 select-none w-8",children:t+1}),(0,a.jsx)("span",{className:"table-cell text-gray-100",dangerouslySetInnerHTML:{__html:r||" "}})]},t)}))})}),(0,a.jsx)("button",{onClick:o,className:"absolute top-3 right-3 p-2 bg-white/90 hover:bg-white rounded-lg transition-all duration-200 border border-gray-300 shadow-sm",title:"Copy to clipboard",children:d?(0,a.jsx)(l.Sr,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(l.Xx,{className:"h-4 w-4 text-gray-600"})})]})]})}function c(e){let{type:s,children:t}=e,r={info:l.KS,warning:l.Pi,tip:l.BZ}[s];return(0,a.jsx)("div",{className:"border rounded-xl p-4 ".concat({info:"bg-blue-50 border-blue-200 text-blue-800",warning:"bg-yellow-50 border-yellow-200 text-yellow-800",tip:"bg-green-50 border-green-200 text-green-800"}[s]),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)(r,{className:"h-5 w-5 flex-shrink-0 mt-0.5 ".concat({info:"text-blue-600",warning:"text-yellow-600",tip:"text-green-600"}[s])}),(0,a.jsx)("div",{className:"text-sm",children:t})]})})}function o(){let[e,s]=(0,r.useState)("overview"),[t,o]=(0,r.useState)(["overview"]),x=[{id:"overview",title:"Overview",icon:l.AQ,subsections:[{id:"what-is-roukey",title:"What is RouKey"},{id:"key-benefits",title:"Key Benefits"},{id:"how-it-works",title:"How It Works"},{id:"architecture",title:"Architecture"}]},{id:"getting-started",title:"Getting Started",icon:l.ud,subsections:[{id:"quickstart",title:"Quickstart"},{id:"installation",title:"Installation"},{id:"first-request",title:"First Request"},{id:"basic-setup",title:"Basic Setup"}]},{id:"features",title:"Features",icon:l.BZ,subsections:[{id:"intelligent-routing",title:"Intelligent Routing"},{id:"multi-role-orchestration",title:"Multi-Role Orchestration"},{id:"semantic-caching",title:"Semantic Caching"},{id:"knowledge-base",title:"Knowledge Base"},{id:"custom-training",title:"Custom Training"},{id:"async-processing",title:"Async Processing"},{id:"analytics-monitoring",title:"Analytics & Monitoring"}]},{id:"authentication",title:"Authentication",icon:l.RY,subsections:[{id:"api-keys",title:"API Keys"},{id:"auth-methods",title:"Authentication Methods"},{id:"security",title:"Security"},{id:"rate-limiting",title:"Rate Limiting"}]},{id:"api-reference",title:"API Reference",icon:l.r$,subsections:[{id:"base-url",title:"Base URL"},{id:"chat-completions",title:"Chat Completions"},{id:"streaming",title:"Streaming"},{id:"async-endpoints",title:"Async Endpoints"},{id:"image-support",title:"Image Support"},{id:"parameters",title:"Parameters"},{id:"responses",title:"Responses"},{id:"errors",title:"Error Handling"}]},{id:"routing-strategies",title:"Routing Strategies",icon:l.DQ,subsections:[{id:"strategy-overview",title:"Strategy Overview"},{id:"intelligent-role-routing",title:"Intelligent Role Routing"},{id:"complexity-routing",title:"Complexity-Based Routing"},{id:"cost-optimized",title:"Cost-Optimized Routing"},{id:"strict-fallback",title:"Strict Fallback"},{id:"ab-testing",title:"A/B Testing"}]},{id:"configuration",title:"Configuration",icon:l.DP,subsections:[{id:"dashboard-setup",title:"Dashboard Setup"},{id:"provider-keys",title:"Provider API Keys"},{id:"routing-config",title:"Routing Configuration"},{id:"custom-roles",title:"Custom Roles"},{id:"temperature-settings",title:"Temperature Settings"}]},{id:"use-cases",title:"Use Cases",icon:l.XL,subsections:[{id:"development-coding",title:"Development & Coding"},{id:"content-creation",title:"Content Creation"},{id:"enterprise-apps",title:"Enterprise Applications"},{id:"educational-platforms",title:"Educational Platforms"},{id:"research-analysis",title:"Research & Analysis"}]},{id:"examples",title:"Examples",icon:l.XR,subsections:[{id:"javascript-examples",title:"JavaScript/Node.js"},{id:"python-examples",title:"Python"},{id:"curl-examples",title:"cURL"},{id:"openai-sdk",title:"OpenAI SDK Integration"}]},{id:"future-releases",title:"Future Releases",icon:l.P,subsections:[{id:"q1-2025",title:"Q1 2025 - Workflow Automation"},{id:"q2-2025",title:"Q2 2025 - Performance & Scale"},{id:"q3-2025",title:"Q3 2025 - AI-Powered Features"},{id:"q4-2025",title:"Q4 2025 - Enterprise Features"}]},{id:"faq",title:"FAQ",icon:l.R2,subsections:[]}];return(0,r.useEffect)(()=>{let e=()=>{let e=window.location.hash.replace("#","");if(e){s(e);let t=x.find(s=>s.id===e);t&&t.subsections.length>0&&o(s=>s.includes(e)?s:[...s,e])}};return e(),window.addEventListener("hashchange",e),()=>{window.removeEventListener("hashchange",e)}},[]),(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsx)(n.A,{}),(0,a.jsxs)("div",{className:"pt-16 flex flex-col lg:flex-row min-h-screen",children:[(0,a.jsx)("div",{className:"w-full lg:w-80 flex-shrink-0 bg-white border-r border-gray-200 shadow-sm lg:fixed lg:top-16 lg:left-0 lg:h-[calc(100vh-4rem)] lg:z-10 lg:overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-4 lg:p-6",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"RouKey Documentation"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Complete guide to integrating and using RouKey's intelligent AI gateway"})]}),(0,a.jsx)("nav",{className:"space-y-1",children:x.map(r=>{let i=r.icon,n=t.includes(r.id),d=r.subsections&&r.subsections.length>0;return(0,a.jsxs)("div",{children:[(0,a.jsxs)("button",{onClick:()=>{s(r.id),d&&o(e=>n?e.filter(e=>e!==r.id):[...e,r.id])},className:"w-full flex items-center gap-3 px-4 py-3 text-sm rounded-xl transition-all duration-200 ".concat(e===r.id?"bg-[#ff6b35] text-white shadow-lg shadow-orange-500/25":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"),children:[(0,a.jsx)(i,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"flex-1 text-left",children:r.title}),d&&(0,a.jsx)(l.vK,{className:"h-4 w-4 transition-transform duration-200 ".concat(n?"rotate-90":"")})]}),d&&n&&(0,a.jsx)("div",{className:"ml-8 mt-1 space-y-1",children:r.subsections.map(t=>(0,a.jsx)("button",{onClick:()=>s("".concat(r.id,"-").concat(t.id)),className:"w-full text-left px-3 py-2 text-xs rounded-lg transition-all duration-200 ".concat(e==="".concat(r.id,"-").concat(t.id)?"bg-[#ff6b35]/10 text-[#ff6b35] font-medium":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:t.title},t.id))})]},r.id)})}),(0,a.jsxs)("div",{className:"mt-8 p-4 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(l.BZ,{className:"h-4 w-4 text-[#ff6b35]"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Quick Start"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-600 mb-3",children:"Get up and running in under 2 minutes"}),(0,a.jsx)("a",{href:"/pricing",className:"block w-full bg-[#ff6b35] hover:bg-[#e55a2b] text-white text-xs py-2 px-3 rounded-lg transition-colors text-center",children:"Start Building"})]})]})}),(0,a.jsx)("div",{className:"flex-1 bg-white text-gray-900 min-h-screen lg:ml-80",children:(0,a.jsx)("div",{className:"max-w-6xl mx-auto p-3 lg:p-6 lg:py-8",children:(0,a.jsxs)(i.PY1.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:["overview"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"RouKey Overview"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6 leading-relaxed",children:"RouKey is a commercial BYOK (Bring Your Own Keys) framework that combines multiple AI routers and a gateway to optimize your LLM API usage through intelligent routing strategies."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 p-4 rounded-xl border border-[#ff6b35]/20 hover:shadow-lg hover:border-[#ff6b35]/30 transition-all duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)(l.AQ,{className:"h-5 w-5 text-[#ff6b35]"}),"What is RouKey?"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Learn about RouKey's core concepts and capabilities"}),(0,a.jsxs)("button",{onClick:()=>s("overview-what-is-roukey"),className:"text-[#ff6b35] hover:text-[#e55a2b] font-medium flex items-center gap-1 transition-colors",children:["Learn more ",(0,a.jsx)(l.vK,{className:"h-4 w-4"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-blue-100/50 p-4 rounded-xl border border-blue-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)(l.BZ,{className:"h-5 w-5 text-blue-600"}),"Key Benefits"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Discover how RouKey optimizes your AI operations"}),(0,a.jsxs)("button",{onClick:()=>s("overview-key-benefits"),className:"text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1 transition-colors",children:["Explore benefits ",(0,a.jsx)(l.vK,{className:"h-4 w-4"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-green-100/50 p-4 rounded-xl border border-green-200 hover:shadow-lg hover:border-green-300 transition-all duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)(l.DP,{className:"h-5 w-5 text-green-600"}),"How It Works"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Understand RouKey's intelligent routing process"}),(0,a.jsxs)("button",{onClick:()=>s("overview-how-it-works"),className:"text-green-600 hover:text-green-700 font-medium flex items-center gap-1 transition-colors",children:["See how ",(0,a.jsx)(l.vK,{className:"h-4 w-4"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-purple-100/50 p-4 rounded-xl border border-purple-200 hover:shadow-lg hover:border-purple-300 transition-all duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)(l.OL,{className:"h-5 w-5 text-purple-600"}),"Architecture"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Deep dive into RouKey's technical architecture"}),(0,a.jsxs)("button",{onClick:()=>s("overview-architecture"),className:"text-purple-600 hover:text-purple-700 font-medium flex items-center gap-1 transition-colors",children:["View architecture ",(0,a.jsx)(l.vK,{className:"h-4 w-4"})]})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"How RouKey Works"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:"1"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Request Analysis"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"RouKey analyzes incoming requests using AI classification to understand complexity, role requirements, and optimal routing strategy."})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:"2"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Intelligent Routing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Based on your configured strategy, RouKey routes the request to the optimal model considering cost, performance, and availability."})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:"3"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Response Delivery"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"RouKey delivers the response with automatic failover, retry logic, and comprehensive analytics for monitoring and optimization."})]})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Key Benefits"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0 mt-1",children:(0,a.jsx)(l.BZ,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 text-lg mb-2",children:"Cost Optimization"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Reduce API costs by up to 60% through intelligent routing to cost-effective models for appropriate tasks."})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1",children:(0,a.jsx)(l.DQ,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 text-lg mb-2",children:"Enhanced Reliability"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Automatic failover and retry mechanisms ensure 99.9% uptime with seamless provider switching."})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1",children:(0,a.jsx)(l.DP,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 text-lg mb-2",children:"Easy Integration"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Drop-in replacement for OpenAI API with full compatibility and additional routing capabilities."})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1",children:(0,a.jsx)(l.RY,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 text-lg mb-2",children:"Enterprise Security"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Your API keys stay secure with enterprise-grade encryption and never leave your control."})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-6 rounded-xl text-white",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Ready to optimize your AI costs?"}),(0,a.jsx)("p",{className:"text-white/90 mb-4",children:"Join thousands of developers who have reduced their AI API costs by up to 60% with RouKey's intelligent routing."}),(0,a.jsx)("button",{onClick:()=>s("getting-started"),className:"bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors",children:"Get Started Now"})]})]}),"overview-what-is-roukey"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"What is RouKey?"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6 leading-relaxed",children:"RouKey is an intelligent AI gateway that revolutionizes how you interact with multiple LLM providers."})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Core Concept"}),(0,a.jsxs)("div",{className:"prose prose-gray max-w-none space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed text-lg",children:"RouKey is an intelligent AI gateway that sits between your application and multiple LLM providers (OpenAI, Anthropic, Google, DeepSeek, xAI, and more). It automatically routes requests to the most appropriate model based on your configured strategies, providing cost optimization, improved reliability, and enhanced performance."}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed text-lg",children:"Unlike traditional API proxies, RouKey uses advanced AI classification to understand your requests and make intelligent routing decisions. This means you get the right model for the right task, every time."})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"BYOK Framework"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"RouKey follows a Bring Your Own Keys (BYOK) model, ensuring you maintain complete control over your API keys and costs."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.RY,{className:"h-5 w-5 text-green-600"}),"Your Keys, Your Control"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• You provide your own API keys"}),(0,a.jsx)("li",{children:"• Direct billing from providers"}),(0,a.jsx)("li",{children:"• No markup on API costs"}),(0,a.jsx)("li",{children:"• Full transparency"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.Zu,{className:"h-5 w-5 text-blue-600"}),"Enterprise Security"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• AES-256 encryption"}),(0,a.jsx)("li",{children:"• Keys never leave your control"}),(0,a.jsx)("li",{children:"• No response logging"}),(0,a.jsx)("li",{children:"• SOC 2 compliance ready"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Supported Providers"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 mb-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("div",{className:"font-semibold text-gray-900 mb-1",children:"OpenAI"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"GPT-4, GPT o3, o1"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("div",{className:"font-semibold text-gray-900 mb-1",children:"Anthropic"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Claude 4 Opus, Claude 3"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("div",{className:"font-semibold text-gray-900 mb-1",children:"Google"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Gemini 2.5 Pro, Flash"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("div",{className:"font-semibold text-gray-900 mb-1",children:"DeepSeek"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"v3, R1 0528"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("div",{className:"font-semibold text-gray-900 mb-1",children:"xAI"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Grok Models"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("div",{className:"font-semibold text-gray-900 mb-1",children:"OpenRouter"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"300+ Models"})]})]}),(0,a.jsxs)("p",{className:"text-gray-600 text-center",children:["Access to ",(0,a.jsx)("strong",{children:"300+ AI models"})," from leading providers with unified API interface"]})]})]}),"overview-key-benefits"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Key Benefits"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6 leading-relaxed",children:"Discover how RouKey transforms your AI operations with intelligent routing and cost optimization."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-[#ff6b35] rounded-xl flex items-center justify-center",children:(0,a.jsx)(l.BZ,{className:"h-5 w-5 text-white"})}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Cost Optimization"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Reduce API costs by up to 60% through intelligent routing to cost-effective models for appropriate tasks."}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Complexity-based routing to cheaper models for simple tasks"}),(0,a.jsx)("li",{children:"• Semantic caching prevents duplicate API calls"}),(0,a.jsx)("li",{children:"• Real-time cost tracking and optimization"}),(0,a.jsx)("li",{children:"• No markup on provider costs"})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200",children:[(0,a.jsx)("div",{className:"text-green-800 font-medium mb-1",children:"Average Savings"}),(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"40-60%"}),(0,a.jsx)("div",{className:"text-green-600 text-sm",children:"on monthly API costs"})]})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center",children:(0,a.jsx)(l.DQ,{className:"h-5 w-5 text-white"})}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Enhanced Reliability"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Automatic failover and retry mechanisms ensure 99.9% uptime with seamless provider switching."}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Automatic failover between providers"}),(0,a.jsx)("li",{children:"• Intelligent retry logic with exponential backoff"}),(0,a.jsx)("li",{children:"• Real-time health monitoring"}),(0,a.jsx)("li",{children:"• Circuit breaker patterns"})]}),(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:[(0,a.jsx)("div",{className:"text-blue-800 font-medium mb-1",children:"Uptime SLA"}),(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"99.9%"}),(0,a.jsx)("div",{className:"text-blue-600 text-sm",children:"guaranteed availability"})]})]})]})]})]}),"overview-how-it-works"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"How RouKey Works"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6 leading-relaxed",children:"Understanding RouKey's intelligent routing process and decision-making algorithms."})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Request Flow"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-white font-bold text-xl",children:"1"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Request Analysis"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"RouKey analyzes incoming requests using AI classification to understand complexity, role requirements, and optimal routing strategy."}),(0,a.jsx)("div",{className:"text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded",children:"~50ms processing"})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-[#ff6b35] rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-white font-bold text-xl",children:"2"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Intelligent Routing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Based on your configured strategy, RouKey routes the request to the optimal model considering cost, performance, and availability."}),(0,a.jsx)("div",{className:"text-xs text-[#ff6b35] bg-[#ff6b35]/10 px-2 py-1 rounded",children:"Real-time decisions"})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-white font-bold text-xl",children:"3"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Response Delivery"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"RouKey delivers the response with automatic failover, retry logic, and comprehensive analytics for monitoring and optimization."}),(0,a.jsx)("div",{className:"text-xs text-green-600 bg-green-50 px-2 py-1 rounded",children:"Sub-second latency"})]})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"AI Classification Engine"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"RouKey uses advanced Gemini-powered classification to understand your requests and make optimal routing decisions."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.YE,{className:"h-5 w-5 text-purple-600"}),"Complexity Analysis"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Analyzes prompt complexity on a 1-5 scale to determine the most cost-effective model."}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Simple (1-2)"}),(0,a.jsx)("span",{className:"text-green-600",children:"GPT-4, Gemini Flash"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Complex (4-5)"}),(0,a.jsx)("span",{className:"text-blue-600",children:"GPT o3, Claude 4 Opus"})]})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-indigo-50 to-indigo-100/50 rounded-xl border border-indigo-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.K6,{className:"h-5 w-5 text-indigo-600"}),"Role Detection"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Identifies the type of task to route to specialized models."}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Coding"}),(0,a.jsx)("span",{className:"text-green-600",children:"Deepseek R1 0528"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Writing"}),(0,a.jsx)("span",{className:"text-blue-600",children:"Claude 4 Opus"})]})]})]})]})]})]})]}),"overview-architecture"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"RouKey Architecture"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6 leading-relaxed",children:"Deep dive into RouKey's technical architecture and infrastructure design."})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"System Architecture"}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-gray-50 p-6 rounded-xl border border-gray-200",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"RouKey Gateway Architecture"}),(0,a.jsx)("p",{className:"text-gray-600",children:"High-level overview of RouKey's distributed system"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4 items-center",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-white rounded-lg border border-gray-200",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 mb-1",children:"Client App"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Your Application"})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(l.vK,{className:"h-6 w-6 text-gray-400 mx-auto"})}),(0,a.jsxs)("div",{className:"text-center p-4 bg-[#ff6b35]/10 rounded-lg border border-[#ff6b35]/20",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 mb-1",children:"RouKey Gateway"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Intelligent Router"})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(l.vK,{className:"h-6 w-6 text-gray-400 mx-auto"})}),(0,a.jsxs)("div",{className:"text-center p-4 bg-white rounded-lg border border-gray-200",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 mb-1",children:"LLM Providers"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"OpenAI, Anthropic, etc."})]})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Core Components"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1",children:(0,a.jsx)(l.YE,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Classification Engine"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Gemini-powered request analysis and routing decisions"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1",children:(0,a.jsx)(l.OL,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Semantic Cache"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"RouKey embeddings with reranking for intelligent caching"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1",children:(0,a.jsx)(l.DQ,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"RouKey Orchestration"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Multi-role workflow management and coordination"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0 mt-1",children:(0,a.jsx)(l.r9,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Analytics Engine"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Real-time monitoring and performance optimization"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Infrastructure"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1",children:(0,a.jsx)(l.hp,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Vercel Edge Network"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Global edge deployment for minimal latency"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-teal-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1",children:(0,a.jsx)(l.OL,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Supabase Database"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"PostgreSQL with real-time subscriptions"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1",children:(0,a.jsx)(l.Zu,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Security Layer"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"AES-256 encryption and secure key management"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1",children:(0,a.jsx)(l.DQ,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Load Balancing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Intelligent request distribution and failover"})]})]})]})]})]})]}),"getting-started"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Getting Started with RouKey"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6 leading-relaxed",children:"Get up and running with RouKey in under 2 minutes. Follow our step-by-step guides to integrate RouKey into your application."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 p-4 rounded-xl border border-[#ff6b35]/20 hover:shadow-lg hover:border-[#ff6b35]/30 transition-all duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)(l.DQ,{className:"h-5 w-5 text-[#ff6b35]"}),"Quickstart"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Get RouKey running in 2 minutes with our fastest setup guide"}),(0,a.jsxs)("button",{onClick:()=>s("getting-started-quickstart"),className:"text-[#ff6b35] hover:text-[#e55a2b] font-medium flex items-center gap-1 transition-colors",children:["Start now ",(0,a.jsx)(l.vK,{className:"h-4 w-4"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-blue-100/50 p-4 rounded-xl border border-blue-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)(l.DP,{className:"h-5 w-5 text-blue-600"}),"Installation"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Detailed installation and setup instructions"}),(0,a.jsxs)("button",{onClick:()=>s("getting-started-installation"),className:"text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1 transition-colors",children:["View guide ",(0,a.jsx)(l.vK,{className:"h-4 w-4"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-green-100/50 p-4 rounded-xl border border-green-200 hover:shadow-lg hover:border-green-300 transition-all duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)(l.ud,{className:"h-5 w-5 text-green-600"}),"First Request"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Make your first API call and see RouKey in action"}),(0,a.jsxs)("button",{onClick:()=>s("getting-started-first-request"),className:"text-green-600 hover:text-green-700 font-medium flex items-center gap-1 transition-colors",children:["Try it out ",(0,a.jsx)(l.vK,{className:"h-4 w-4"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-purple-100/50 p-4 rounded-xl border border-purple-200 hover:shadow-lg hover:border-purple-300 transition-all duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)(l.DP,{className:"h-5 w-5 text-purple-600"}),"Basic Setup"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Configure routing strategies and optimize your setup"}),(0,a.jsxs)("button",{onClick:()=>s("getting-started-basic-setup"),className:"text-purple-600 hover:text-purple-700 font-medium flex items-center gap-1 transition-colors",children:["Configure ",(0,a.jsx)(l.vK,{className:"h-4 w-4"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-6 rounded-xl text-white",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Need help getting started?"}),(0,a.jsx)("p",{className:"text-white/90 mb-4",children:"Our quickstart guide will have you up and running in under 2 minutes with your first RouKey API call."}),(0,a.jsx)("button",{onClick:()=>s("getting-started-quickstart"),className:"bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors",children:"Start Quickstart Guide"})]})]}),"getting-started-quickstart"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Quickstart Guide"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6 leading-relaxed",children:"Get RouKey up and running in under 2 minutes with this step-by-step guide."})]}),(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-xl border border-blue-200 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsx)(l.KS,{className:"h-6 w-6 text-blue-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-blue-900",children:"Prerequisites"})]}),(0,a.jsxs)("ul",{className:"space-y-2 text-blue-800",children:[(0,a.jsx)("li",{children:"• API keys from at least one LLM provider (OpenAI, Anthropic, etc.)"}),(0,a.jsxs)("li",{children:["• A RouKey account (sign up at ",(0,a.jsx)("a",{href:"https://roukey.online",className:"underline",children:"roukey.online"}),")"]}),(0,a.jsx)("li",{children:"• Basic knowledge of REST APIs"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsx)("div",{className:"w-7 h-7 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm",children:"1"}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Create Your Account"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Sign up for a free RouKey account to get started with intelligent AI routing."}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Visit:"}),(0,a.jsx)("a",{href:"https://roukey.online",className:"text-[#ff6b35] hover:text-[#e55a2b] font-medium underline",children:"https://roukey.online"})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsx)("div",{className:"w-7 h-7 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm",children:"2"}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Add Provider API Keys"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Add your LLM provider API keys to enable RouKey's intelligent routing."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Supported Providers:"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,a.jsx)("li",{children:"• OpenAI"}),(0,a.jsx)("li",{children:"• Anthropic"}),(0,a.jsx)("li",{children:"• Google"}),(0,a.jsx)("li",{children:"• DeepSeek"}),(0,a.jsx)("li",{children:"• xAI"}),(0,a.jsx)("li",{children:"• OpenRouter"})]})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Security:"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Your keys are encrypted with AES-256 and never leave your control."})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsx)("div",{className:"w-7 h-7 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm",children:"3"}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Generate Your RouKey API Key"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Create a user API key to access RouKey's intelligent routing from your application."}),(0,a.jsx)("div",{className:"bg-yellow-50 p-4 rounded-lg border border-yellow-200",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-800",children:[(0,a.jsx)("strong",{children:"Important:"})," Your API key will be displayed only once. Make sure to copy and store it securely."]})})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsx)("div",{className:"w-7 h-7 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm",children:"4"}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Make Your First Request"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Test your setup with a simple API call to RouKey's chat completions endpoint."}),(0,a.jsx)(d,{title:"Your first RouKey API call",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [\n      {"role": "user", "content": "Hello RouKey! How does intelligent routing work?"}\n    ],\n    "stream": false\n  }\''})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-xl text-white",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"\uD83C\uDF89 Congratulations!"}),(0,a.jsx)("p",{className:"text-white/90 mb-4",children:"You've successfully set up RouKey and made your first API call. RouKey is now intelligently routing your requests to optimize cost and performance."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("button",{onClick:()=>s("getting-started-basic-setup"),className:"bg-white text-green-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors",children:"Configure Routing Strategies"}),(0,a.jsx)("button",{onClick:()=>s("api-reference"),className:"bg-white/20 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/30 transition-colors",children:"Explore API Reference"})]})]})]}),"getting-started-installation"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Installation Guide"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6 leading-relaxed",children:"Detailed installation and setup instructions for integrating RouKey into your application."})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"SDK Installation"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"RouKey is compatible with existing OpenAI SDKs. Simply change the base URL to start using RouKey's intelligent routing."}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"JavaScript/Node.js"}),(0,a.jsx)(d,{title:"Install OpenAI SDK",language:"bash",children:"npm install openai"}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(d,{title:"Configure for RouKey",language:"javascript",children:"import OpenAI from 'openai';\n\nconst openai = new OpenAI({\n  apiKey: 'rk_live_your_api_key_here',\n  baseURL: 'https://roukey.online/api/external/v1',\n  defaultHeaders: {\n    'X-API-Key': 'rk_live_your_api_key_here'\n  }\n});\n\n// Use exactly like OpenAI\nconst completion = await openai.chat.completions.create({\n  messages: [{ role: 'user', content: 'Hello RouKey!' }],\n  model: 'gpt-4', // RouKey will intelligently route this\n});"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Python"}),(0,a.jsx)(d,{title:"Install OpenAI SDK",language:"bash",children:"pip install openai"}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(d,{title:"Configure for RouKey",language:"python",children:'from openai import OpenAI\n\nclient = OpenAI(\n    api_key="rk_live_your_api_key_here",\n    base_url="https://roukey.online/api/external/v1",\n    default_headers={"X-API-Key": "rk_live_your_api_key_here"}\n)\n\n# Use exactly like OpenAI\ncompletion = client.chat.completions.create(\n    messages=[{"role": "user", "content": "Hello RouKey!"}],\n    model="gpt-4"  # RouKey will intelligently route this\n)'})})]})]})]})]}),"getting-started-first-request"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Your First Request"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Learn how to make your first API call to RouKey and understand the response format."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Basic Request"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Here's a simple example of making your first request to RouKey's chat completions endpoint:"}),(0,a.jsx)(d,{title:"Basic chat completion request",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [\n      {"role": "user", "content": "Explain quantum computing in simple terms"}\n    ],\n    "stream": false,\n    "temperature": 0.7\n  }\''})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Understanding the Response"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"RouKey returns OpenAI-compatible responses with additional metadata about routing decisions:"}),(0,a.jsx)(d,{title:"Example response",language:"json",children:'{\n  "id": "chatcmpl-abc123",\n  "object": "chat.completion",\n  "created": **********,\n  "model": "routed-model",\n  "choices": [\n    {\n      "index": 0,\n      "message": {\n        "role": "assistant",\n        "content": "Quantum computing is like having a super-powered calculator..."\n      },\n      "finish_reason": "stop"\n    }\n  ],\n  "usage": {\n    "prompt_tokens": 12,\n    "completion_tokens": 150,\n    "total_tokens": 162\n  },\n  "roukey_metadata": {\n    "routing_strategy": "intelligent_role",\n    "selected_provider": "openai",\n    "complexity_score": 3,\n    "estimated_cost": 0.0024\n  }\n}'})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Request with Role-Based Routing"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Use RouKey's role parameter to optimize routing for specific tasks:"}),(0,a.jsx)(d,{title:"Role-based routing request",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [\n      {"role": "user", "content": "Write a Python function to calculate fibonacci numbers"}\n    ],\n    "role": "coding",\n    "stream": true,\n    "max_tokens": 1000\n  }\''}),(0,a.jsx)("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200 mt-4",children:(0,a.jsxs)("p",{className:"text-blue-800 text-sm",children:[(0,a.jsx)("strong",{children:"Tip:"})," Using ",(0,a.jsx)("code",{children:'role: "coding"'})," will route your request to models optimized for code generation like Deepseek R1 0528."]})})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Streaming Responses"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"For better user experience and to avoid timeouts, use streaming responses:"}),(0,a.jsx)(d,{title:"JavaScript streaming example",language:"javascript",children:"const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {\n  method: 'POST',\n  headers: {\n    'Content-Type': 'application/json',\n    'X-API-Key': 'rk_live_your_api_key_here'\n  },\n  body: JSON.stringify({\n    messages: [{ role: 'user', content: 'Write a story about AI' }],\n    stream: true,\n    role: 'writing'\n  })\n});\n\nconst reader = response.body.getReader();\nconst decoder = new TextDecoder();\n\nwhile (true) {\n  const { done, value } = await reader.read();\n  if (done) break;\n\n  const chunk = decoder.decode(value);\n  const lines = chunk.split('\\n');\n\n  for (const line of lines) {\n    if (line.startsWith('data: ')) {\n      const data = line.slice(6);\n      if (data === '[DONE]') return;\n\n      try {\n        const parsed = JSON.parse(data);\n        const content = parsed.choices[0]?.delta?.content;\n        if (content) {\n          console.log(content); // Stream the content\n        }\n      } catch (e) {\n        // Handle parsing errors\n      }\n    }\n  }\n}"})]})]}),"getting-started-basic-setup"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Basic Setup"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Configure RouKey's routing strategies and optimize your setup for your specific use case."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Choosing a Routing Strategy"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"RouKey offers several routing strategies. Choose the one that best fits your use case:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.DQ,{className:"h-5 w-5 text-blue-600"}),"Intelligent Role Routing"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"AI-powered classification routes requests based on detected roles (coding, writing, analysis, etc.) to specialized models."}),(0,a.jsx)("div",{className:"text-sm text-blue-600 font-medium",children:"Best for: Multi-purpose applications"})]}),(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.OL,{className:"h-5 w-5 text-green-600"}),"Complexity-Based Routing"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Analyzes prompt complexity (1-5 scale) and routes to appropriate models for optimal cost-performance balance."}),(0,a.jsx)("div",{className:"text-sm text-green-600 font-medium",children:"Best for: Cost optimization"})]}),(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.yq,{className:"h-5 w-5 text-[#ff6b35]"}),"Strict Fallback Strategy"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Ordered failover sequence with predictable routing and guaranteed fallback chain for maximum reliability."}),(0,a.jsx)("div",{className:"text-sm text-[#ff6b35] font-medium",children:"Best for: Mission-critical applications"})]}),(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.BZ,{className:"h-5 w-5 text-purple-600"}),"Cost-Optimized Routing"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Smart cost optimization with learning algorithms that adapt to your usage patterns over time."}),(0,a.jsx)("div",{className:"text-sm text-purple-600 font-medium",children:"Best for: Budget-conscious deployments"})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Custom Roles Configuration"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Create custom roles to optimize routing for your specific use cases:"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:'Example: Custom "Data Analysis" Role'}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Primary: Claude 4 Opus (excellent for analysis)"}),(0,a.jsx)("li",{children:"• Fallback: Gemini 2.5 Pro (reliable backup)"}),(0,a.jsx)("li",{children:"• Cost-effective: GPT-4 (for simple queries)"})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:'Example: Custom "Code Review" Role'}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Primary: GPT o3 (excellent for coding)"}),(0,a.jsx)("li",{children:"• Fallback: Deepseek R1 0528 (specialized for code)"}),(0,a.jsx)("li",{children:"• Fast: GPT-4 (for simple reviews)"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-8 rounded-xl text-white",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Ready to optimize further?"}),(0,a.jsx)("p",{className:"text-white/90 mb-6",children:"Explore advanced features like semantic caching, knowledge base integration, and multi-role orchestration."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("button",{onClick:()=>s("features"),className:"bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors",children:"Explore Features"}),(0,a.jsx)("button",{onClick:()=>s("routing-strategies"),className:"bg-white/20 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/30 transition-colors",children:"Advanced Routing"})]})]})]}),"features"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"RouKey Features"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6 leading-relaxed",children:"Discover the comprehensive features that make RouKey the leading AI gateway solution for developers and enterprises."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-blue-100/50 p-4 rounded-xl border border-blue-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)(l.DQ,{className:"h-5 w-5 text-blue-600"}),"Intelligent Routing"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"AI-powered request classification and optimal model selection"}),(0,a.jsxs)("button",{onClick:()=>s("features-intelligent-routing"),className:"text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1 transition-colors",children:["Learn more ",(0,a.jsx)(l.vK,{className:"h-4 w-4"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-purple-100/50 p-4 rounded-xl border border-purple-200 hover:shadow-lg hover:border-purple-300 transition-all duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)(l.K6,{className:"h-5 w-5 text-purple-600"}),"Multi-Role Orchestration"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Advanced workflow management with RouKey integration"}),(0,a.jsxs)("button",{onClick:()=>s("features-multi-role-orchestration"),className:"text-purple-600 hover:text-purple-700 font-medium flex items-center gap-1 transition-colors",children:["Explore workflows ",(0,a.jsx)(l.vK,{className:"h-4 w-4"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-green-100/50 p-4 rounded-xl border border-green-200 hover:shadow-lg hover:border-green-300 transition-all duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)(l.OL,{className:"h-5 w-5 text-green-600"}),"Semantic Caching"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Intelligent caching with embeddings and reranking"}),(0,a.jsxs)("button",{onClick:()=>s("features-semantic-caching"),className:"text-green-600 hover:text-green-700 font-medium flex items-center gap-1 transition-colors",children:["See caching ",(0,a.jsx)(l.vK,{className:"h-4 w-4"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 p-4 rounded-xl border border-[#ff6b35]/20 hover:shadow-lg hover:border-[#ff6b35]/30 transition-all duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)(l.Ru,{className:"h-5 w-5 text-[#ff6b35]"}),"Knowledge Base"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Upload documents for enhanced AI responses"}),(0,a.jsxs)("button",{onClick:()=>s("features-knowledge-base"),className:"text-[#ff6b35] hover:text-[#e55a2b] font-medium flex items-center gap-1 transition-colors",children:["Upload docs ",(0,a.jsx)(l.vK,{className:"h-4 w-4"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-indigo-50 to-indigo-100/50 p-4 rounded-xl border border-indigo-200 hover:shadow-lg hover:border-indigo-300 transition-all duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)(l.DP,{className:"h-5 w-5 text-indigo-600"}),"Custom Training"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Train models with your specific data and requirements"}),(0,a.jsxs)("button",{onClick:()=>s("features-custom-training"),className:"text-indigo-600 hover:text-indigo-700 font-medium flex items-center gap-1 transition-colors",children:["Start training ",(0,a.jsx)(l.vK,{className:"h-4 w-4"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-teal-50 to-teal-100/50 p-4 rounded-xl border border-teal-200 hover:shadow-lg hover:border-teal-300 transition-all duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)(l.DQ,{className:"h-5 w-5 text-teal-600"}),"Async Processing"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Handle long-running tasks with webhook notifications"}),(0,a.jsxs)("button",{onClick:()=>s("features-async-processing"),className:"text-teal-600 hover:text-teal-700 font-medium flex items-center gap-1 transition-colors",children:["Process async ",(0,a.jsx)(l.vK,{className:"h-4 w-4"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-yellow-50 to-yellow-100/50 p-4 rounded-xl border border-yellow-200 hover:shadow-lg hover:border-yellow-300 transition-all duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)(l.r9,{className:"h-5 w-5 text-yellow-600"}),"Analytics & Monitoring"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Real-time insights and performance monitoring"}),(0,a.jsxs)("button",{onClick:()=>s("features-analytics-monitoring"),className:"text-yellow-600 hover:text-yellow-700 font-medium flex items-center gap-1 transition-colors",children:["View analytics ",(0,a.jsx)(l.vK,{className:"h-4 w-4"})]})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Multi-Role Orchestration"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"RouKey's advanced orchestration integration enables sophisticated multi-role task orchestration with automatic workflow detection and management."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Workflow Types"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Sequential:"})," Single roles, step-by-step processing"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Supervisor:"})," 2-3 roles with coordinated execution"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Hierarchical:"})," 4+ roles or complex browsing tasks"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Auto:"})," Intelligent workflow selection"]})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Advanced Features"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Memory Management:"})," Context preservation across roles"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Streaming Support:"})," Real-time progress updates"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Error Recovery:"})," Automatic retry and fallback"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Progress Tracking:"})," Detailed execution monitoring"]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Provider Support"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("div",{className:"font-semibold text-gray-900 mb-1",children:"OpenAI"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"GPT-4, GPT o3, o1"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("div",{className:"font-semibold text-gray-900 mb-1",children:"Anthropic"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Claude 4 Opus, Claude 3"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("div",{className:"font-semibold text-gray-900 mb-1",children:"Google"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Gemini 2.5 Pro, Flash"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("div",{className:"font-semibold text-gray-900 mb-1",children:"DeepSeek"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"v3, R1 0528"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("div",{className:"font-semibold text-gray-900 mb-1",children:"xAI"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Grok Models"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("div",{className:"font-semibold text-gray-900 mb-1",children:"OpenRouter"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"300+ Models"})]})]}),(0,a.jsxs)("p",{className:"text-gray-600 mt-6 text-center",children:["Access to ",(0,a.jsx)("strong",{children:"300+ AI models"})," from leading providers with unified API interface"]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Advanced Capabilities"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"RouKey Semantic Caching"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"RouKey's intelligent caching system uses advanced semantic analysis and reranking for faster responses and reduced costs."}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• RouKey embedding-based similarity detection"}),(0,a.jsx)("li",{children:"• Advanced reranking for result optimization"}),(0,a.jsx)("li",{children:"• Automatic cache invalidation"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Knowledge Base Integration"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Upload and integrate custom documents for enhanced AI responses with your specific knowledge."}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Document upload and processing"}),(0,a.jsx)("li",{children:"• Automatic embedding generation"}),(0,a.jsx)("li",{children:"• Context-aware retrieval"}),(0,a.jsx)("li",{children:"• Tier-based document limits"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Custom Training"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Train models with your specific data and requirements for specialized use cases."}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• File upload and processing"}),(0,a.jsx)("li",{children:"• Training job management"}),(0,a.jsx)("li",{children:"• Enhanced system prompts"}),(0,a.jsx)("li",{children:"• Multi-provider compatibility"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Async Processing"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Handle long-running tasks with asynchronous processing and webhook notifications."}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Job submission and tracking"}),(0,a.jsx)("li",{children:"• Webhook notifications"}),(0,a.jsx)("li",{children:"• Progress monitoring"}),(0,a.jsx)("li",{children:"• Result retrieval"})]})]})]})]})]}),"features-intelligent-routing"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Intelligent Routing"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6 leading-relaxed",children:"RouKey's AI-powered routing engine automatically selects the optimal model for each request based on multiple factors."})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Routing Strategies"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.DQ,{className:"h-5 w-5 text-blue-600"}),"Intelligent Role Routing"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"AI-powered classification routes requests based on detected roles (coding, writing, analysis, etc.) to specialized models."}),(0,a.jsx)("div",{className:"text-sm text-blue-600 font-medium",children:"Best for: Multi-purpose applications"})]}),(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.OL,{className:"h-5 w-5 text-green-600"}),"Complexity-Based Routing"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Analyzes prompt complexity (1-5 scale) and routes to appropriate models for optimal cost-performance balance."}),(0,a.jsx)("div",{className:"text-sm text-green-600 font-medium",children:"Best for: Cost optimization"})]}),(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.yq,{className:"h-5 w-5 text-[#ff6b35]"}),"Strict Fallback Strategy"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Ordered failover sequence with predictable routing and guaranteed fallback chain for maximum reliability."}),(0,a.jsx)("div",{className:"text-sm text-[#ff6b35] font-medium",children:"Best for: Mission-critical applications"})]}),(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.BZ,{className:"h-5 w-5 text-purple-600"}),"Cost-Optimized Routing"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Smart cost optimization with learning algorithms that adapt to your usage patterns over time."}),(0,a.jsx)("div",{className:"text-sm text-purple-600 font-medium",children:"Best for: Budget-conscious deployments"})]})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"How Routing Works"}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-white font-bold text-xl",children:"1"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Request Analysis"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Gemini-powered classification analyzes request content, complexity, and context"})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-[#ff6b35] rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-white font-bold text-xl",children:"2"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Model Selection"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Algorithm selects optimal model based on strategy, availability, and cost"})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-white font-bold text-xl",children:"3"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Request Routing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Request is routed to selected model with automatic failover if needed"})]})]})})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Configuration Example"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Here's how to configure intelligent routing step by step:"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Step 1: Create a Custom Configuration"}),(0,a.jsx)(d,{title:"Create custom configuration",language:"bash",children:'POST /api/custom-configs\nContent-Type: application/json\n\n{\n  "name": "My Coding Assistant"\n}'})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Step 2: Add API Keys for Different Models"}),(0,a.jsx)(d,{title:"Add API keys",language:"bash",children:'# Add DeepSeek key for coding\nPOST /api/keys\nContent-Type: application/json\n\n{\n  "custom_api_config_id": "your-config-id",\n  "provider": "deepseek",\n  "predefined_model_id": "deepseek-coder",\n  "api_key_raw": "your-deepseek-api-key",\n  "label": "DeepSeek Coder",\n  "temperature": 0.1\n}\n\n# Add Claude key for writing\nPOST /api/keys\nContent-Type: application/json\n\n{\n  "custom_api_config_id": "your-config-id",\n  "provider": "anthropic",\n  "predefined_model_id": "claude-3-5-sonnet",\n  "api_key_raw": "your-claude-api-key",\n  "label": "Claude Writer",\n  "temperature": 0.7\n}\n\n# Add GPT-4 key for general chat (set as default)\nPOST /api/keys\nContent-Type: application/json\n\n{\n  "custom_api_config_id": "your-config-id",\n  "provider": "openai",\n  "predefined_model_id": "gpt-4",\n  "api_key_raw": "your-openai-api-key",\n  "label": "GPT-4 General",\n  "temperature": 1.0\n}'})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Step 3: Assign Roles to API Keys"}),(0,a.jsx)(d,{title:"Assign roles",language:"bash",children:'# Assign coding role to DeepSeek key\nPOST /api/keys/{deepseek-key-id}/roles\nContent-Type: application/json\n\n{\n  "role_name": "coding_backend"\n}\n\n# Assign writing role to Claude key\nPOST /api/keys/{claude-key-id}/roles\nContent-Type: application/json\n\n{\n  "role_name": "writing"\n}\n\n# Set GPT-4 as default general chat model\nPUT /api/custom-configs/{config-id}/default-key-handler/{gpt4-key-id}'})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Step 4: Enable Intelligent Role Routing"}),(0,a.jsx)(d,{title:"Enable intelligent routing",language:"bash",children:'PUT /api/custom-configs/{config-id}/routing\nContent-Type: application/json\n\n{\n  "routing_strategy": "intelligent_role",\n  "routing_strategy_params": null\n}'})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"How It Works"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("p",{className:"text-gray-700 mb-3",children:"Once configured, RouKey automatically:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-2 text-gray-700",children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Classifies incoming prompts"})," using advanced AI to determine the task type (coding, writing, analysis, etc.)"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Routes to the assigned model"})," for that specific role based on your role assignments"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Falls back to the default general chat model"})," if no specific role is assigned or detected"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Provides seamless switching"})," between different specialized models within the same conversation"]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Available Predefined Roles"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold text-blue-900 mb-2",children:"Development"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("code",{children:"coding_frontend"})," - HTML, CSS, JavaScript, React"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("code",{children:"coding_backend"})," - APIs, databases, server-side"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("code",{children:"data_extraction_structuring"})," - Data processing"]})]})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-3 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"Content & Analysis"}),(0,a.jsxs)("ul",{className:"text-sm text-green-800 space-y-1",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("code",{children:"writing"})," - Articles, blogs, creative content"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("code",{children:"research_synthesis"})," - Research and analysis"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("code",{children:"summarization_briefing"})," - Document summaries"]})]})]}),(0,a.jsxs)("div",{className:"bg-purple-50 p-3 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold text-purple-900 mb-2",children:"Specialized"}),(0,a.jsxs)("ul",{className:"text-sm text-purple-800 space-y-1",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("code",{children:"logic_reasoning"})," - Math, logical puzzles"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("code",{children:"translation_localization"})," - Language translation"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("code",{children:"general_chat"})," - Casual conversation"]})]})]}),(0,a.jsxs)("div",{className:"bg-orange-50 p-3 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold text-orange-900 mb-2",children:"Custom Roles"}),(0,a.jsx)("p",{className:"text-sm text-orange-800",children:"Create your own custom roles for specialized use cases specific to your application."})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Step 5: Use Your Configured Routing"}),(0,a.jsx)(d,{title:"Make requests with intelligent routing",language:"bash",children:'# All requests now automatically use intelligent routing\nPOST /api/v1/chat/completions\nContent-Type: application/json\n\n{\n  "custom_api_config_id": "your-config-id",\n  "messages": [\n    {\n      "role": "user",\n      "content": "Write a Python function to calculate fibonacci numbers"\n    }\n  ]\n}\n\n# RouKey will:\n# 1. Classify this as a coding task\n# 2. Route to your DeepSeek key (assigned to coding_backend role)\n# 3. Return the response from DeepSeek Coder\n\n# For a writing task:\nPOST /api/v1/chat/completions\nContent-Type: application/json\n\n{\n  "custom_api_config_id": "your-config-id",\n  "messages": [\n    {\n      "role": "user",\n      "content": "Write a blog post about artificial intelligence"\n    }\n  ]\n}\n\n# RouKey will:\n# 1. Classify this as a writing task\n# 2. Route to your Claude key (assigned to writing role)\n# 3. Return the response from Claude'})]}),(0,a.jsxs)("div",{className:"bg-amber-50 border border-amber-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-semibold text-amber-900 mb-2",children:"\uD83D\uDCCB Important Notes"}),(0,a.jsxs)("ul",{className:"text-sm text-amber-800 space-y-1",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Subscription Required:"})," Intelligent role routing is available on Starter, Professional, and Enterprise plans"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Role Management:"})," Custom role creation and assignment requires a paid plan"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"API Key Limits:"})," Free tier is limited to 3 API keys maximum"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Automatic Classification:"})," RouKey uses advanced AI to classify prompts - no manual role specification needed"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Fallback Behavior:"})," If no role is assigned for a classified task, requests fall back to your default general chat model"]})]})]})]})]})]}),"features-semantic-caching"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Semantic Caching"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6 leading-relaxed",children:"RouKey's intelligent caching system uses advanced semantic analysis to reduce costs and improve response times."})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"How It Works"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"Unlike traditional caching that requires exact matches, RouKey's semantic caching understands the meaning of requests and can serve cached responses for semantically similar queries."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.OL,{className:"h-5 w-5 text-blue-600"}),"RouKey Embeddings"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Requests are converted to high-dimensional embeddings that capture semantic meaning."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Sub-second embedding generation"}),(0,a.jsx)("li",{children:"• Multi-language support"}),(0,a.jsx)("li",{children:"• High-performance processing"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.BZ,{className:"h-5 w-5 text-green-600"}),"Reranking"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"RouKey reranker optimizes cache hit accuracy by reordering similarity results."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Improved relevance scoring"}),(0,a.jsx)("li",{children:"• Context-aware matching"}),(0,a.jsx)("li",{children:"• Reduced false positives"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Cache Performance"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-green-600 mb-2",children:"85%"}),(0,a.jsx)("div",{className:"text-gray-900 font-medium mb-1",children:"Cache Hit Rate"}),(0,a.jsx)("div",{className:"text-gray-600 text-sm",children:"Average across all request types"})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:"50ms"}),(0,a.jsx)("div",{className:"text-gray-900 font-medium mb-1",children:"Response Time"}),(0,a.jsx)("div",{className:"text-gray-600 text-sm",children:"Average for cached responses"})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-[#ff6b35] mb-2",children:"90%"}),(0,a.jsx)("div",{className:"text-gray-900 font-medium mb-1",children:"Cost Reduction"}),(0,a.jsx)("div",{className:"text-gray-600 text-sm",children:"For cached requests"})]})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Example Scenarios"}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-gray-50 p-6 rounded-xl border border-gray-200",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:"Scenario: Similar Coding Questions"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"p-3 bg-white rounded border-l-4 border-blue-500",children:[(0,a.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Original Request:"}),(0,a.jsx)("div",{className:"text-gray-900",children:'"Write a Python function to sort a list"'})]}),(0,a.jsxs)("div",{className:"p-3 bg-green-50 rounded border-l-4 border-green-500",children:[(0,a.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Cache Hit:"}),(0,a.jsx)("div",{className:"text-gray-900",children:'"Create a Python function for sorting an array"'})]})]}),(0,a.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[(0,a.jsx)("strong",{children:"Result:"})," 95% similarity match, cached response served in 45ms"]})]})})]})]}),"features-multi-role-orchestration"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Multi-Role Orchestration"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6 leading-relaxed",children:"RouKey's advanced orchestration system automatically detects when tasks require multiple specialized AI roles working together, then coordinates their execution using intelligent workflow patterns for optimal results."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Workflow Types"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.fl,{className:"h-5 w-5 text-blue-600"}),"Sequential Workflow"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Single role execution for focused, domain-specific tasks."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• One specialized role handles the task"}),(0,a.jsx)("li",{children:"• Direct, efficient processing"}),(0,a.jsx)("li",{children:"• Fastest execution time"}),(0,a.jsx)("li",{children:"• Best for single-domain requests"})]}),(0,a.jsx)("div",{className:"mt-4 p-3 bg-blue-100 rounded-lg",children:(0,a.jsx)("p",{className:"text-xs text-blue-800 font-medium",children:'Example: "Write a Python function to sort a list"'})})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.K6,{className:"h-5 w-5 text-green-600"}),"Supervisor Workflow"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"2-4 roles with coordinated execution and intelligent supervision."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Multiple roles work sequentially"}),(0,a.jsx)("li",{children:"• Each role builds on previous output"}),(0,a.jsx)("li",{children:"• Intelligent handoff coordination"}),(0,a.jsx)("li",{children:"• Quality validation between steps"})]}),(0,a.jsx)("div",{className:"mt-4 p-3 bg-green-100 rounded-lg",children:(0,a.jsx)("p",{className:"text-xs text-green-800 font-medium",children:'Example: "Research competitors, analyze data, create presentation"'})})]}),(0,a.jsxs)("div",{className:"p-6 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.yQ,{className:"h-5 w-5 text-purple-600"}),"Hierarchical Workflow"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"5+ roles for complex, multi-domain tasks requiring specialized coordination."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Complex task decomposition"}),(0,a.jsx)("li",{children:"• Multi-level role coordination"}),(0,a.jsx)("li",{children:"• Advanced dependency management"}),(0,a.jsx)("li",{children:"• Comprehensive result synthesis"})]}),(0,a.jsx)("div",{className:"mt-4 p-3 bg-purple-100 rounded-lg",children:(0,a.jsx)("p",{className:"text-xs text-purple-800 font-medium",children:'Example: "Build full-stack app with docs, tests, deployment"'})})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Intelligent Detection & Examples"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"RouKey uses advanced AI classification to automatically detect when multi-role orchestration is needed and selects the appropriate workflow type based on task complexity and requirements."}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 p-6 rounded-xl border border-[#ff6b35]/20",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Detection Process"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-[#ff6b35] rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"1"})}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Request Analysis"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Analyze complexity and domain requirements"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-[#ff6b35] rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"2"})}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Role Matching"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Match available roles to task requirements"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-[#ff6b35] rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"3"})}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Workflow Selection"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Choose optimal orchestration pattern"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-[#ff6b35] rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"4"})}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Execution"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Coordinate roles with real-time streaming"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Single-Role Detection"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Tasks that can be handled by one specialized role:"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600 text-sm",children:[(0,a.jsx)("li",{children:'• "Debug this Python code"'}),(0,a.jsx)("li",{children:'• "Write a marketing email"'}),(0,a.jsx)("li",{children:'• "Explain quantum computing"'}),(0,a.jsx)("li",{children:'• "Create a SQL query"'})]}),(0,a.jsx)("div",{className:"mt-4 p-3 bg-blue-100 rounded-lg",children:(0,a.jsxs)("p",{className:"text-xs text-blue-800",children:[(0,a.jsx)("strong",{children:"Result:"})," Sequential workflow with 1 role"]})})]}),(0,a.jsxs)("div",{className:"p-6 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Multi-Role Detection"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Complex tasks requiring multiple specializations:"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600 text-sm",children:[(0,a.jsx)("li",{children:'• "Research market trends, analyze data, create strategy"'}),(0,a.jsx)("li",{children:'• "Build API, write tests, create documentation"'}),(0,a.jsx)("li",{children:'• "Design UI, implement frontend, optimize performance"'}),(0,a.jsx)("li",{children:'• "Analyze competitors, plan features, estimate costs"'})]}),(0,a.jsx)("div",{className:"mt-4 p-3 bg-green-100 rounded-lg",children:(0,a.jsxs)("p",{className:"text-xs text-green-800",children:[(0,a.jsx)("strong",{children:"Result:"})," Supervisor/Hierarchical workflow with 2-5+ roles"]})})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Automatic Streaming & Real-Time Coordination"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"RouKey automatically enables streaming for multi-role tasks, providing real-time progress updates and seamless coordination between AI roles."}),(0,a.jsxs)("div",{className:"bg-blue-50 p-6 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Streaming Features"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"font-medium text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full"}),"Auto-Detection"]}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Automatic stream=true for multi-role"}),(0,a.jsx)("li",{children:"• No manual configuration needed"}),(0,a.jsx)("li",{children:"• Intelligent workflow selection"}),(0,a.jsx)("li",{children:"• Seamless API integration"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"font-medium text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"Real-Time Updates"]}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Live role execution progress"}),(0,a.jsx)("li",{children:"• Server-Sent Events (SSE)"}),(0,a.jsx)("li",{children:"• Role handoff notifications"}),(0,a.jsx)("li",{children:"• Step-by-step visibility"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"font-medium text-gray-900 mb-2 flex items-center gap-2",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-purple-500 rounded-full"}),"Performance"]}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• No 60-second timeout limits"}),(0,a.jsx)("li",{children:"• Prevents Vercel timeouts"}),(0,a.jsx)("li",{children:"• Chunked response delivery"}),(0,a.jsx)("li",{children:"• Optimized for complex tasks"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"API Streaming Behavior"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"External API"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"When multi-role tasks are detected via RouKey's AI classification, streaming is automatically forced:"}),(0,a.jsx)("div",{className:"bg-white p-3 rounded-lg border",children:(0,a.jsx)("code",{className:"text-xs text-gray-800",children:'{\n  "stream": true,\n  "streaming_forced": true,\n  "streaming_reason": "Multi-role orchestration detected"\n}'})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Internal Orchestration"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"Real-time coordination uses Server-Sent Events for live updates:"}),(0,a.jsx)("div",{className:"bg-white p-3 rounded-lg border",children:(0,a.jsx)("code",{className:"text-xs text-gray-800",children:"GET /api/orchestration/stream/{executionId}\nContent-Type: text/event-stream\nCache-Control: no-cache"})})]})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Real-World Examples"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"See how RouKey's multi-role orchestration handles complex tasks by coordinating specialized AI roles."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)("span",{className:"w-3 h-3 bg-blue-500 rounded-full"}),"Software Development Project"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-blue-100",children:[(0,a.jsx)("p",{className:"text-sm text-gray-700 font-medium mb-2",children:"User Request:"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 italic",children:'"Build a REST API for user management with authentication, write comprehensive tests, and create API documentation"'})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"w-6 h-6 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center font-bold",children:"1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Backend Developer Role"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Creates API endpoints, authentication logic, database models"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"w-6 h-6 bg-green-500 text-white text-xs rounded-full flex items-center justify-center font-bold",children:"2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"QA Engineer Role"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Writes unit tests, integration tests, validates functionality"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"w-6 h-6 bg-purple-500 text-white text-xs rounded-full flex items-center justify-center font-bold",children:"3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Technical Writer Role"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Creates API documentation, usage examples, deployment guide"})]})]})]}),(0,a.jsx)("div",{className:"bg-green-100 p-3 rounded-lg border border-green-200",children:(0,a.jsxs)("p",{className:"text-xs text-green-800",children:[(0,a.jsx)("strong",{children:"Workflow:"})," Supervisor (3 roles) with automatic streaming"]})})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)("span",{className:"w-3 h-3 bg-green-500 rounded-full"}),"Market Research & Strategy"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-green-100",children:[(0,a.jsx)("p",{className:"text-sm text-gray-700 font-medium mb-2",children:"User Request:"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 italic",children:'"Research the AI tools market, analyze competitor pricing, and create a go-to-market strategy for our new product"'})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"w-6 h-6 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center font-bold",children:"1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Research Analyst Role"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Gathers market data, identifies key players, trends analysis"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"w-6 h-6 bg-green-500 text-white text-xs rounded-full flex items-center justify-center font-bold",children:"2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Data Analyst Role"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Analyzes pricing models, market size, competitive positioning"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"w-6 h-6 bg-purple-500 text-white text-xs rounded-full flex items-center justify-center font-bold",children:"3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Strategy Consultant Role"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Creates go-to-market plan, pricing strategy, launch timeline"})]})]})]}),(0,a.jsx)("div",{className:"bg-green-100 p-3 rounded-lg border border-green-200",children:(0,a.jsxs)("p",{className:"text-xs text-green-800",children:[(0,a.jsx)("strong",{children:"Workflow:"})," Supervisor (3 roles) with real-time coordination"]})})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl border border-purple-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,a.jsx)("span",{className:"w-3 h-3 bg-purple-500 rounded-full"}),"Complex Enterprise Application (Hierarchical)"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-purple-100 mb-4",children:[(0,a.jsx)("p",{className:"text-sm text-gray-700 font-medium mb-2",children:"User Request:"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 italic",children:'"Build a complete e-commerce platform with frontend, backend, payment integration, admin dashboard, and mobile app"'})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)("span",{className:"w-5 h-5 bg-blue-500 text-white text-xs rounded flex items-center justify-center",children:"1"}),(0,a.jsx)("span",{className:"text-gray-900",children:"System Architect"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)("span",{className:"w-5 h-5 bg-green-500 text-white text-xs rounded flex items-center justify-center",children:"2"}),(0,a.jsx)("span",{className:"text-gray-900",children:"Backend Developer"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)("span",{className:"w-5 h-5 bg-purple-500 text-white text-xs rounded flex items-center justify-center",children:"3"}),(0,a.jsx)("span",{className:"text-gray-900",children:"Frontend Developer"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)("span",{className:"w-5 h-5 bg-orange-500 text-white text-xs rounded flex items-center justify-center",children:"4"}),(0,a.jsx)("span",{className:"text-gray-900",children:"Mobile Developer"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)("span",{className:"w-5 h-5 bg-red-500 text-white text-xs rounded flex items-center justify-center",children:"5"}),(0,a.jsx)("span",{className:"text-gray-900",children:"DevOps Engineer"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:"Hierarchical Coordination"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm text-gray-600",children:[(0,a.jsx)("li",{children:"• Multi-level task decomposition"}),(0,a.jsx)("li",{children:"• Parallel development streams"}),(0,a.jsx)("li",{children:"• Cross-role dependency management"}),(0,a.jsx)("li",{children:"• Integrated testing & deployment"}),(0,a.jsx)("li",{children:"• Real-time progress synchronization"})]}),(0,a.jsx)("div",{className:"bg-purple-100 p-3 rounded-lg border border-purple-200 mt-4",children:(0,a.jsxs)("p",{className:"text-xs text-purple-800",children:[(0,a.jsx)("strong",{children:"Workflow:"})," Hierarchical (5+ roles) with advanced streaming"]})})]})]})]})]})]}),(0,a.jsxs)(c,{type:"tip",children:[(0,a.jsx)("strong",{children:"Automatic Streaming:"})," RouKey automatically detects multi-role tasks and enables streaming to provide real-time progress updates and prevent timeout issues. No manual configuration required."]})]}),"features-knowledge-base"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Knowledge Base"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Enhance your AI responses with custom knowledge by uploading documents that provide domain-specific context and expertise."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Document Upload & Processing"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"Upload documents in various formats to create a custom knowledge base that enhances AI responses with your proprietary information."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.py,{className:"h-5 w-5 text-blue-600"}),"Supported Formats"]}),(0,a.jsxs)("ul",{className:"text-gray-600 space-y-2",children:[(0,a.jsx)("li",{children:"• PDF documents"}),(0,a.jsx)("li",{children:"• Word documents (.docx)"}),(0,a.jsx)("li",{children:"• Text files (.txt)"}),(0,a.jsx)("li",{children:"• Markdown files (.md)"}),(0,a.jsx)("li",{children:"• CSV files"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.DP,{className:"h-5 w-5 text-green-600"}),"Processing Features"]}),(0,a.jsxs)("ul",{className:"text-gray-600 space-y-2",children:[(0,a.jsx)("li",{children:"• Automatic text extraction"}),(0,a.jsx)("li",{children:"• Intelligent chunking"}),(0,a.jsx)("li",{children:"• Semantic indexing"}),(0,a.jsx)("li",{children:"• Context preservation"}),(0,a.jsx)("li",{children:"• Metadata extraction"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Tier Limits & Usage"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full bg-white border border-gray-200 rounded-xl shadow-sm",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Tier"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Max Documents"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"File Size Limit"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Features"})]})}),(0,a.jsxs)("tbody",{className:"divide-y divide-gray-200",children:[(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:"Free"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Not available"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"-"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Upgrade required"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:"Starter"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Not available"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"-"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Upgrade required"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:"Professional"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"5 documents"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"10MB per file"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Full knowledge base access"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:"Enterprise"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"15 documents"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"25MB per file"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Advanced features, priority processing"})]})]})]})})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"How It Works"}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Intelligent Context Retrieval"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"When you ask a question, RouKey automatically searches your knowledge base for relevant information and includes it in the AI's context."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"1"})}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Query Analysis"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Understand user intent"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"2"})}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Document Search"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Find relevant content"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"3"})}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Context Injection"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Add to AI prompt"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"4"})}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Enhanced Response"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"AI responds with context"})]})]})]})})]}),(0,a.jsxs)(c,{type:"tip",children:[(0,a.jsx)("strong",{children:"Best Practice:"})," Upload well-structured documents with clear headings and sections for optimal knowledge extraction and retrieval accuracy."]})]}),"features-custom-training"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Custom Training"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Train AI models with your specific data, instructions, and behavioral patterns to create domain-specific assistants tailored to your needs."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Training Methods"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.AQ,{className:"h-5 w-5 text-blue-600"}),"Prompt Engineering"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Define AI behavior through custom system prompts, instructions, and example interactions."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• System instructions"}),(0,a.jsx)("li",{children:"• Behavioral guidelines"}),(0,a.jsx)("li",{children:"• Example conversations"}),(0,a.jsx)("li",{children:"• Response formatting"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.tl,{className:"h-5 w-5 text-green-600"}),"Fine-tuning (Coming Soon)"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Advanced model customization with your specific datasets for specialized performance."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Custom model weights"}),(0,a.jsx)("li",{children:"• Domain specialization"}),(0,a.jsx)("li",{children:"• Performance optimization"}),(0,a.jsx)("li",{children:"• Proprietary knowledge"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Training Format Guide"}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-gray-50 p-6 rounded-xl border border-gray-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Supported Formats"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"System Instructions"}),(0,a.jsxs)("div",{className:"bg-white p-3 rounded border font-mono text-sm",children:["SYSTEM: You are a helpful customer service agent for our company",(0,a.jsx)("br",{}),"BEHAVIOR: Always be polite and offer solutions"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Example Interactions"}),(0,a.jsxs)("div",{className:"bg-white p-3 rounded border font-mono text-sm",children:["User asks about returns → I'd be happy to help with your return!",(0,a.jsx)("br",{}),"Customer is frustrated → I understand your frustration. Let me help."]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"General Instructions"}),(0,a.jsxs)("div",{className:"bg-white p-3 rounded border font-mono text-sm",children:["Always provide step-by-step explanations for technical topics.",(0,a.jsx)("br",{}),"Include relevant examples when explaining concepts."]})]})]})]})})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Tier Availability"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full bg-white border border-gray-200 rounded-xl shadow-sm",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Tier"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Prompt Engineering"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Fine-tuning"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Custom Models"})]})}),(0,a.jsxs)("tbody",{className:"divide-y divide-gray-200",children:[(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:"Free"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Not available"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Not available"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Not available"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:"Starter"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-green-600",children:"✓ Available"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Coming soon"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Coming soon"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:"Professional"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-green-600",children:"✓ Available"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Coming soon"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Coming soon"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:"Enterprise"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-green-600",children:"✓ Available"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Coming soon"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Coming soon"})]})]})]})})]}),(0,a.jsxs)(c,{type:"info",children:[(0,a.jsx)("strong",{children:"Getting Started:"})," Custom training through prompt engineering is available for Starter tier and above. Visit the Training page in your dashboard to create custom prompts and behavioral instructions."]})]}),"features-async-processing"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Async Processing"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Handle long-running AI tasks with webhook notifications, perfect for complex multi-role workflows and time-intensive operations."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"How It Works"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"Submit tasks for background processing and receive results via webhooks when complete, eliminating timeout constraints."}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-teal-50 to-teal-100 p-6 rounded-xl border border-teal-200",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Processing Flow"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"1"})}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Submit Job"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Send request with webhook URL"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"2"})}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Queue Processing"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Task added to processing queue"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"3"})}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Background Execution"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"AI processes without timeout"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"4"})}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Webhook Notification"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Results sent to your endpoint"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"API Usage"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Submit Async Job"}),(0,a.jsx)(d,{title:"Submit async processing job",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/async/submit" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [\n      {"role": "user", "content": "Analyze this complex dataset and provide insights"}\n    ],\n    "webhook_url": "https://your-app.com/webhook/roukey",\n    "metadata": {\n      "task_id": "analysis_001",\n      "priority": "high"\n    }\n  }\''}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Check Job Status"}),(0,a.jsx)(d,{title:"Check async job status",language:"bash",children:'curl -X GET "https://roukey.online/api/external/v1/async/status/job_id_here" \\\n  -H "X-API-Key: rk_live_your_api_key_here"'})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Webhook Integration"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"Receive job completion notifications at your specified webhook endpoint with full results and metadata."}),(0,a.jsxs)("div",{className:"bg-gray-50 p-6 rounded-xl border border-gray-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Webhook Payload Example"}),(0,a.jsx)(d,{title:"Webhook notification payload",language:"json",children:'{\n  "job_id": "async_job_123456",\n  "status": "completed",\n  "result": {\n    "response": "Analysis complete: The dataset shows...",\n    "roles_used": ["analyst", "researcher"],\n    "processing_time": 45.2,\n    "tokens_used": 2847\n  },\n  "metadata": {\n    "task_id": "analysis_001",\n    "priority": "high"\n  },\n  "completed_at": "2024-06-24T10:30:00Z"\n}'})]})]})]}),(0,a.jsxs)(c,{type:"tip",children:[(0,a.jsx)("strong",{children:"Best Use Cases:"})," Async processing is ideal for complex multi-role tasks, large document analysis, research workflows, and any operation that might exceed standard timeout limits."]})]}),"features-analytics-monitoring"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Analytics & Monitoring"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Comprehensive real-time insights into your AI usage, performance metrics, and cost optimization opportunities."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Performance Metrics"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.O4,{className:"h-5 w-5 text-blue-600"}),"Response Times"]}),(0,a.jsxs)("ul",{className:"text-gray-600 space-y-2",children:[(0,a.jsx)("li",{children:"• First token latency tracking"}),(0,a.jsx)("li",{children:"• Complete response timing"}),(0,a.jsx)("li",{children:"• Provider comparison"}),(0,a.jsx)("li",{children:"• Performance trends"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.xm,{className:"h-5 w-5 text-green-600"}),"Cost Analytics"]}),(0,a.jsxs)("ul",{className:"text-gray-600 space-y-2",children:[(0,a.jsx)("li",{children:"• Token usage tracking"}),(0,a.jsx)("li",{children:"• Cost per request"}),(0,a.jsx)("li",{children:"• Provider cost comparison"}),(0,a.jsx)("li",{children:"• Budget monitoring"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.r9,{className:"h-5 w-5 text-purple-600"}),"Usage Statistics"]}),(0,a.jsxs)("ul",{className:"text-gray-600 space-y-2",children:[(0,a.jsx)("li",{children:"• Request volume trends"}),(0,a.jsx)("li",{children:"• Model usage distribution"}),(0,a.jsx)("li",{children:"• Cache hit rates"}),(0,a.jsx)("li",{children:"• Error rate monitoring"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Real-time Monitoring"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"Monitor your AI infrastructure in real-time with comprehensive dashboards and alerting capabilities."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.Pi,{className:"h-5 w-5 text-yellow-600"}),"Health Monitoring"]}),(0,a.jsxs)("ul",{className:"text-gray-600 space-y-2",children:[(0,a.jsx)("li",{children:"• API endpoint health checks"}),(0,a.jsx)("li",{children:"• Provider availability status"}),(0,a.jsx)("li",{children:"• Automatic failover tracking"}),(0,a.jsx)("li",{children:"• System uptime monitoring"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-red-50 rounded-xl border border-red-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.XF,{className:"h-5 w-5 text-red-600"}),"Alert System"]}),(0,a.jsxs)("ul",{className:"text-gray-600 space-y-2",children:[(0,a.jsx)("li",{children:"• High error rate alerts"}),(0,a.jsx)("li",{children:"• Cost threshold notifications"}),(0,a.jsx)("li",{children:"• Performance degradation warnings"}),(0,a.jsx)("li",{children:"• Custom alert rules"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Performance Targets"}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"First Token Performance"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-white rounded-lg border border-green-200",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-1",children:"⚡"}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Excellent"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"< 500ms"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-white rounded-lg border border-blue-200",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:"✅"}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Good"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"500-1000ms"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-white rounded-lg border border-yellow-200",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600 mb-1",children:"⚠️"}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Slow"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"1000-2000ms"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-white rounded-lg border border-red-200",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600 mb-1",children:"\uD83D\uDC0C"}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Very Slow"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"> 2000ms"})]})]})]})})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Tier Features"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full bg-white border border-gray-200 rounded-xl shadow-sm",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Tier"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Analytics"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Monitoring"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Alerts"})]})}),(0,a.jsxs)("tbody",{className:"divide-y divide-gray-200",children:[(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:"Free"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Basic usage stats"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Basic monitoring"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Not available"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:"Starter"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-green-600",children:"Enhanced analytics"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-green-600",children:"Real-time monitoring"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Basic alerts"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:"Professional"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-green-600",children:"Advanced analytics"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-green-600",children:"Comprehensive monitoring"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-green-600",children:"Custom alerts"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:"Enterprise"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-green-600",children:"Full analytics suite"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-green-600",children:"Enterprise monitoring"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-green-600",children:"Advanced alerting"})]})]})]})})]}),(0,a.jsxs)(c,{type:"info",children:[(0,a.jsx)("strong",{children:"Performance Optimization:"})," Use the analytics dashboard to identify bottlenecks, optimize routing strategies, and reduce costs while maintaining response quality."]})]}),"authentication"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Authentication"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Learn how to authenticate with RouKey using API keys."})]}),(0,a.jsxs)(c,{type:"info",children:[(0,a.jsx)("strong",{children:"Important:"})," RouKey uses the ",(0,a.jsx)("code",{className:"bg-blue-100 px-2 py-1 rounded text-blue-800",children:"X-API-Key"})," header for authentication. Never use ",(0,a.jsx)("code",{className:"bg-blue-100 px-2 py-1 rounded text-blue-800",children:"Authorization"})," header format."]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Getting Your API Key"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"To get started with RouKey, you'll need to create an API key from your dashboard:"}),(0,a.jsxs)("ol",{className:"list-decimal list-inside space-y-3 text-gray-600 ml-4 text-lg",children:[(0,a.jsxs)("li",{children:["Sign up for a RouKey account at ",(0,a.jsx)("a",{href:"https://roukey.online",className:"text-[#ff6b35] hover:text-[#e55a2b] underline",children:"roukey.online"})]}),(0,a.jsx)("li",{children:"Navigate to your dashboard and create a configuration"}),(0,a.jsx)("li",{children:"Add your LLM provider API keys (OpenAI, Anthropic, etc.)"}),(0,a.jsx)("li",{children:"Generate a user API key for external access"}),(0,a.jsxs)("li",{children:["Copy your API key (format: ",(0,a.jsx)("code",{className:"bg-gray-100 px-2 py-1 rounded text-gray-700",children:"rk_live_..."}),")"]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Authentication Methods"}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Method 1: X-API-Key Header (Recommended)"}),(0,a.jsx)(d,{title:"Using X-API-Key header",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [{"role": "user", "content": "Hello!"}],\n    "stream": false\n  }\''})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Method 2: Bearer Token"}),(0,a.jsx)(d,{title:"Using Authorization Bearer header",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "Authorization: Bearer rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [{"role": "user", "content": "Hello!"}],\n    "stream": false\n  }\''})]})]})]}),(0,a.jsxs)(c,{type:"tip",children:[(0,a.jsx)("strong",{children:"Best Practice:"})," Always use the ",(0,a.jsx)("code",{className:"bg-green-100 px-2 py-1 rounded text-green-800",children:"X-API-Key"})," header method as it's the primary authentication method for RouKey and ensures maximum compatibility."]})]}),"authentication-api-keys"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"API Keys"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Learn how to create, manage, and secure your RouKey API keys for optimal performance and security."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Creating API Keys"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"RouKey API keys are generated from your dashboard and provide secure access to the intelligent routing system."}),(0,a.jsxs)("div",{className:"bg-blue-50 p-6 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Key Format"}),(0,a.jsx)("div",{className:"font-mono text-sm bg-white p-3 rounded border",children:"rk_live_1234567890abcdef..."}),(0,a.jsxs)("p",{className:"text-gray-600 text-sm mt-2",children:["All RouKey API keys start with ",(0,a.jsx)("code",{className:"bg-white px-1 rounded",children:"rk_live_"})," followed by a secure random string."]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Key Management"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.RY,{className:"h-5 w-5 text-green-600"}),"Best Practices"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Store keys in environment variables"}),(0,a.jsx)("li",{children:"• Never commit keys to version control"}),(0,a.jsx)("li",{children:"• Rotate keys regularly"}),(0,a.jsx)("li",{children:"• Use different keys for different environments"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-red-50 rounded-xl border border-red-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.Pi,{className:"h-5 w-5 text-red-600"}),"Security Warnings"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Never expose keys in client-side code"}),(0,a.jsx)("li",{children:"• Don't share keys in public channels"}),(0,a.jsx)("li",{children:"• Revoke compromised keys immediately"}),(0,a.jsx)("li",{children:"• Monitor key usage for anomalies"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Tier Limits"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full bg-white border border-gray-200 rounded-xl shadow-sm",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Tier"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Max API Keys"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Features"})]})}),(0,a.jsxs)("tbody",{className:"divide-y divide-gray-200",children:[(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:"Free"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"3"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Basic routing, 1 config"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:"Starter"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"50"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Advanced routing, custom roles"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:"Professional"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Unlimited"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"All features, knowledge base"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:"Enterprise"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Unlimited"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Enterprise features, priority support"})]})]})]})})]})]}),"authentication-security"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Security"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"RouKey implements enterprise-grade security measures to protect your API keys and data."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Encryption & Storage"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.Zu,{className:"h-5 w-5 text-blue-600"}),"AES-256 Encryption"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"All API keys are encrypted using AES-256 encryption before storage in our secure database."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Keys encrypted at rest"}),(0,a.jsx)("li",{children:"• Secure key derivation"}),(0,a.jsx)("li",{children:"• Regular key rotation"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.hp,{className:"h-5 w-5 text-green-600"}),"Transit Security"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"All communications use TLS 1.3 encryption to protect data in transit."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• TLS 1.3 encryption"}),(0,a.jsx)("li",{children:"• Certificate pinning"}),(0,a.jsx)("li",{children:"• HSTS headers"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Data Privacy"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-green-50 p-6 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"What We DON'T Store"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Your actual API responses from providers"}),(0,a.jsx)("li",{children:"• Sensitive content from your requests"}),(0,a.jsx)("li",{children:"• Personal data beyond what's necessary"}),(0,a.jsx)("li",{children:"• Provider API keys in plain text"})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 p-6 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"What We Store"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Encrypted provider API keys"}),(0,a.jsx)("li",{children:"• Request metadata for routing decisions"}),(0,a.jsx)("li",{children:"• Usage analytics (anonymized)"}),(0,a.jsx)("li",{children:"• Configuration settings"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Compliance"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(l.Zu,{className:"h-6 w-6 text-white"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"SOC 2 Ready"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Security controls aligned with SOC 2 Type II requirements"})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(l.AQ,{className:"h-6 w-6 text-white"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"GDPR Compliant"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Full compliance with European data protection regulations"})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(l.RY,{className:"h-6 w-6 text-white"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"BYOK Model"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"You maintain control of your API keys and data"})]})]})]})]}),"authentication-auth-methods"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Authentication Methods"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"RouKey supports multiple authentication methods to integrate seamlessly with your existing infrastructure and security requirements."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Supported Methods"}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"border-l-4 border-[#ff6b35] pl-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,a.jsx)(l.RY,{className:"h-5 w-5 text-[#ff6b35]"}),"X-API-Key Header (Recommended)"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"The primary and recommended authentication method for RouKey. This method provides the best compatibility and performance."}),(0,a.jsx)(d,{title:"X-API-Key authentication",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [{"role": "user", "content": "Hello!"}]\n  }\''}),(0,a.jsxs)("div",{className:"mt-4 bg-green-50 p-4 rounded-lg border border-green-200",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Benefits:"}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Primary authentication method"}),(0,a.jsx)("li",{children:"• Maximum compatibility"}),(0,a.jsx)("li",{children:"• Optimal performance"}),(0,a.jsx)("li",{children:"• Clear separation from OAuth tokens"})]})]})]}),(0,a.jsxs)("div",{className:"border-l-4 border-blue-500 pl-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,a.jsx)(l.Zu,{className:"h-5 w-5 text-blue-600"}),"Authorization Bearer Token"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Alternative authentication method using the standard Authorization header with Bearer token format."}),(0,a.jsx)(d,{title:"Bearer token authentication",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "Authorization: Bearer rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [{"role": "user", "content": "Hello!"}]\n  }\''}),(0,a.jsxs)("div",{className:"mt-4 bg-blue-50 p-4 rounded-lg border border-blue-200",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Use Cases:"}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Legacy system integration"}),(0,a.jsx)("li",{children:"• Standard OAuth workflows"}),(0,a.jsx)("li",{children:"• Third-party tool compatibility"}),(0,a.jsx)("li",{children:"• Enterprise security requirements"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"SDK Integration"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"RouKey is compatible with popular AI SDKs. Here's how to configure authentication for different platforms:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"OpenAI SDK (Python)"}),(0,a.jsx)(d,{title:"OpenAI Python SDK configuration",language:"python",children:'from openai import OpenAI\n\nclient = OpenAI(\n    api_key="rk_live_your_api_key_here",\n    base_url="https://roukey.online/api/external/v1"\n)\n\nresponse = client.chat.completions.create(\n    messages=[{"role": "user", "content": "Hello!"}],\n    model="gpt-4"  # This will be routed by RouKey\n)'})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"OpenAI SDK (JavaScript)"}),(0,a.jsx)(d,{title:"OpenAI JavaScript SDK configuration",language:"javascript",children:"import OpenAI from 'openai';\n\nconst openai = new OpenAI({\n  apiKey: 'rk_live_your_api_key_here',\n  baseURL: 'https://roukey.online/api/external/v1'\n});\n\nconst response = await openai.chat.completions.create({\n  messages: [{ role: 'user', content: 'Hello!' }],\n  model: 'gpt-4' // This will be routed by RouKey\n});"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Security Best Practices"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.Sr,{className:"h-5 w-5 text-green-600"}),"Recommended Practices"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Use environment variables for API keys"}),(0,a.jsx)("li",{children:"• Implement key rotation policies"}),(0,a.jsx)("li",{children:"• Monitor API key usage patterns"}),(0,a.jsx)("li",{children:"• Use different keys for different environments"}),(0,a.jsx)("li",{children:"• Implement proper error handling"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-red-50 rounded-xl border border-red-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.Pi,{className:"h-5 w-5 text-red-600"}),"Security Warnings"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Never expose keys in client-side code"}),(0,a.jsx)("li",{children:"• Don't commit keys to version control"}),(0,a.jsx)("li",{children:"• Avoid logging API keys"}),(0,a.jsx)("li",{children:"• Don't share keys in public channels"}),(0,a.jsx)("li",{children:"• Revoke compromised keys immediately"})]})]})]})]}),(0,a.jsxs)(c,{type:"tip",children:[(0,a.jsx)("strong",{children:"Recommendation:"})," Use the X-API-Key header method for new integrations as it provides the best performance and compatibility with RouKey's routing system."]})]}),"authentication-rate-limiting"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Rate Limiting"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"RouKey provides unlimited API requests across all tiers, focusing on feature-based restrictions rather than rate limiting."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"No Rate Limits Policy"}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(l.Sr,{className:"h-6 w-6 text-green-600"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Unlimited API Requests"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"RouKey does not impose rate limits on any subscription tier. You can make as many API requests as needed without worrying about request quotas or throttling."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-white rounded-lg border border-green-200",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-1",children:"∞"}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Free Tier"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Unlimited requests"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-white rounded-lg border border-green-200",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-1",children:"∞"}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Paid Tiers"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Unlimited requests"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-white rounded-lg border border-green-200",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-1",children:"∞"}),(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Enterprise"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Unlimited requests"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"What We Limit Instead"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"Instead of rate limiting, RouKey uses feature-based restrictions to differentiate subscription tiers:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.DP,{className:"h-5 w-5 text-blue-600"}),"Configuration Limits"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Free: 1 configuration"}),(0,a.jsx)("li",{children:"• Starter: 4 configurations"}),(0,a.jsx)("li",{children:"• Professional: 20 configurations"}),(0,a.jsx)("li",{children:"• Enterprise: Unlimited"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.RY,{className:"h-5 w-5 text-purple-600"}),"API Keys Per Config"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Free: 3 keys per config"}),(0,a.jsx)("li",{children:"• Starter: 5 keys per config"}),(0,a.jsx)("li",{children:"• Professional: 15 keys per config"}),(0,a.jsx)("li",{children:"• Enterprise: Unlimited"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.BZ,{className:"h-5 w-5 text-green-600"}),"User-Generated API Keys"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Free: 3 total keys"}),(0,a.jsx)("li",{children:"• Starter: 50 total keys"}),(0,a.jsx)("li",{children:"• Professional: Unlimited"}),(0,a.jsx)("li",{children:"• Enterprise: Unlimited"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.K6,{className:"h-5 w-5 text-yellow-600"}),"Custom Roles & Features"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Free: No custom roles"}),(0,a.jsx)("li",{children:"• Starter: 3 custom roles"}),(0,a.jsx)("li",{children:"• Professional: Unlimited roles"}),(0,a.jsx)("li",{children:"• Enterprise: Unlimited roles"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-orange-50 rounded-xl border border-orange-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.py,{className:"h-5 w-5 text-orange-600"}),"Knowledge Base Documents"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Free: Not available"}),(0,a.jsx)("li",{children:"• Starter: Not available"}),(0,a.jsx)("li",{children:"• Professional: 5 documents"}),(0,a.jsx)("li",{children:"• Enterprise: 15 documents"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-teal-50 rounded-xl border border-teal-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.OL,{className:"h-5 w-5 text-teal-600"}),"Feature Access"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Advanced routing strategies"}),(0,a.jsx)("li",{children:"• Prompt engineering"}),(0,a.jsx)("li",{children:"• Semantic caching"}),(0,a.jsx)("li",{children:"• Multi-role orchestration"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Provider Rate Limits"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"While RouKey doesn't impose rate limits, your underlying AI providers (OpenAI, Anthropic, etc.) may have their own rate limits:"}),(0,a.jsxs)("div",{className:"bg-yellow-50 p-6 rounded-xl border border-yellow-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.Pi,{className:"h-5 w-5 text-yellow-600"}),"Provider Limitations"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"OpenAI:"})," Rate limits based on your OpenAI tier"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Anthropic:"})," Rate limits based on your Claude usage tier"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Google:"})," Free tier limited to ~60 requests/minute"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Other providers:"})," Each has their own rate limiting policies"]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 p-6 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"How RouKey Handles Provider Limits"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Automatic failover to alternative providers when limits are hit"}),(0,a.jsx)("li",{children:"• Intelligent routing to avoid known rate-limited providers"}),(0,a.jsx)("li",{children:"• Detection of provider free tiers with lower limits"}),(0,a.jsx)("li",{children:"• Load balancing across multiple API keys for the same provider"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Best Practices"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.Sr,{className:"h-5 w-5 text-green-600"}),"Optimization Tips"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Use semantic caching to reduce duplicate requests"}),(0,a.jsx)("li",{children:"• Configure multiple API keys per provider for load balancing"}),(0,a.jsx)("li",{children:"• Enable streaming for long-running tasks"}),(0,a.jsx)("li",{children:"• Use intelligent routing to optimize costs"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.Zu,{className:"h-5 w-5 text-blue-600"}),"Monitoring"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Monitor your provider API usage and costs"}),(0,a.jsx)("li",{children:"• Set up alerts for unusual usage patterns"}),(0,a.jsx)("li",{children:"• Use RouKey's analytics to track performance"}),(0,a.jsx)("li",{children:"• Implement proper error handling for provider limits"})]})]})]})]}),(0,a.jsxs)(c,{type:"info",children:[(0,a.jsx)("strong",{children:"No Limits:"})," RouKey focuses on providing unlimited access to AI models while optimizing costs and performance through intelligent routing rather than restricting usage."]})]}),"api-reference"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"API Reference"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Complete reference for the RouKey API endpoints and parameters."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Base URL"}),(0,a.jsx)(d,{title:"Production Base URL",children:"https://roukey.online/api/external/v1"})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Chat Completions"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6 text-lg",children:"Create a chat completion using RouKey's intelligent routing. This endpoint is fully compatible with OpenAI's API."}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl mb-6 border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsx)("span",{className:"bg-green-600 text-white px-3 py-1 rounded-lg text-sm font-medium",children:"POST"}),(0,a.jsx)("code",{className:"text-gray-900 text-lg font-mono",children:"/chat/completions"})]}),(0,a.jsx)("p",{className:"text-gray-600",children:"OpenAI-compatible endpoint with RouKey's intelligent routing capabilities"})]}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Request Parameters"}),(0,a.jsx)("div",{className:"overflow-x-auto mb-8",children:(0,a.jsxs)("table",{className:"min-w-full bg-white border border-gray-200 rounded-xl shadow-sm",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Parameter"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Type"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Required"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Description"})]})}),(0,a.jsxs)("tbody",{className:"divide-y divide-gray-200",children:[(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"messages"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"array"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-green-600",children:"Yes"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Array of message objects"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"stream"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"boolean"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:"No"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Enable streaming responses (recommended for multi-role tasks)"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"temperature"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"number"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:"No"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Sampling temperature (0-2)"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"max_tokens"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"integer"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:"No"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Maximum tokens to generate"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"role"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"string"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:"No"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:'RouKey-specific role for routing (e.g., "coding", "writing")'})]})]})]})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Example Request"}),(0,a.jsx)(d,{title:"Basic chat completion",language:"json",children:'{\n  "messages": [\n    {"role": "user", "content": "Explain quantum computing"}\n  ],\n  "stream": false,\n  "temperature": 0.7,\n  "max_tokens": 500\n}'}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4 mt-8",children:"Example with Role-Based Routing"}),(0,a.jsx)(d,{title:"Role-based routing request",language:"json",children:'{\n  "messages": [\n    {"role": "user", "content": "Write a Python function to sort a list"}\n  ],\n  "role": "coding",\n  "stream": true,\n  "max_tokens": 1000\n}'}),(0,a.jsxs)(c,{type:"tip",children:[(0,a.jsx)("strong",{children:"Streaming Recommended:"})," For complex tasks that may involve multiple roles or require significant processing, use ",(0,a.jsx)("code",{className:"bg-green-100 px-2 py-1 rounded text-green-800",children:"stream: true"})," to avoid timeouts and get real-time responses."]})]})]}),"use-cases-development-coding"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Development & Coding"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Optimize your development workflow with RouKey's intelligent routing for coding tasks."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Recommended Setup"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Primary: Claude 4 Opus"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Best coding model with excellent performance for programming tasks"}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Superior code generation and completion"}),(0,a.jsx)("li",{children:"• Multi-language support (Python, JavaScript, Go, Rust, etc.)"}),(0,a.jsx)("li",{children:"• Cost-effective for high-volume coding tasks"}),(0,a.jsx)("li",{children:"• Excellent at debugging and code explanation"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Fallback: GPT-4 / Deepseek R1 0528"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Premium models for complex architectural decisions and code reviews"}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Advanced reasoning for complex problems"}),(0,a.jsx)("li",{children:"• Excellent code review and optimization suggestions"}),(0,a.jsx)("li",{children:"• Strong performance on system design questions"}),(0,a.jsx)("li",{children:"• Better at explaining complex concepts"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Common Use Cases"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-xl",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Code Generation"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Generate functions, classes, and complete modules from natural language descriptions."})]}),(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-xl",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Code Review"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Automated code review with suggestions for improvements and bug detection."})]}),(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-xl",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Documentation"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Generate comprehensive documentation, comments, and API references."})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-xl",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Debugging"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Identify bugs, suggest fixes, and explain error messages in detail."})]}),(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-xl",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Code Translation"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Convert code between different programming languages and frameworks."})]}),(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-xl",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Learning & Tutorials"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Interactive coding tutorials and explanations for learning new technologies."})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Setup Guide"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Step 1: Enable Intelligent Role Routing"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:'In your RouKey app, navigate to your Custom Model configuration and set the routing strategy to "Intelligent Role Routing".'}),(0,a.jsx)("div",{className:"bg-white p-3 rounded border text-sm text-gray-700 font-mono",children:"Routing Strategy: Intelligent Role Routing"})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Step 2: Add Your API Keys"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Add API keys for different providers optimized for coding tasks:"}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1 mb-3",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Deepseek Coder:"})," Primary coding model"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Claude 3.5 Sonnet:"})," Code review and analysis"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"GPT-4:"})," Documentation and explanations"]})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-orange-50 rounded-xl border border-orange-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Step 3: Assign Roles to API Keys"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:'In the "My Models" section, assign specific roles to each API key:'}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"bg-white p-2 rounded border",children:[(0,a.jsx)("span",{className:"font-medium",children:"Deepseek Coder API Key"})," → ",(0,a.jsx)("span",{className:"text-blue-600",children:"coding"})," role"]}),(0,a.jsxs)("div",{className:"bg-white p-2 rounded border",children:[(0,a.jsx)("span",{className:"font-medium",children:"Claude 3.5 Sonnet API Key"})," → ",(0,a.jsx)("span",{className:"text-green-600",children:"code_review"})," role"]}),(0,a.jsxs)("div",{className:"bg-white p-2 rounded border",children:[(0,a.jsx)("span",{className:"font-medium",children:"GPT-4 API Key"})," → ",(0,a.jsx)("span",{className:"text-purple-600",children:"documentation"})," role"]})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"How It Works"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"When you send a request, RouKey's AI automatically classifies your prompt and routes it to the appropriate API key based on the assigned roles. No complex configuration needed - just assign roles in the app interface."})]})]})]})]}),"use-cases-content-creation"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Content Creation"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Enhance your content creation workflow with RouKey's intelligent routing for writing tasks."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Recommended Setup"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Primary: Claude 4 Opus"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Exceptional writing quality with natural, engaging tone"}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Superior creative writing capabilities"}),(0,a.jsx)("li",{children:"• Excellent at maintaining consistent tone and style"}),(0,a.jsx)("li",{children:"• Strong performance on long-form content"}),(0,a.jsx)("li",{children:"• Great for editing and improving existing content"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Fallback: GPT-4 / Gemini 2.5 Pro"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Versatile models for diverse content types and quick generation"}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Fast content generation for high-volume needs"}),(0,a.jsx)("li",{children:"• Good at research and fact-checking"}),(0,a.jsx)("li",{children:"• Excellent for SEO-optimized content"}),(0,a.jsx)("li",{children:"• Cost-effective for bulk content creation"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Content Types"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Blog Posts & Articles"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"Long-form content with research and SEO optimization"}),(0,a.jsx)("div",{className:"text-xs text-blue-600 font-medium",children:"Temperature: 0.7"})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Marketing Copy"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"Persuasive copy for ads, emails, and landing pages"}),(0,a.jsx)("div",{className:"text-xs text-green-600 font-medium",children:"Temperature: 0.8"})]}),(0,a.jsxs)("div",{className:"p-4 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Creative Writing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"Stories, scripts, and imaginative content"}),(0,a.jsx)("div",{className:"text-xs text-purple-600 font-medium",children:"Temperature: 0.9"})]})]})]})]}),"use-cases"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Use Cases"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Discover how RouKey can optimize your specific use case with intelligent routing and cost savings."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Development & Coding"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Optimize your development workflow with specialized coding models and intelligent routing."}),(0,a.jsx)("div",{className:"text-[#ff6b35] font-medium",children:"→ Development Guide"})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Content Creation"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Enhance your content creation with models optimized for writing and creativity."}),(0,a.jsx)("div",{className:"text-[#ff6b35] font-medium",children:"→ Content Creation Guide"})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Enterprise Applications"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Scale your enterprise AI applications with reliable routing and cost optimization."}),(0,a.jsx)("div",{className:"text-[#ff6b35] font-medium",children:"→ Enterprise Guide"})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Educational Platforms"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Build intelligent tutoring systems and educational tools with RouKey."}),(0,a.jsx)("div",{className:"text-[#ff6b35] font-medium",children:"→ Education Guide"})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Development & Coding"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Code Generation & Review"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Route coding tasks to specialized models like Claude 4 Opus for optimal code quality and cost efficiency."}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Recommended Strategy:"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:'Intelligent Role Routing with "coding" role detection'})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Documentation & Comments"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Automatically route documentation tasks to cost-effective models while maintaining quality."}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Cost Savings:"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Up to 70% reduction using complexity-based routing"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Content Creation"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Blog Posts & Articles"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Route creative writing tasks to models optimized for content generation with appropriate complexity analysis."}),(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Multi-Role Workflow:"}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Research and outline generation"}),(0,a.jsx)("li",{children:"• Content writing and expansion"}),(0,a.jsx)("li",{children:"• Editing and refinement"}),(0,a.jsx)("li",{children:"• SEO optimization"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Marketing Copy"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Optimize marketing content creation with role-based routing for different content types."}),(0,a.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Specialized Roles:"}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Email campaigns"}),(0,a.jsx)("li",{children:"• Social media posts"}),(0,a.jsx)("li",{children:"• Ad copy generation"}),(0,a.jsx)("li",{children:"• Product descriptions"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Enterprise Applications"}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Customer Support"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Intelligent routing based on query complexity and urgency."}),(0,a.jsx)("div",{className:"text-sm text-purple-600 font-medium",children:"Strategy: Complexity + Fallback"})]}),(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Data Analysis"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Route analytical tasks to models optimized for reasoning and computation."}),(0,a.jsx)("div",{className:"text-sm text-blue-600 font-medium",children:"Strategy: Role-based Routing"})]}),(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Document Processing"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Efficient processing of large document sets with cost optimization."}),(0,a.jsx)("div",{className:"text-sm text-green-600 font-medium",children:"Strategy: Cost-optimized"})]})]})})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Educational Platforms"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Personalized Learning"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Adapt content difficulty and teaching style based on student needs with intelligent routing."}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Beginner explanations → Cost-effective models"}),(0,a.jsx)("li",{children:"• Advanced topics → Premium models"}),(0,a.jsx)("li",{children:"• Interactive exercises → Specialized models"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Content Generation"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Generate educational content at scale while maintaining quality and controlling costs."}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Quiz generation"}),(0,a.jsx)("li",{children:"• Lesson plan creation"}),(0,a.jsx)("li",{children:"• Assignment feedback"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Research & Analysis"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"RouKey's multi-role orchestration excels at complex research workflows that require coordination between different AI capabilities."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Market Research"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Data collection and web browsing"}),(0,a.jsx)("li",{children:"• Competitive analysis"}),(0,a.jsx)("li",{children:"• Trend identification"}),(0,a.jsx)("li",{children:"• Report generation"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Academic Research"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Literature review"}),(0,a.jsx)("li",{children:"• Data analysis"}),(0,a.jsx)("li",{children:"• Hypothesis generation"}),(0,a.jsx)("li",{children:"• Paper writing assistance"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-8 rounded-xl text-white",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Ready to optimize your use case?"}),(0,a.jsx)("p",{className:"text-white/90 mb-6",children:"Contact our team to discuss how RouKey can be tailored to your specific requirements and use case."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("button",{onClick:()=>s("getting-started"),className:"bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors",children:"Get Started"}),(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"bg-white/20 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/30 transition-colors text-center",children:"Contact Sales"})]})]})]}),"use-cases-enterprise-apps"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Enterprise Applications"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Scale your enterprise AI applications with RouKey's reliable routing, cost optimization, and enterprise-grade features."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Enterprise Benefits"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Cost Optimization"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"Reduce AI costs by 40-60% through intelligent routing"}),(0,a.jsxs)("ul",{className:"text-gray-600 text-xs space-y-1",children:[(0,a.jsx)("li",{children:"• Route simple queries to cheaper models"}),(0,a.jsx)("li",{children:"• Use premium models only when necessary"}),(0,a.jsx)("li",{children:"• Automatic cost tracking and analytics"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Reliability & Scale"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"99.9% uptime with automatic failover"}),(0,a.jsxs)("ul",{className:"text-gray-600 text-xs space-y-1",children:[(0,a.jsx)("li",{children:"• Multiple provider redundancy"}),(0,a.jsx)("li",{children:"• Automatic retry logic"}),(0,a.jsx)("li",{children:"• Load balancing across API keys"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Security & Compliance"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"Enterprise-grade security and data protection"}),(0,a.jsxs)("ul",{className:"text-gray-600 text-xs space-y-1",children:[(0,a.jsx)("li",{children:"• Encrypted API key storage"}),(0,a.jsx)("li",{children:"• SOC 2 compliance ready"}),(0,a.jsx)("li",{children:"• Audit logs and monitoring"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Analytics & Insights"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"Comprehensive usage analytics and reporting"}),(0,a.jsxs)("ul",{className:"text-gray-600 text-xs space-y-1",children:[(0,a.jsx)("li",{children:"• Cost breakdown by model/provider"}),(0,a.jsx)("li",{children:"• Performance metrics and trends"}),(0,a.jsx)("li",{children:"• Custom reporting dashboards"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Common Enterprise Use Cases"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-xl",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Customer Support Automation"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Intelligent chatbots and support ticket routing with cost-optimized model selection."})]}),(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-xl",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Document Processing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Large-scale document analysis, summarization, and data extraction workflows."})]}),(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-xl",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Internal Tools & Automation"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"AI-powered internal tools for HR, legal, finance, and operations teams."})]})]})]})]}),"use-cases-educational-platforms"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Educational Platforms"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Build intelligent tutoring systems and educational tools with RouKey's adaptive routing and cost-effective scaling."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Educational Applications"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Intelligent Tutoring"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Personalized learning experiences with adaptive difficulty and explanations."})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Homework Assistance"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Step-by-step problem solving and concept explanations across subjects."})]}),(0,a.jsxs)("div",{className:"p-4 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Content Generation"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Automated quiz generation, lesson plans, and educational materials."})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Language Learning"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Conversational practice, grammar correction, and pronunciation feedback."})]}),(0,a.jsxs)("div",{className:"p-4 bg-red-50 rounded-xl border border-red-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Assessment & Grading"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Automated essay grading, feedback generation, and learning analytics."})]}),(0,a.jsxs)("div",{className:"p-4 bg-indigo-50 rounded-xl border border-indigo-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Research Assistance"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Academic research support, citation help, and literature reviews."})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Cost-Effective Scaling"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Educational platforms often need to serve thousands of students cost-effectively. RouKey's intelligent routing helps optimize costs:"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Simple Questions → Cheaper Models"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Basic math problems, vocabulary questions, and factual queries use cost-effective models."})]}),(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Complex Problems → Premium Models"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Advanced problem-solving, essay feedback, and detailed explanations use higher-quality models."})]})]})]})]}),"use-cases-research-analysis"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Research & Analysis"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Accelerate your research and analysis workflows with RouKey's intelligent routing for data processing and insights generation."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Research Applications"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Literature Review"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Automated paper summarization, key finding extraction, and research gap identification."})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Data Analysis"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Statistical analysis interpretation, trend identification, and insight generation."})]}),(0,a.jsxs)("div",{className:"p-4 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Report Generation"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Automated research reports, executive summaries, and presentation materials."})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Survey Analysis"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Qualitative data analysis, sentiment analysis, and theme extraction."})]}),(0,a.jsxs)("div",{className:"p-4 bg-red-50 rounded-xl border border-red-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Hypothesis Generation"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Research question formulation and experimental design suggestions."})]}),(0,a.jsxs)("div",{className:"p-4 bg-indigo-50 rounded-xl border border-indigo-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Citation & References"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Automated citation formatting, reference checking, and bibliography generation."})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Recommended Model Strategy"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Analysis & Reasoning: GPT-4 / Claude 4 Opus"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Complex analytical tasks requiring deep reasoning and nuanced understanding."})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Data Processing: Gemini 2.5 Pro"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Large-scale data processing, summarization, and pattern recognition tasks."})]}),(0,a.jsxs)("div",{className:"p-4 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Writing & Reports: Claude 4 Opus"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"High-quality research writing, report generation, and academic formatting."})]})]})]})]}),"examples-javascript-examples"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"JavaScript/Node.js Examples"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Complete JavaScript and Node.js examples for integrating RouKey into your applications."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Basic Fetch Example"}),(0,a.jsx)(d,{title:"Simple chat completion with fetch",language:"javascript",children:"const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {\n  method: 'POST',\n  headers: {\n    'Content-Type': 'application/json',\n    'X-API-Key': 'rk_live_your_api_key_here'\n  },\n  body: JSON.stringify({\n    messages: [\n      { role: 'user', content: 'Hello! How does RouKey work?' }\n    ],\n    stream: false\n    // Note: model parameter is optional - RouKey routes based on your configuration\n  })\n});\n\nconst data = await response.json();\nconsole.log(data.choices[0].message.content);"}),(0,a.jsx)("div",{className:"mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,a.jsx)("strong",{children:"Note:"})," RouKey automatically routes your request based on your configured routing strategy and API keys. The model parameter is optional and used for OpenAI compatibility."]})})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Streaming Example"}),(0,a.jsx)(d,{title:"Streaming responses with Server-Sent Events",language:"javascript",children:"async function streamCompletion() {\n  const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n      'X-API-Key': 'rk_live_your_api_key_here'\n    },\n    body: JSON.stringify({\n      messages: [\n        { role: 'user', content: 'Write a short story about AI' }\n      ],\n      stream: true\n    })\n  });\n\n  const reader = response.body.getReader();\n  const decoder = new TextDecoder();\n\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) break;\n\n    const chunk = decoder.decode(value);\n    const lines = chunk.split('\\n');\n\n    for (const line of lines) {\n      if (line.startsWith('data: ')) {\n        const data = line.slice(6);\n        if (data === '[DONE]') return;\n\n        try {\n          const parsed = JSON.parse(data);\n          const content = parsed.choices[0]?.delta?.content;\n          if (content) {\n            process.stdout.write(content);\n          }\n        } catch (e) {\n          // Skip invalid JSON\n        }\n      }\n    }\n  }\n}"}),(0,a.jsx)("div",{className:"mt-4 p-4 bg-green-50 rounded-lg border border-green-200",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,a.jsx)("strong",{children:"Streaming:"})," RouKey automatically handles streaming for complex multi-role tasks. Your routing strategy determines which models are used for the response."]})})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Express.js Integration"}),(0,a.jsx)(d,{title:"Express.js API endpoint with RouKey",language:"javascript",children:"const express = require('express');\nconst app = express();\n\napp.use(express.json());\n\napp.post('/api/chat', async (req, res) => {\n  try {\n    const { message } = req.body;\n\n    const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-API-Key': process.env.ROUKEY_API_KEY\n      },\n      body: JSON.stringify({\n        messages: [\n          { role: 'user', content: message }\n        ],\n        stream: false,\n        temperature: 0.7  // Optional: customize parameters\n      })\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      return res.status(response.status).json({ error: data.error });\n    }\n\n    res.json({\n      response: data.choices[0].message.content,\n      usage: data.usage,\n      rokey_metadata: data.rokey_metadata  // RouKey-specific information\n    });\n  } catch (error) {\n    console.error('RouKey API Error:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\napp.listen(3000, () => {\n  console.log('Server running on port 3000');\n});"})]})]}),"examples-python-examples"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Python Examples"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Complete Python examples for integrating RouKey into your applications and scripts."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Basic Requests Example"}),(0,a.jsx)(d,{title:"Simple chat completion with requests",language:"python",children:'import requests\nimport json\n\ndef chat_with_roukey(message):\n    url = "https://roukey.online/api/external/v1/chat/completions"\n\n    headers = {\n        "Content-Type": "application/json",\n        "X-API-Key": "rk_live_your_api_key_here"\n    }\n\n    data = {\n        "messages": [\n            {"role": "user", "content": message}\n        ],\n        "stream": False,\n        "temperature": 0.7  # Optional: customize parameters\n        # Note: model parameter is optional - RouKey routes automatically\n    }\n\n    response = requests.post(url, headers=headers, json=data)\n\n    if response.status_code == 200:\n        result = response.json()\n        print(f"Routing info: {result.get(\'rokey_metadata\', {})}")\n        return result["choices"][0]["message"]["content"]\n    else:\n        print(f"Error: {response.status_code} - {response.text}")\n        return None\n\n# Usage\nresponse = chat_with_roukey("Hello! How does RouKey work?")\nprint(response)'})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Streaming Example"}),(0,a.jsx)(d,{title:"Streaming responses with requests",language:"python",children:'import requests\nimport json\n\ndef stream_chat_completion(message):\n    url = "https://roukey.online/api/external/v1/chat/completions"\n\n    headers = {\n        "Content-Type": "application/json",\n        "X-API-Key": "rk_live_your_api_key_here"\n    }\n\n    data = {\n        "messages": [\n            {"role": "user", "content": message}\n        ],\n        "stream": True\n    }\n\n    response = requests.post(url, headers=headers, json=data, stream=True)\n\n    for line in response.iter_lines():\n        if line:\n            line = line.decode(\'utf-8\')\n            if line.startswith(\'data: \'):\n                data_str = line[6:]  # Remove \'data: \' prefix\n                if data_str == \'[DONE]\':\n                    break\n\n                try:\n                    data = json.loads(data_str)\n                    content = data.get(\'choices\', [{}])[0].get(\'delta\', {}).get(\'content\', \'\')\n                    if content:\n                        print(content, end=\'\', flush=True)\n                except json.JSONDecodeError:\n                    continue\n\n# Usage\nstream_chat_completion("Write a short story about AI")\nprint()  # New line after streaming'})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"FastAPI Integration"}),(0,a.jsx)(d,{title:"FastAPI endpoint with RouKey",language:"python",children:'from fastapi import FastAPI, HTTPException\nfrom pydantic import BaseModel\nimport requests\nimport os\n\napp = FastAPI()\n\nclass ChatRequest(BaseModel):\n    message: str\n    temperature: float = 0.7  # Optional parameter\n\nclass ChatResponse(BaseModel):\n    response: str\n    usage: dict\n    routing_info: dict  # RouKey metadata\n\<EMAIL>("/api/chat", response_model=ChatResponse)\nasync def chat_endpoint(request: ChatRequest):\n    try:\n        url = "https://roukey.online/api/external/v1/chat/completions"\n\n        headers = {\n            "Content-Type": "application/json",\n            "X-API-Key": os.getenv("ROUKEY_API_KEY")\n        }\n\n        data = {\n            "messages": [\n                {"role": "user", "content": request.message}\n            ],\n            "stream": False,\n            "temperature": request.temperature\n        }\n\n        response = requests.post(url, headers=headers, json=data)\n\n        if response.status_code != 200:\n            raise HTTPException(\n                status_code=response.status_code,\n                detail=f"RouKey API error: {response.text}"\n            )\n\n        result = response.json()\n\n        return ChatResponse(\n            response=result["choices"][0]["message"]["content"],\n            usage=result.get("usage", {}),\n            routing_info=result.get("rokey_metadata", {})\n        )\n\n    except Exception as e:\n        raise HTTPException(status_code=500, detail=str(e))\n\nif __name__ == "__main__":\n    import uvicorn\n    uvicorn.run(app, host="0.0.0.0", port=8000)'})]})]}),"examples"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Examples"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Practical examples to get you started with RouKey in different programming languages."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"JavaScript/Node.js"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Complete examples for web applications and Node.js backends."}),(0,a.jsx)("div",{className:"text-[#ff6b35] font-medium",children:"→ JavaScript Examples"})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Python"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Python examples for data science, web apps, and automation."}),(0,a.jsx)("div",{className:"text-[#ff6b35] font-medium",children:"→ Python Examples"})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"cURL"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Command-line examples for testing and shell scripting."}),(0,a.jsx)("div",{className:"text-[#ff6b35] font-medium",children:"→ cURL Examples"})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"OpenAI SDK Integration"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Drop-in replacement examples using OpenAI SDKs."}),(0,a.jsx)("div",{className:"text-[#ff6b35] font-medium",children:"→ OpenAI SDK Examples"})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"JavaScript/Node.js"}),(0,a.jsx)(d,{title:"Basic chat completion with fetch",language:"javascript",children:"const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {\n  method: 'POST',\n  headers: {\n    'Content-Type': 'application/json',\n    'X-API-Key': 'rk_live_your_api_key_here'\n  },\n  body: JSON.stringify({\n    messages: [\n      { role: 'user', content: 'Explain machine learning in simple terms' }\n    ],\n    stream: false,\n    max_tokens: 500\n  })\n});\n\nconst data = await response.json();\nconsole.log(data.choices[0].message.content);"}),(0,a.jsx)(d,{title:"Streaming response example",language:"javascript",children:"const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {\n  method: 'POST',\n  headers: {\n    'Content-Type': 'application/json',\n    'X-API-Key': 'rk_live_your_api_key_here'\n  },\n  body: JSON.stringify({\n    messages: [\n      { role: 'user', content: 'Write a detailed explanation of quantum computing' }\n    ],\n    stream: true,\n    max_tokens: 1000\n  })\n});\n\nconst reader = response.body.getReader();\nconst decoder = new TextDecoder();\n\nwhile (true) {\n  const { done, value } = await reader.read();\n  if (done) break;\n\n  const chunk = decoder.decode(value);\n  const lines = chunk.split('\\n');\n\n  for (const line of lines) {\n    if (line.startsWith('data: ')) {\n      const data = line.slice(6);\n      if (data === '[DONE]') return;\n\n      try {\n        const parsed = JSON.parse(data);\n        const content = parsed.choices[0]?.delta?.content;\n        if (content) {\n          process.stdout.write(content);\n        }\n      } catch (e) {\n        // Skip invalid JSON\n      }\n    }\n  }\n}"})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Python"}),(0,a.jsx)(d,{title:"Basic chat completion with requests",language:"python",children:"import requests\nimport json\n\nresponse = requests.post(\n    'https://roukey.online/api/external/v1/chat/completions',\n    headers={\n        'Content-Type': 'application/json',\n        'X-API-Key': 'rk_live_your_api_key_here'\n    },\n    json={\n        'messages': [\n            {'role': 'user', 'content': 'Explain machine learning in simple terms'}\n        ],\n        'stream': False,\n        'max_tokens': 500\n    }\n)\n\ndata = response.json()\nprint(data['choices'][0]['message']['content'])"}),(0,a.jsx)(d,{title:"Streaming response with requests",language:"python",children:"import requests\nimport json\n\nresponse = requests.post(\n    'https://roukey.online/api/external/v1/chat/completions',\n    headers={\n        'Content-Type': 'application/json',\n        'X-API-Key': 'rk_live_your_api_key_here'\n    },\n    json={\n        'messages': [\n            {'role': 'user', 'content': 'Write a detailed explanation of quantum computing'}\n        ],\n        'stream': True,\n        'max_tokens': 1000\n    },\n    stream=True\n)\n\nfor line in response.iter_lines():\n    if line:\n        line = line.decode('utf-8')\n        if line.startswith('data: '):\n            data = line[6:]\n            if data == '[DONE]':\n                break\n            try:\n                parsed = json.loads(data)\n                content = parsed['choices'][0]['delta'].get('content', '')\n                if content:\n                    print(content, end='', flush=True)\n            except json.JSONDecodeError:\n                continue"})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"cURL"}),(0,a.jsx)(d,{title:"Basic request with cURL",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [\n      {"role": "user", "content": "Hello, how are you?"}\n    ],\n    "stream": false,\n    "max_tokens": 150\n  }\''}),(0,a.jsx)(d,{title:"Role-based routing with cURL",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [\n      {"role": "user", "content": "Write a Python function to calculate fibonacci numbers"}\n    ],\n    "role": "coding",\n    "stream": true,\n    "max_tokens": 500\n  }\''})]}),(0,a.jsxs)(c,{type:"info",children:[(0,a.jsx)("strong",{children:"Need more examples?"})," Check out our GitHub repository for complete example applications and integration guides for popular frameworks like React, Vue, and Express.js."]})]}),"examples-curl-examples"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"cURL Examples"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Command-line examples using cURL for testing RouKey API endpoints and shell scripting."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Basic Chat Completion"}),(0,a.jsx)(d,{title:"Simple chat completion request",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [\n      {"role": "user", "content": "Hello! How does RouKey work?"}\n    ],\n    "stream": false\n  }\''})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Streaming Request"}),(0,a.jsx)(d,{title:"Streaming chat completion",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [\n      {"role": "user", "content": "Write a short story about AI"}\n    ],\n    "stream": true\n  }\' \\\n  --no-buffer'})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Advanced Parameters"}),(0,a.jsx)(d,{title:"Request with custom parameters",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [\n      {"role": "system", "content": "You are a helpful coding assistant."},\n      {"role": "user", "content": "Write a Python function to calculate fibonacci numbers"}\n    ],\n    "max_tokens": 500,\n    "temperature": 0.2,\n    "stream": false\n  }\''}),(0,a.jsx)("div",{className:"mt-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,a.jsx)("strong",{children:"Parameters:"})," RouKey supports all standard OpenAI parameters. The actual model used depends on your RouKey configuration, not a model parameter."]})})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Shell Script Example"}),(0,a.jsx)(d,{title:"Bash script for batch processing",language:"bash",children:'#!/bin/bash\n\n# RouKey API configuration\nAPI_KEY="rk_live_your_api_key_here"\nBASE_URL="https://roukey.online/api/external/v1"\n\n# Function to call RouKey API\ncall_roukey() {\n    local message="$1"\n\n    curl -s -X POST "$BASE_URL/chat/completions" \\\n        -H "Content-Type: application/json" \\\n        -H "X-API-Key: $API_KEY" \\\n        -d "{\n            \\"messages\\": [\n                {\\"role\\": \\"user\\", \\"content\\": \\"$message\\"}\n            ],\n            \\"stream\\": false\n        }" | jq -r \'.choices[0].message.content\'\n}\n\n# Process multiple queries\nqueries=(\n    "What is machine learning?"\n    "Explain neural networks"\n    "How does deep learning work?"\n)\n\nfor query in "${queries[@]}"; do\n    echo "Query: $query"\n    echo "Response: $(call_roukey "$query")"\n    echo "---"\ndone'})]})]}),"examples-openai-sdk"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"OpenAI SDK Integration"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Drop-in replacement examples using official OpenAI SDKs with RouKey as the backend."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Python OpenAI SDK"}),(0,a.jsx)(d,{title:"OpenAI Python SDK with RouKey",language:"python",children:'from openai import OpenAI\n\n# Initialize OpenAI client with RouKey\nclient = OpenAI(\n    api_key="rk_live_your_api_key_here",\n    base_url="https://roukey.online/api/external/v1"\n)\n\n# Standard chat completion\nresponse = client.chat.completions.create(\n    messages=[\n        {"role": "user", "content": "Hello! How does RouKey work?"}\n    ],\n    # model parameter is optional - RouKey routes based on your configuration\n    temperature=0.7,  # Optional: customize parameters\n    stream=False\n)\n\nprint(response.choices[0].message.content)\nprint(f"Routing info: {getattr(response, \'rokey_metadata\', {})}")\n\n# Streaming example\nstream = client.chat.completions.create(\n    messages=[\n        {"role": "user", "content": "Write a short story about AI"}\n    ],\n    stream=True\n)\n\nfor chunk in stream:\n    if chunk.choices[0].delta.content is not None:\n        print(chunk.choices[0].delta.content, end="")'}),(0,a.jsx)("div",{className:"mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,a.jsx)("strong",{children:"SDK Integration:"})," RouKey works as a drop-in replacement for OpenAI's API. The model parameter is optional - RouKey automatically routes based on your dashboard configuration."]})})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"JavaScript OpenAI SDK"}),(0,a.jsx)(d,{title:"OpenAI JavaScript SDK with RouKey",language:"javascript",children:"import OpenAI from 'openai';\n\n// Initialize OpenAI client with RouKey\nconst openai = new OpenAI({\n  apiKey: 'rk_live_your_api_key_here',\n  baseURL: 'https://roukey.online/api/external/v1'\n});\n\n// Standard chat completion\nasync function chatCompletion() {\n  const response = await openai.chat.completions.create({\n    messages: [\n      { role: 'user', content: 'Hello! How does RouKey work?' }\n    ],\n    // model parameter is optional - RouKey routes automatically\n    temperature: 0.7,  // Optional: customize parameters\n    stream: false\n  });\n\n  console.log(response.choices[0].message.content);\n  console.log('Routing info:', response.rokey_metadata);\n}\n\n// Streaming example\nasync function streamingCompletion() {\n  const stream = await openai.chat.completions.create({\n    messages: [\n      { role: 'user', content: 'Write a short story about AI' }\n    ],\n    stream: true\n  });\n\n  for await (const chunk of stream) {\n    const content = chunk.choices[0]?.delta?.content;\n    if (content) {\n      process.stdout.write(content);\n    }\n  }\n}\n\n// Usage\nchatCompletion();\nstreamingCompletion();"})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Migration Guide"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Easy Migration"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Migrating from OpenAI to RouKey requires only two changes to your existing code:"}),(0,a.jsxs)("ul",{className:"text-gray-600 space-y-2",children:[(0,a.jsxs)("li",{children:["• Change the ",(0,a.jsx)("code",{className:"bg-gray-100 px-2 py-1 rounded",children:"api_key"})," to your RouKey API key"]}),(0,a.jsxs)("li",{children:["• Set ",(0,a.jsx)("code",{className:"bg-gray-100 px-2 py-1 rounded",children:"base_url"})," to RouKey's endpoint"]})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Benefits of Using RouKey"}),(0,a.jsxs)("ul",{className:"text-gray-600 space-y-2",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Cost Optimization:"})," Intelligent routing reduces costs by 40-60%"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Higher Reliability:"})," Automatic failover across multiple providers"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"More Models:"})," Access to 300+ models from different providers"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Same Interface:"})," No code changes needed beyond configuration"]})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"⚠️ Important: Model Parameter Behavior"}),(0,a.jsxs)("p",{className:"text-gray-600 mb-3",children:["Unlike OpenAI's API, RouKey doesn't use the ",(0,a.jsx)("code",{className:"bg-gray-100 px-2 py-1 rounded",children:"model"})," parameter for routing:"]}),(0,a.jsxs)("ul",{className:"text-gray-600 space-y-2",children:[(0,a.jsxs)("li",{children:["• The ",(0,a.jsx)("code",{className:"bg-gray-100 px-2 py-1 rounded",children:"model"})," parameter is optional and ignored for routing"]}),(0,a.jsxs)("li",{children:["• RouKey routes based on your ",(0,a.jsx)("strong",{children:"dashboard configuration"})]}),(0,a.jsx)("li",{children:"• Your routing strategy determines which models are actually used"}),(0,a.jsx)("li",{children:"• This allows for intelligent cost optimization and failover"})]})]})]})]})]}),"api-reference-base-url"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Base URL"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"RouKey's API is accessible through a single base URL for all endpoints."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Production Base URL"}),(0,a.jsx)(d,{title:"Production Base URL",children:"https://roukey.online/api/external/v1"}),(0,a.jsx)("p",{className:"text-gray-600 mt-4",children:"All API requests should be made to endpoints under this base URL. For example, the chat completions endpoint would be:"}),(0,a.jsx)(d,{title:"Chat completions endpoint",children:"https://roukey.online/api/external/v1/chat/completions"})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"HTTPS Required"}),(0,a.jsxs)(c,{type:"warning",children:[(0,a.jsx)("strong",{children:"Security Notice:"})," All API requests must use HTTPS. HTTP requests will be rejected for security reasons."]})]})]}),"api-reference-chat-completions"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Chat Completions"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Create a chat completion using RouKey's intelligent routing. This endpoint is fully compatible with OpenAI's API."})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl mb-6 border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsx)("span",{className:"bg-green-600 text-white px-3 py-1 rounded-lg text-sm font-medium",children:"POST"}),(0,a.jsx)("code",{className:"text-gray-900 text-lg font-mono",children:"/chat/completions"})]}),(0,a.jsx)("p",{className:"text-gray-600",children:"OpenAI-compatible endpoint with RouKey's intelligent routing capabilities"})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Basic Example"}),(0,a.jsx)(d,{title:"Basic chat completion request",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [\n      {"role": "user", "content": "Hello! How does RouKey work?"}\n    ],\n    "stream": false\n  }\''})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Multimodal Content Support"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"RouKey supports multimodal content including images, allowing you to send both text and visual content in your messages."}),(0,a.jsx)(d,{title:"Multimodal request with image",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [\n      {\n        "role": "user",\n        "content": [\n          {\n            "type": "text",\n            "text": "What do you see in this image?"\n          },\n          {\n            "type": "image_url",\n            "image_url": {\n              "url": "https://example.com/image.jpg"\n            }\n          }\n        ]\n      }\n    ],\n    "stream": false\n  }\''}),(0,a.jsxs)(c,{type:"info",children:[(0,a.jsx)("strong",{children:"Multimodal Support:"})," RouKey supports sending images to any configured model. Ensure your selected models have vision capabilities for optimal image processing."]})]}),(0,a.jsxs)("div",{className:"card p-8 bg-blue-50 border border-blue-200",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"\uD83C\uDFAF How RouKey Routing Works"}),(0,a.jsxs)("div",{className:"space-y-4 text-gray-700",children:[(0,a.jsxs)("p",{className:"text-lg",children:[(0,a.jsx)("strong",{children:"Important:"})," RouKey doesn't use the ",(0,a.jsx)("code",{className:"bg-white px-2 py-1 rounded text-sm",children:"model"})," parameter for routing decisions."]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"✅ What Controls Routing:"}),(0,a.jsxs)("ul",{className:"space-y-1 text-sm",children:[(0,a.jsx)("li",{children:"• Your configured routing strategy"}),(0,a.jsx)("li",{children:"• API keys you've added to your setup"}),(0,a.jsx)("li",{children:"• Intelligent role detection"}),(0,a.jsx)("li",{children:"• Complexity analysis"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"ℹ️ Model Parameter:"}),(0,a.jsxs)("ul",{className:"space-y-1 text-sm",children:[(0,a.jsx)("li",{children:"• Optional (for OpenAI compatibility)"}),(0,a.jsx)("li",{children:"• Defaults to 'gpt-4.1' (compatibility only)"}),(0,a.jsx)("li",{children:"• Doesn't affect which model is used"}),(0,a.jsx)("li",{children:"• Actual model depends on your configuration"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Request Parameters"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full bg-white border border-gray-200 rounded-xl shadow-sm",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Parameter"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Type"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Required"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Description"})]})}),(0,a.jsxs)("tbody",{className:"divide-y divide-gray-200",children:[(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"messages"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"array"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-green-600",children:"Yes"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Array of message objects with role and content (supports multimodal content)"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"stream"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"boolean"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"No"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Whether to stream the response (default: false)"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"temperature"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"number"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"No"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Sampling temperature (0.0 to 2.0)"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"max_tokens"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"integer"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"No"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Maximum tokens to generate"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"top_p"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"number"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"No"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Nucleus sampling parameter (0.0 to 1.0)"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"frequency_penalty"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"number"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"No"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Frequency penalty (-2.0 to 2.0)"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"presence_penalty"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"number"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"No"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Presence penalty (-2.0 to 2.0)"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"stop"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"string | array"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"No"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Stop sequences (string or array of strings)"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"n"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"integer"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"No"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Number of completions to generate (default: 1)"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"role"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"string"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"No"}),(0,a.jsxs)("td",{className:"px-6 py-4 text-sm text-gray-600",children:[(0,a.jsx)("span",{className:"text-[#ff6b35] font-medium",children:"RouKey-specific"})," - Custom role for intelligent routing"]})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"model"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"string"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"No"}),(0,a.jsxs)("td",{className:"px-6 py-4 text-sm text-gray-600",children:[(0,a.jsx)("span",{className:"text-yellow-600 font-medium",children:"OpenAI compatibility only"})," - Does not control routing; RouKey routes based on your configuration."]})]})]})]})})]})]}),"api-reference-streaming"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Streaming"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"RouKey supports streaming responses for real-time applications and better user experience."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Streaming Request"}),(0,a.jsx)(d,{title:"Streaming chat completion",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [\n      {"role": "user", "content": "Write a short story about AI"}\n    ],\n    "stream": true\n  }\''})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Streaming Response Format"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Streaming responses are sent as Server-Sent Events (SSE) with each chunk containing a JSON object:"}),(0,a.jsx)(d,{title:"Streaming response chunk",language:"json",children:'data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"routed-model","choices":[{"delta":{"content":"Hello"},"index":0,"finish_reason":null}]}\n\ndata: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"routed-model","choices":[{"delta":{"content":" there!"},"index":0,"finish_reason":null}]}\n\ndata: [DONE]'}),(0,a.jsx)("div",{className:"mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,a.jsx)("strong",{children:"Note:"})," The model field in the response reflects the actual model used by RouKey's routing system, not necessarily the model parameter from your request."]})})]})]}),"api-reference-async-endpoints"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Async Endpoints"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"For complex multi-role tasks, RouKey provides dedicated async endpoints to handle long-running operations without timeout limits."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Submit Async Job"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Submit a request for asynchronous processing. Ideal for complex multi-role tasks that may take several minutes."}),(0,a.jsx)(d,{title:"POST /api/external/v1/async/submit",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/async/submit" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [\n      {"role": "user", "content": "Create a comprehensive business plan for a tech startup"}\n    ],\n    "temperature": 0.7,\n    "webhook_url": "https://your-app.com/webhook/roukey"\n  }\''}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mt-6 mb-4",children:"Request Parameters"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full bg-white border border-gray-200 rounded-xl shadow-sm",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-900",children:"Parameter"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-900",children:"Type"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-900",children:"Required"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-900",children:"Description"})]})}),(0,a.jsxs)("tbody",{className:"divide-y divide-gray-200",children:[(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-4 py-3 text-sm font-mono text-[#ff6b35]",children:"messages"}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"array"}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-green-600",children:"Yes"}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"Array of message objects"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-4 py-3 text-sm font-mono text-[#ff6b35]",children:"webhook_url"}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"string"}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"No"}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"URL to receive completion notifications"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-4 py-3 text-sm font-mono text-[#ff6b35]",children:"role"}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"string"}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"No"}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"Custom role for routing"})]})]})]})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mt-6 mb-4",children:"Response"}),(0,a.jsx)(d,{title:"Async submit response",language:"json",children:'{\n  "job_id": "job_abc123def456",\n  "status": "pending",\n  "estimated_completion": "2024-01-15T10:35:00Z",\n  "created_at": "2024-01-15T10:30:00Z",\n  "progress_percentage": 0,\n  "polling_url": "https://roukey.online/api/external/v1/async/status/job_abc123def456",\n  "result_url": "https://roukey.online/api/external/v1/async/result/job_abc123def456",\n  "webhook_configured": true,\n  "message": "Job submitted for async processing. Use polling_url to check status or configure webhook_url for notifications."\n}'})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Check Job Status"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Poll the status of an async job to track progress and completion."}),(0,a.jsx)(d,{title:"GET /api/external/v1/async/status/{jobId}",language:"bash",children:'curl -X GET "https://roukey.online/api/external/v1/async/status/job_abc123def456" \\\n  -H "X-API-Key: rk_live_your_api_key_here"'}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mt-6 mb-4",children:"Response"}),(0,a.jsx)(d,{title:"Status response",language:"json",children:'{\n  "job_id": "job_abc123def456",\n  "status": "processing",\n  "progress_percentage": 65,\n  "created_at": "2024-01-15T10:30:00Z",\n  "started_at": "2024-01-15T10:30:15Z",\n  "estimated_completion": "2024-01-15T10:35:00Z",\n  "estimated_remaining_minutes": 2,\n  "elapsed_minutes": 3,\n  "roles_detected": ["business_analyst", "market_researcher"],\n  "webhook_configured": true,\n  "result_available": false,\n  "result_url": null\n}'})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Get Job Result"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Retrieve the completed result of an async job."}),(0,a.jsx)(d,{title:"GET /api/external/v1/async/result/{jobId}",language:"bash",children:'curl -X GET "https://roukey.online/api/external/v1/async/result/job_abc123def456" \\\n  -H "X-API-Key: rk_live_your_api_key_here"'}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mt-6 mb-4",children:"Response"}),(0,a.jsx)(d,{title:"Result response",language:"json",children:'{\n  "id": "chatcmpl-abc123",\n  "object": "chat.completion",\n  "created": **********,\n  "model": "routed-model",\n  "choices": [\n    {\n      "index": 0,\n      "message": {\n        "role": "assistant",\n        "content": "# Comprehensive Business Plan\\n\\n## Executive Summary\\n..."\n      },\n      "finish_reason": "stop"\n    }\n  ],\n  "usage": {\n    "prompt_tokens": 45,\n    "completion_tokens": 2847,\n    "total_tokens": 2892\n  },\n  "rokey_metadata": {\n    "job_id": "job_abc123def456",\n    "roles_used": ["business_analyst", "market_researcher"],\n    "total_processing_time_ms": 245000,\n    "routing_strategy": "intelligent_role"\n  }\n}'})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Job Status Values"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium",children:"pending"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Job is queued and waiting to start"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium",children:"processing"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Job is actively being processed"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium",children:"completed"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Job finished successfully, result available"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium",children:"failed"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Job failed, check error_message"})]})]})]}),(0,a.jsxs)(c,{type:"tip",children:[(0,a.jsx)("strong",{children:"Webhook Notifications:"})," Configure a webhook_url when submitting jobs to receive automatic notifications when processing completes, eliminating the need for polling."]})]}),"api-reference-image-support"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Image Support"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"RouKey supports multimodal content including images, allowing you to send visual content alongside text for analysis, description, and processing."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Supported Image Formats"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.SF,{className:"h-5 w-5 text-blue-600"}),"Image Formats"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• JPEG (.jpg, .jpeg)"}),(0,a.jsx)("li",{children:"• PNG (.png)"}),(0,a.jsx)("li",{children:"• WebP (.webp)"}),(0,a.jsx)("li",{children:"• GIF (.gif)"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.Sr,{className:"h-5 w-5 text-green-600"}),"Input Methods"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Base64 encoded images"}),(0,a.jsx)("li",{children:"• Public image URLs"}),(0,a.jsx)("li",{children:"• Data URLs (data:image/...)"}),(0,a.jsx)("li",{children:"• Multiple images per message"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Using Public Image URLs"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"The simplest way to include images is by referencing publicly accessible image URLs."}),(0,a.jsx)(d,{title:"Image URL example",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [\n      {\n        "role": "user",\n        "content": [\n          {\n            "type": "text",\n            "text": "What do you see in this image? Describe it in detail."\n          },\n          {\n            "type": "image_url",\n            "image_url": {\n              "url": "https://example.com/path/to/your/image.jpg"\n            }\n          }\n        ]\n      }\n    ]\n  }\''})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Using Base64 Encoded Images"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"For private images or when you want to send image data directly, use base64 encoding."}),(0,a.jsx)(d,{title:"Base64 image example",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [\n      {\n        "role": "user",\n        "content": [\n          {\n            "type": "text",\n            "text": "Analyze this chart and explain the trends."\n          },\n          {\n            "type": "image_url",\n            "image_url": {\n              "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="\n            }\n          }\n        ]\n      }\n    ]\n  }\''}),(0,a.jsxs)(c,{type:"tip",children:[(0,a.jsx)("strong",{children:"Base64 Encoding:"})," You can convert images to base64 using command line tools like ",(0,a.jsx)("code",{className:"bg-gray-100 px-2 py-1 rounded",children:"base64 image.jpg"})," or online converters."]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Multiple Images"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You can include multiple images in a single message for comparison, analysis, or batch processing."}),(0,a.jsx)(d,{title:"Multiple images example",language:"bash",children:'curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\\n  -H "Content-Type: application/json" \\\n  -H "X-API-Key: rk_live_your_api_key_here" \\\n  -d \'{\n    "messages": [\n      {\n        "role": "user",\n        "content": [\n          {\n            "type": "text",\n            "text": "Compare these two images and tell me the differences."\n          },\n          {\n            "type": "image_url",\n            "image_url": {\n              "url": "https://example.com/image1.jpg"\n            }\n          },\n          {\n            "type": "image_url",\n            "image_url": {\n              "url": "https://example.com/image2.jpg"\n            }\n          }\n        ]\n      }\n    ]\n  }\''})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Image Analysis Use Cases"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Visual Analysis"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600 text-sm",children:[(0,a.jsx)("li",{children:"• Object detection and identification"}),(0,a.jsx)("li",{children:"• Scene description and analysis"}),(0,a.jsx)("li",{children:"• Text extraction (OCR)"}),(0,a.jsx)("li",{children:"• Color and composition analysis"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-orange-50 rounded-xl border border-orange-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Business Applications"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600 text-sm",children:[(0,a.jsx)("li",{children:"• Document processing"}),(0,a.jsx)("li",{children:"• Chart and graph analysis"}),(0,a.jsx)("li",{children:"• Product catalog descriptions"}),(0,a.jsx)("li",{children:"• Quality control inspection"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Creative Tasks"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600 text-sm",children:[(0,a.jsx)("li",{children:"• Image captioning"}),(0,a.jsx)("li",{children:"• Style and mood analysis"}),(0,a.jsx)("li",{children:"• Creative writing prompts"}),(0,a.jsx)("li",{children:"• Art and design feedback"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Technical Analysis"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600 text-sm",children:[(0,a.jsx)("li",{children:"• Code screenshot analysis"}),(0,a.jsx)("li",{children:"• Diagram interpretation"}),(0,a.jsx)("li",{children:"• Technical documentation"}),(0,a.jsx)("li",{children:"• Error message debugging"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Vision Model Compatibility"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"When sending images, ensure your configured models support vision capabilities. RouKey will pass multimodal content to your selected models based on your routing strategy."}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-xl border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.BZ,{className:"h-5 w-5 text-blue-600"}),"Popular Vision-Capable Models"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"GPT-4 Vision:"})," Excellent for detailed analysis and complex reasoning"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Claude Vision:"})," Great for document analysis and text extraction"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Gemini Vision:"})," Fast processing for simple image tasks"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Model Configuration:"})," Ensure your API keys are configured with vision-capable models"]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Best Practices"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.Sr,{className:"h-5 w-5 text-green-600"}),"Optimization Tips"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Use clear, high-resolution images for better analysis"}),(0,a.jsx)("li",{children:"• Provide specific instructions about what to analyze"}),(0,a.jsx)("li",{children:"• For multiple images, explain the relationship between them"}),(0,a.jsx)("li",{children:"• Use public URLs when possible to reduce payload size"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.Pi,{className:"h-5 w-5 text-yellow-600"}),"Limitations"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Maximum image size: 20MB per image"}),(0,a.jsx)("li",{children:"• Base64 images increase request size significantly"}),(0,a.jsx)("li",{children:"• Some models may have different vision capabilities"}),(0,a.jsx)("li",{children:"• Processing time increases with image complexity"})]})]})]})]}),(0,a.jsxs)(c,{type:"info",children:[(0,a.jsx)("strong",{children:"Model Configuration:"})," Make sure your configured API keys include vision-capable models when working with images. RouKey will route requests according to your selected routing strategy."]})]}),"api-reference-parameters"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Parameters"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Complete reference for all supported parameters in RouKey API requests."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Common Parameters"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"border-l-4 border-[#ff6b35] pl-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"messages"}),(0,a.jsx)("p",{className:"text-gray-600 mb-2",children:"Array of message objects that make up the conversation."}),(0,a.jsx)(d,{title:"Message format",language:"json",children:'{\n  "messages": [\n    {"role": "system", "content": "You are a helpful assistant."},\n    {"role": "user", "content": "Hello!"},\n    {"role": "assistant", "content": "Hi there! How can I help you?"},\n    {"role": "user", "content": "What\'s the weather like?"}\n  ]\n}'})]}),(0,a.jsxs)("div",{className:"border-l-4 border-blue-500 pl-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"stream"}),(0,a.jsx)("p",{className:"text-gray-600 mb-2",children:"Boolean flag to enable streaming responses (default: false)."}),(0,a.jsx)(d,{title:"Streaming enabled",language:"json",children:'{\n  "messages": [...],\n  "stream": true\n}'})]}),(0,a.jsxs)("div",{className:"border-l-4 border-green-500 pl-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"max_tokens"}),(0,a.jsx)("p",{className:"text-gray-600 mb-2",children:"Maximum number of tokens to generate in the response."}),(0,a.jsx)(d,{title:"Token limit",language:"json",children:'{\n  "messages": [...],\n  "max_tokens": 1000\n}'})]})]})]})]}),"api-reference-responses"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Responses"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Understanding RouKey's response formats for both streaming and non-streaming requests."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Standard Response"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"RouKey returns OpenAI-compatible responses with additional metadata about routing and processing."}),(0,a.jsx)(d,{title:"Non-streaming response format",language:"json",children:'{\n  "id": "chatcmpl-123",\n  "object": "chat.completion",\n  "created": **********,\n  "model": "routed-model",\n  "choices": [\n    {\n      "index": 0,\n      "message": {\n        "role": "assistant",\n        "content": "Hello! I\'m here to help you with any questions you have."\n      },\n      "finish_reason": "stop"\n    }\n  ],\n  "usage": {\n    "prompt_tokens": 12,\n    "completion_tokens": 15,\n    "total_tokens": 27\n  },\n  "rokey_metadata": {\n    "routing_strategy": "complexity_based",\n    "selected_provider": "openai",\n    "selected_model": "gpt-4",\n    "processing_time_ms": 1250,\n    "roles_detected": ["general"],\n    "complexity_score": 0.3\n  }\n}'})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"RouKey Metadata"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Every response includes RouKey-specific metadata to help you understand how your request was processed."}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full bg-white border border-gray-200 rounded-xl shadow-sm",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Field"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Type"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-sm font-medium text-gray-900",children:"Description"})]})}),(0,a.jsxs)("tbody",{className:"divide-y divide-gray-200",children:[(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"routing_strategy"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"string"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:'Strategy used for routing (e.g., "complexity_based", "intelligent_role")'})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"selected_provider"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"string"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Provider that handled the request"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"selected_model"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"string"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Specific model used for generation"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"processing_time_ms"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"number"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Total processing time in milliseconds"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"roles_detected"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"array"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Roles detected in the request for multi-role routing"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-mono text-[#ff6b35]",children:"complexity_score"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"number"}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:"Complexity score (0.0-1.0) used for routing decisions"})]})]})]})})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Streaming Response"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Streaming responses are sent as Server-Sent Events with incremental content:"}),(0,a.jsx)(d,{title:"Streaming response chunks",language:"json",children:'data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"routed-model","choices":[{"delta":{"role":"assistant"},"index":0,"finish_reason":null}]}\n\ndata: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"routed-model","choices":[{"delta":{"content":"Hello"},"index":0,"finish_reason":null}]}\n\ndata: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"routed-model","choices":[{"delta":{"content":"!"},"index":0,"finish_reason":"stop"}]}\n\ndata: [DONE]'})]})]}),"api-reference-errors"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Error Handling"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"RouKey uses standard HTTP status codes and provides detailed error messages for troubleshooting."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Error Response Format"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"All RouKey API errors follow a consistent JSON structure with detailed error information."}),(0,a.jsx)(d,{title:"Error response structure",language:"json",children:'{\n  "error": {\n    "message": "API key is required. Provide it in Authorization header as \\"Bearer YOUR_API_KEY\\" or in x-api-key header.",\n    "type": "authentication_error",\n    "code": "invalid_api_key"\n  }\n}'})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"HTTP Status Codes"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-red-50 rounded-xl border border-red-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("span",{className:"bg-red-600 text-white px-2 py-1 rounded text-sm font-medium",children:"401"}),(0,a.jsx)("span",{className:"font-semibold text-gray-900",children:"Unauthorized"})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"Invalid, missing, or expired API key"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,a.jsx)("strong",{children:"Error type:"})," authentication_error",(0,a.jsx)("br",{}),(0,a.jsx)("strong",{children:"Common codes:"})," invalid_api_key, expired_api_key"]})]}),(0,a.jsxs)("div",{className:"p-4 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("span",{className:"bg-yellow-600 text-white px-2 py-1 rounded text-sm font-medium",children:"403"}),(0,a.jsx)("span",{className:"font-semibold text-gray-900",children:"Forbidden"})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"API key doesn't have required permissions or IP restrictions"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,a.jsx)("strong",{children:"Error type:"})," permission_denied",(0,a.jsx)("br",{}),(0,a.jsx)("strong",{children:"Common codes:"})," insufficient_permissions, ip_not_allowed"]})]}),(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("span",{className:"bg-blue-600 text-white px-2 py-1 rounded text-sm font-medium",children:"400"}),(0,a.jsx)("span",{className:"font-semibold text-gray-900",children:"Bad Request"})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"Invalid request parameters or malformed JSON"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,a.jsx)("strong",{children:"Error type:"})," api_error",(0,a.jsx)("br",{}),(0,a.jsx)("strong",{children:"Common codes:"})," invalid_request, validation_error"]})]}),(0,a.jsxs)("div",{className:"p-4 bg-orange-50 rounded-xl border border-orange-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("span",{className:"bg-orange-600 text-white px-2 py-1 rounded text-sm font-medium",children:"404"}),(0,a.jsx)("span",{className:"font-semibold text-gray-900",children:"Not Found"})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"Requested resource (e.g., async job) not found"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,a.jsx)("strong",{children:"Error type:"})," not_found_error",(0,a.jsx)("br",{}),(0,a.jsx)("strong",{children:"Common codes:"})," job_not_found, resource_not_found"]})]}),(0,a.jsxs)("div",{className:"p-4 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("span",{className:"bg-purple-600 text-white px-2 py-1 rounded text-sm font-medium",children:"500"}),(0,a.jsx)("span",{className:"font-semibold text-gray-900",children:"Internal Server Error"})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"Unexpected server error - please try again"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,a.jsx)("strong",{children:"Error type:"})," internal_error",(0,a.jsx)("br",{}),(0,a.jsx)("strong",{children:"Common codes:"})," server_error, internal_error"]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Error Examples"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Authentication Error"}),(0,a.jsx)(d,{title:"Missing API key",language:"json",children:'{\n  "error": {\n    "message": "API key is required. Provide it in Authorization header as \\"Bearer YOUR_API_KEY\\" or in x-api-key header.",\n    "type": "authentication_error",\n    "code": "invalid_api_key"\n  }\n}'})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Permission Error"}),(0,a.jsx)(d,{title:"Insufficient permissions",language:"json",children:'{\n  "error": {\n    "message": "API key does not have chat permission",\n    "type": "permission_error",\n    "code": "insufficient_permissions"\n  }\n}'})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Validation Error"}),(0,a.jsx)(d,{title:"Invalid request data",language:"json",children:'{\n  "error": {\n    "message": "Messages array cannot be empty and must contain at least one message.",\n    "type": "api_error",\n    "code": "validation_error"\n  }\n}'})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Async Job Not Found"}),(0,a.jsx)(d,{title:"Job not found",language:"json",children:'{\n  "error": {\n    "message": "Job not found",\n    "type": "not_found_error",\n    "code": "job_not_found"\n  }\n}'})]})]})]})]}),"routing-strategies-strategy-overview"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Strategy Overview"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"RouKey offers six distinct routing strategies, each designed for specific use cases and optimization goals."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Available Strategies"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Default Load Balancing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"Automatic distribution with zero configuration"}),(0,a.jsx)("div",{className:"text-xs text-blue-600 font-medium",children:"Strategy: none"})]}),(0,a.jsxs)("div",{className:"p-6 bg-[#ff6b35]/10 rounded-xl border border-[#ff6b35]/20",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Intelligent Role Routing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"AI-powered role classification and routing"}),(0,a.jsx)("div",{className:"text-xs text-[#ff6b35] font-medium",children:"Strategy: intelligent_role"})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Complexity-Based Routing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"Cost optimization through complexity analysis"}),(0,a.jsx)("div",{className:"text-xs text-green-600 font-medium",children:"Strategy: complexity_round_robin"})]}),(0,a.jsxs)("div",{className:"p-6 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Cost-Optimized Routing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"Learning-based cost optimization"}),(0,a.jsx)("div",{className:"text-xs text-purple-600 font-medium",children:"Strategy: cost_optimized"})]}),(0,a.jsxs)("div",{className:"p-6 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Strict Fallback"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"Predictable ordered failover sequence"}),(0,a.jsx)("div",{className:"text-xs text-yellow-600 font-medium",children:"Strategy: strict_fallback"})]}),(0,a.jsxs)("div",{className:"p-6 bg-indigo-50 rounded-xl border border-indigo-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"A/B Testing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"Split traffic for performance comparison"}),(0,a.jsx)("div",{className:"text-xs text-indigo-600 font-medium",children:"Strategy: ab_routing"})]})]})]})]}),"routing-strategies-intelligent-role-routing"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Intelligent Role Routing Setup"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Set up AI-powered role classification through the RouKey app interface to automatically route requests to specialized models."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Step-by-Step Setup"}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold",children:"1"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Create Custom Roles"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Navigate to your model configuration page and create custom roles for different task types."}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-xl border border-gray-200",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-500 rounded-lg mx-auto mb-2 flex items-center justify-center",children:(0,a.jsx)(l.r$,{className:"w-6 h-6 text-white"})}),(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"coding"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Programming tasks"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-500 rounded-lg mx-auto mb-2 flex items-center justify-center",children:(0,a.jsx)(l.AQ,{className:"w-6 h-6 text-white"})}),(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"writing"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Content creation"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-purple-500 rounded-lg mx-auto mb-2 flex items-center justify-center",children:(0,a.jsx)(l.r9,{className:"w-6 h-6 text-white"})}),(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"analysis"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Data analysis"})]})]})})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold",children:"2"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Assign Models to Roles"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"For each API key in your configuration, assign it to the appropriate roles based on the model's strengths."}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"DS"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"DeepSeek Coder"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Specialized for coding tasks"})]})]}),(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full",children:"coding"})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-xs font-bold text-purple-600",children:"C3"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Claude 3.5 Sonnet"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Excellent for writing and analysis"})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full",children:"writing"}),(0,a.jsx)("span",{className:"px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full",children:"analysis"})]})]})]})})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold",children:"3"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Enable Intelligent Role Routing"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:'Go to Advanced Routing Setup and select "Intelligent Role Routing" as your strategy.'}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-orange-50 to-red-50 p-4 rounded-xl border border-orange-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsx)(l.DQ,{className:"w-6 h-6 text-orange-600"}),(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Routing Strategy Selection"})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mb-3",children:["Navigate to: ",(0,a.jsx)("strong",{children:"Model Configuration → Advanced Routing Setup → Intelligent Role Routing"})]}),(0,a.jsx)("div",{className:"bg-white p-3 rounded-lg border border-orange-100",children:(0,a.jsx)("p",{className:"text-xs text-gray-700",children:"✅ RouKey will automatically classify user prompts and route to the best model for each role"})})]})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold",children:"4"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Set Default Fallback Model"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Configure a default general chat model for requests that don't match any specific role."}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-xl border border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)(l.Zu,{className:"w-5 h-5 text-gray-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Fallback Protection"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"If no role matches the user's request, RouKey will automatically use your designated default model to ensure every request gets a response."})]})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Role Management Tips"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.XL,{className:"w-5 h-5 text-blue-600"}),"Best Practices"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600 text-sm",children:[(0,a.jsx)("li",{children:"• Use descriptive role IDs (coding, writing, analysis)"}),(0,a.jsx)("li",{children:"• Assign models based on their strengths"}),(0,a.jsx)("li",{children:"• Test different role assignments to optimize performance"}),(0,a.jsx)("li",{children:"• Keep role descriptions clear and specific"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.Sr,{className:"w-5 h-5 text-green-600"}),"Common Role Types"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600 text-sm",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"coding:"})," Programming, debugging, code review"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"writing:"})," Content creation, editing, copywriting"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"analysis:"})," Data analysis, research, insights"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"creative:"})," Brainstorming, design, ideation"]})]})]})]})]}),(0,a.jsxs)(c,{type:"tip",children:[(0,a.jsx)("strong",{children:"Pro Tip:"})," Start with 2-3 basic roles (coding, writing, general) and expand as you identify specific use cases. RouKey's AI classification becomes more accurate with well-defined role assignments."]})]}),"routing-strategies-complexity-routing"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Complexity-Based Routing"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Optimize costs by routing simple requests to cheaper models and complex requests to more capable models."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Complexity Analysis"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"RouKey analyzes each request and assigns a complexity score from 1-5, then routes to the appropriate model tier:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-2",children:"1"}),(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 mb-1",children:"Simple"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Basic Q&A"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600 mb-2",children:"2"}),(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 mb-1",children:"Easy"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Explanations"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600 mb-2",children:"3"}),(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 mb-1",children:"Medium"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Analysis"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-orange-50 rounded-xl border border-orange-200",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600 mb-2",children:"4"}),(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 mb-1",children:"Hard"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Complex tasks"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-red-50 rounded-xl border border-red-200",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600 mb-2",children:"5"}),(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 mb-1",children:"Expert"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Advanced reasoning"})]})]})]})]}),"routing-strategies-cost-optimized"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Cost-Optimized Routing"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Machine learning-based routing that learns from usage patterns to minimize costs while maintaining quality."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Learning Algorithm"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"This strategy continuously learns from your usage patterns and optimizes routing decisions based on:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Cost Analysis"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Tracks actual costs per request type and optimizes for minimum spend"})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Quality Metrics"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Monitors response quality to ensure cost savings don't compromise results"})]}),(0,a.jsxs)("div",{className:"p-4 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Usage Patterns"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Learns from your specific use cases and request patterns"})]}),(0,a.jsxs)("div",{className:"p-4 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Performance Data"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Considers response time and success rates in routing decisions"})]})]})]})]}),"routing-strategies-strict-fallback"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Strict Fallback"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Predictable ordered failover sequence that tries API keys in a specific order for maximum reliability."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"How It Works"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white font-bold text-sm",children:"1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Primary API Key"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Always tries the first configured API key first"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold text-sm",children:"2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Secondary Fallback"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"If primary fails, tries the second API key"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm",children:"3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Continue Sequence"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Continues through all configured keys in order"})]})]})]})]})]}),"routing-strategies-ab-testing"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"A/B Testing"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Split traffic between different models to compare performance, cost, and quality metrics."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Traffic Splitting"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Configure percentage-based traffic distribution between different API keys or models:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Group A (70%)"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"Primary model for comparison"}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"70%"}})})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Group B (30%)"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"Alternative model being tested"}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"30%"}})})]})]})]})]}),"routing-strategies"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Routing Strategies"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"RouKey's intelligent routing strategies are the core of cost optimization and performance enhancement. Each strategy uses advanced AI classification and analysis to route requests to the optimal model based on your specific requirements."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Strategy Overview"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg leading-relaxed",children:"RouKey offers seven distinct routing strategies, each designed for specific use cases and optimization goals. All strategies include automatic failover, retry logic, and comprehensive analytics. The choice of strategy depends on your priorities: cost optimization, reliability, performance, or specialized routing requirements."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Default Load Balancing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Automatic distribution with zero configuration"})]}),(0,a.jsxs)("div",{className:"p-4 bg-[#ff6b35]/10 rounded-xl border border-[#ff6b35]/20",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Intelligent Role Routing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"AI-powered role classification and routing"})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Complexity-Based Routing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Cost optimization through complexity analysis"})]}),(0,a.jsxs)("div",{className:"p-4 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Cost-Optimized Routing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Learning-based cost optimization"})]}),(0,a.jsxs)("div",{className:"p-4 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Strict Fallback"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Predictable ordered failover sequence"})]}),(0,a.jsxs)("div",{className:"p-4 bg-indigo-50 rounded-xl border border-indigo-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"A/B Testing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Split traffic for performance comparison"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Default Load Balancing"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-xl border border-blue-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(l.DP,{className:"h-6 w-6 text-blue-600"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Zero Configuration Required"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"The default strategy requires no setup and automatically distributes requests across all active API keys in your configuration. Perfect for getting started quickly or when you want simple, reliable load balancing."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"How It Works"}),(0,a.jsxs)("ul",{className:"space-y-3 text-gray-600",children:[(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Round-Robin Distribution:"})," Requests are distributed evenly across all available API keys"]})]}),(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Automatic Retry:"})," Failed requests are automatically retried with different keys"]})]}),(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Health Monitoring:"})," Unhealthy keys are temporarily removed from rotation"]})]}),(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Load Balancing:"})," Prevents any single API key from being overwhelmed"]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Best Use Cases"}),(0,a.jsxs)("ul",{className:"space-y-3 text-gray-600",children:[(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)(l.Sr,{className:"h-5 w-5 text-green-600 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{children:"Getting started with RouKey"})]}),(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)(l.Sr,{className:"h-5 w-5 text-green-600 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{children:"Simple applications with consistent workloads"})]}),(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)(l.Sr,{className:"h-5 w-5 text-green-600 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{children:"High-availability requirements"})]}),(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)(l.Sr,{className:"h-5 w-5 text-green-600 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{children:"When you want zero configuration overhead"})]})]})]})]}),(0,a.jsxs)(c,{type:"tip",children:[(0,a.jsx)("strong",{children:"Performance:"})," This strategy provides excellent reliability with 99.9% uptime and automatic failover. While it doesn't optimize for cost like other strategies, it ensures consistent performance and is perfect for production workloads."]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Intelligent Role Routing"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 p-6 rounded-xl border border-[#ff6b35]/20",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(l.DQ,{className:"h-6 w-6 text-[#ff6b35]"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"AI-Powered Request Classification"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"RouKey's most sophisticated routing strategy uses advanced AI classification to analyze each request and determine the optimal model based on the detected role or task type. This enables specialized routing for different types of work like coding, writing, analysis, and more."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Supported Roles"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"general_chat"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"General conversation and Q&A"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"coding"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Code generation, debugging, and review"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"writing"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Content creation and editing"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"analysis"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Data analysis and research"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"creative"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Creative writing and brainstorming"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"How Classification Works"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:"1"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Request Analysis"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"RouKey's AI classifier analyzes the user's prompt to understand intent and context"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:"2"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Role Detection"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"The system identifies the most appropriate role category for the request"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:"3"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Model Selection"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Routes to the API key configured for that specific role"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:"4"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Fallback Handling"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"If no specific role match, uses the default general chat model"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 p-6 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Multi-Role Detection & Orchestration"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"For complex requests requiring multiple specialized capabilities, RouKey can detect multi-role requirements and orchestrate workflows using our advanced RouKey integration."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Sequential Workflow"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Single roles, step-by-step"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Supervisor Workflow"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"2-3 roles, coordinated"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Hierarchical Workflow"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"4+ roles, complex tasks"})]})]})]}),(0,a.jsxs)(c,{type:"info",children:[(0,a.jsx)("strong",{children:"Streaming Recommended:"})," For multi-role tasks, enable streaming to avoid timeouts and get real-time progress updates. RouKey automatically detects complex multi-role requirements and processes them asynchronously with streaming support."]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Complexity-Based Routing"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(l.OL,{className:"h-6 w-6 text-green-600"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Cost Optimization Through Intelligence"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"RouKey's complexity-based routing analyzes each prompt to determine its complexity level (1-5 scale) and routes it to the most cost-effective model capable of handling that complexity. This strategy can reduce costs by up to 60% while maintaining response quality."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Complexity Levels"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center text-white font-bold text-sm",children:"1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Simple"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Basic Q&A, simple requests"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-sm",children:"2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Basic"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Explanations, summaries"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center text-white font-bold text-sm",children:"3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Moderate"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Analysis, code review"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-orange-50 rounded-lg border border-orange-200",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center text-white font-bold text-sm",children:"4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Complex"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Advanced reasoning, complex code"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center text-white font-bold text-sm",children:"5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Expert"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Research, advanced problem-solving"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Routing Logic"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg border border-gray-200",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Exact Match Routing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"RouKey first attempts to route to API keys specifically configured for the detected complexity level."})]}),(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg border border-gray-200",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Proximal Search"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"If no exact match is found, RouKey searches adjacent complexity levels (\xb11) to find suitable models."})]}),(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg border border-gray-200",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Round-Robin Distribution"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Multiple keys at the same complexity level are used in round-robin fashion for load balancing."})]}),(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg border border-gray-200",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Fallback Protection"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"If no suitable complexity match is found, falls back to default general chat model."})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 p-6 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Cost Optimization Example"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Before RouKey"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"All requests go to GPT-4 ($0.03/1K tokens)"}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Simple Q&A: $0.03/1K tokens"}),(0,a.jsx)("li",{children:"• Basic explanations: $0.03/1K tokens"}),(0,a.jsx)("li",{children:"• Complex analysis: $0.03/1K tokens"}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Average cost: $0.03/1K tokens"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"With Complexity Routing"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"Intelligent model selection based on complexity"}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Simple Q&A: GPT o3 ($0.001/1K tokens)"}),(0,a.jsx)("li",{children:"• Basic explanations: GPT o3 ($0.001/1K tokens)"}),(0,a.jsx)("li",{children:"• Complex analysis: GPT-4 ($0.03/1K tokens)"}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Average cost: $0.012/1K tokens (60% savings)"})]})]})]})]})]}),(0,a.jsxs)(c,{type:"tip",children:[(0,a.jsx)("strong",{children:"Best Practice:"})," Configure cheaper models (GPT o3, Claude Haiku) for complexity levels 1-2, mid-tier models for level 3, and premium models (GPT-4, Claude Opus) for levels 4-5 to maximize cost savings."]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Cost-Optimized Routing"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-xl border border-purple-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(l.BZ,{className:"h-6 w-6 text-purple-600"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Learning-Based Optimization"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"RouKey's most advanced cost optimization strategy that learns from your usage patterns over time. It combines complexity analysis with user behavior learning to provide personalized cost optimization while maintaining response quality standards."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Learning Phases"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-yellow-50 rounded-lg border border-yellow-200",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Phase 1: Learning (First 50 requests)"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Conservative approach using complexity-based routing while collecting usage data and user feedback patterns."})]}),(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Phase 2: Optimization (51+ requests)"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Advanced routing using learned patterns, cost profiles, and quality preferences for maximum optimization."})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Optimization Factors"}),(0,a.jsxs)("ul",{className:"space-y-3 text-gray-600",children:[(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Usage Patterns:"})," Analyzes your typical request types and complexity distribution"]})]}),(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Cost Sensitivity:"})," Learns your cost vs. quality preferences from routing decisions"]})]}),(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Performance Metrics:"})," Tracks response quality and user satisfaction indicators"]})]}),(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Model Performance:"})," Monitors which models perform best for your specific use cases"]})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-6 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Advanced Features"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Adaptive Learning"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Continuously improves routing decisions based on outcomes"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Quality Monitoring"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Ensures cost optimization doesn't compromise response quality"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Budget Awareness"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Considers your budget constraints in routing decisions"})]})]})]}),(0,a.jsxs)(c,{type:"warning",children:[(0,a.jsx)("strong",{children:"Learning Period:"})," This strategy requires a learning period of 50+ requests to reach optimal performance. During the learning phase, it uses conservative complexity-based routing while building your usage profile."]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Strict Fallback Strategy"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-yellow-50 to-yellow-100 p-6 rounded-xl border border-yellow-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(l.yq,{className:"h-6 w-6 text-yellow-600"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Predictable Ordered Routing"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"The strict fallback strategy provides maximum reliability and predictability by defining an ordered list of API keys. RouKey will try them in sequence until one succeeds, making it perfect for mission-critical applications where predictable behavior is essential."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"How It Works"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center text-white font-bold text-sm",children:"1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Primary Model"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"First choice, highest priority"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center text-white font-bold text-sm",children:"2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Secondary Model"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Backup if primary fails"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center text-white font-bold text-sm",children:"3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Tertiary Model"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Final fallback option"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center text-white font-bold text-sm",children:"✗"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Error Response"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"If all models fail"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Configuration Example"}),(0,a.jsx)(d,{title:"Fallback order configuration",language:"json",children:'{\n  "routing_strategy": "strict_fallback",\n  "routing_strategy_params": {\n    "fallback_order": [\n      "primary-gpt4-key",\n      "backup-claude-key",\n      "emergency-gpt35-key"\n    ],\n    "retry_attempts": 3,\n    "timeout_seconds": 30\n  }\n}'})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Best Use Cases"}),(0,a.jsxs)("ul",{className:"space-y-3 text-gray-600",children:[(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)(l.Sr,{className:"h-5 w-5 text-green-600 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{children:"Mission-critical applications requiring guaranteed responses"})]}),(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)(l.Sr,{className:"h-5 w-5 text-green-600 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{children:"Applications with strict SLA requirements"})]}),(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)(l.Sr,{className:"h-5 w-5 text-green-600 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{children:"When you need predictable routing behavior"})]}),(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)(l.Sr,{className:"h-5 w-5 text-green-600 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{children:"Compliance requirements for specific model usage"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Advanced Features"}),(0,a.jsxs)("ul",{className:"space-y-3 text-gray-600",children:[(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-600 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Health Monitoring:"})," Automatically skips unhealthy models in the chain"]})]}),(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-600 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Retry Logic:"})," Configurable retry attempts for each model in the chain"]})]}),(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-600 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Timeout Control:"})," Individual timeout settings for each fallback level"]})]}),(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-600 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Detailed Logging:"})," Complete audit trail of fallback decisions"]})]})]})]})]}),(0,a.jsxs)(c,{type:"tip",children:[(0,a.jsx)("strong",{children:"Reliability:"})," This strategy provides the highest reliability with guaranteed fallback behavior. It's ideal for production environments where consistent availability is more important than cost optimization."]})]})]})]}),"configuration-dashboard-setup"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Dashboard Setup"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Get started with RouKey by setting up your dashboard and creating your first configuration."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Step-by-Step Setup"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm",children:"1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Create Your Account"}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Sign up at ",(0,a.jsx)("a",{href:"https://roukey.online",className:"text-[#ff6b35] hover:underline",children:"roukey.online"})," with your email address. Email verification is required."]})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm",children:"2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Access Your Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600",children:"After logging in, you'll see your main dashboard with options to create configurations, manage API keys, and view analytics."})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm",children:"3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Create Your First Configuration"}),(0,a.jsx)("p",{className:"text-gray-600",children:'Click "Create Configuration" to set up your first routing configuration. Give it a descriptive name like "Production API" or "Development Setup".'})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm",children:"4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Add Provider API Keys"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Add your LLM provider API keys (OpenAI, Anthropic, Google, etc.) to enable routing to different models."})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm",children:"5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Generate User API Key"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Create a user-generated API key that you'll use to make requests to RouKey from your applications."})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Dashboard Overview"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Configurations"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Manage your routing configurations, each with its own set of API keys and routing strategy."})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Analytics"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"View usage statistics, cost analysis, and performance metrics for your API calls."})]}),(0,a.jsxs)("div",{className:"p-4 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"User API Keys"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Generate and manage API keys for external access to your RouKey configurations."})]}),(0,a.jsxs)("div",{className:"p-4 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Custom Roles"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Create specialized roles for intelligent routing based on your specific use cases."})]})]})]})]}),"configuration-provider-keys"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Provider API Keys"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Learn how to add and manage API keys from different LLM providers in RouKey."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Supported Providers"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"OpenAI"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"GPT-4, GPT o3, GPT-4 Turbo"}),(0,a.jsx)("div",{className:"text-xs text-green-600 font-medium",children:"Most Popular"})]}),(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Anthropic"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"Claude 4 Opus, Claude 3 Haiku"}),(0,a.jsx)("div",{className:"text-xs text-blue-600 font-medium",children:"High Quality"})]}),(0,a.jsxs)("div",{className:"p-4 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Google"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"Gemini 2.5 Pro, Gemini Flash"}),(0,a.jsx)("div",{className:"text-xs text-purple-600 font-medium",children:"Cost Effective"})]}),(0,a.jsxs)("div",{className:"p-4 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"DeepSeek"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"Deepseek R1 0528, DeepSeek Chat"}),(0,a.jsx)("div",{className:"text-xs text-yellow-600 font-medium",children:"Coding Specialist"})]}),(0,a.jsxs)("div",{className:"p-4 bg-indigo-50 rounded-xl border border-indigo-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Mistral"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"Mistral Large, Mistral Medium"}),(0,a.jsx)("div",{className:"text-xs text-indigo-600 font-medium",children:"European"})]}),(0,a.jsxs)("div",{className:"p-4 bg-teal-50 rounded-xl border border-teal-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"xAI"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"Grok Models"}),(0,a.jsx)("div",{className:"text-xs text-teal-600 font-medium",children:"Advanced"})]}),(0,a.jsxs)("div",{className:"p-4 bg-red-50 rounded-xl border border-red-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"And More"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"300+ models supported"}),(0,a.jsx)("div",{className:"text-xs text-red-600 font-medium",children:"Growing"})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Adding API Keys"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-6 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Security First"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"All API keys are encrypted at rest using AES-256 encryption. RouKey never stores your keys in plain text."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Keys are encrypted before database storage"}),(0,a.jsx)("li",{children:"• Decryption only happens during API calls"}),(0,a.jsx)("li",{children:"• Keys are never logged or exposed in responses"}),(0,a.jsx)("li",{children:"• Regular security audits and compliance checks"})]})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-6 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Best Practices"}),(0,a.jsxs)("ul",{className:"text-gray-600 space-y-2",children:[(0,a.jsx)("li",{children:'• Use descriptive labels for your API keys (e.g., "OpenAI Production", "Claude Backup")'}),(0,a.jsx)("li",{children:"• Add multiple keys from the same provider for load balancing"}),(0,a.jsx)("li",{children:"• Set appropriate temperature settings for each key"}),(0,a.jsx)("li",{children:"• Regularly rotate your provider API keys for security"}),(0,a.jsx)("li",{children:"• Monitor usage through your provider dashboards"})]})]})]})]})]}),"configuration"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Configuration"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Learn how to configure RouKey for optimal performance with your specific use cases."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Dashboard Setup"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Get started with your RouKey dashboard and create your first configuration."}),(0,a.jsx)("div",{className:"text-[#ff6b35] font-medium",children:"→ Dashboard Setup Guide"})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Provider API Keys"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Add and manage API keys from different LLM providers."}),(0,a.jsx)("div",{className:"text-[#ff6b35] font-medium",children:"→ Provider Keys Guide"})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Routing Configuration"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Set up intelligent routing strategies for your use case."}),(0,a.jsx)("div",{className:"text-[#ff6b35] font-medium",children:"→ Routing Setup Guide"})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Custom Roles"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Create specialized roles for intelligent routing."}),(0,a.jsx)("div",{className:"text-[#ff6b35] font-medium",children:"→ Custom Roles Guide"})]})]})]}),"configuration-routing-config"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Routing Configuration"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Configure intelligent routing strategies to optimize cost, performance, and reliability."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Choosing a Routing Strategy"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Default Load Balancing (none)"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Perfect for getting started. Automatically distributes requests across all your API keys."}),(0,a.jsx)("div",{className:"text-sm text-blue-600 font-medium",children:"✓ Zero configuration • ✓ High reliability • ✓ Automatic failover"})]}),(0,a.jsxs)("div",{className:"p-6 bg-[#ff6b35]/10 rounded-xl border border-[#ff6b35]/20",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Intelligent Role Routing (intelligent_role)"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"AI classifies requests and routes to specialized models. Requires custom roles setup."}),(0,a.jsx)("div",{className:"text-sm text-[#ff6b35] font-medium",children:"✓ AI-powered • ✓ Specialized routing • ✓ Cost optimization"})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Complexity-Based Routing (complexity_round_robin)"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Routes simple requests to cheaper models, complex ones to premium models."}),(0,a.jsx)("div",{className:"text-sm text-green-600 font-medium",children:"✓ Cost optimization • ✓ Automatic complexity analysis • ✓ Smart routing"})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Configuration Steps"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm",children:"1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Select Your Configuration"}),(0,a.jsx)("p",{className:"text-gray-600",children:'Go to your configuration and click on "Routing Setup" to access routing options.'})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm",children:"2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Choose Strategy"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Select the routing strategy that best fits your use case and requirements."})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm",children:"3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Configure Parameters"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Set up strategy-specific parameters like role assignments or complexity thresholds."})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm",children:"4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Test & Monitor"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Test your configuration and monitor performance through the analytics dashboard."})]})]})]})]})]}),"configuration-custom-roles"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Custom Roles"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Create specialized roles to optimize routing for your specific use cases and requirements."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"What are Custom Roles?"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Custom roles allow you to define specialized routing behavior for different types of requests. When using intelligent role routing, RouKey's AI classifies incoming requests and routes them to the most appropriate model based on your custom role definitions."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Coding Role"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"Optimized for programming tasks"}),(0,a.jsx)("div",{className:"text-xs text-blue-600",children:"Primary: Claude 4 Opus • Fallback: GPT-4"})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Writing Role"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"Specialized for content creation"}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:"Primary: Claude 4 Opus • Fallback: GPT-4"})]}),(0,a.jsxs)("div",{className:"p-4 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Analysis Role"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"For data analysis and research"}),(0,a.jsx)("div",{className:"text-xs text-purple-600",children:"Primary: GPT-4 • Fallback: Claude 4 Opus"})]}),(0,a.jsxs)("div",{className:"p-4 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"General Chat"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"Default for unclassified requests"}),(0,a.jsx)("div",{className:"text-xs text-yellow-600",children:"Primary: Gemini 2.5 Pro • Fallback: GPT o3"})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Creating Custom Roles"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-green-50 p-6 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Role Configuration"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Each custom role requires:"}),(0,a.jsxs)("ul",{className:"text-gray-600 space-y-2",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Role Name:"}),' Descriptive identifier (e.g., "coding", "writing", "analysis")']}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Primary Model:"})," The preferred API key/model for this role"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Fallback Models:"})," Backup options if primary fails"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Description:"})," Clear description of when this role should be used"]})]})]}),(0,a.jsxs)(c,{type:"info",children:[(0,a.jsx)("strong",{children:"Tier Requirements:"})," Custom roles are available on Starter tier and above. Free tier users can only use the default load balancing strategy."]})]})]})]}),"configuration-temperature-settings"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Temperature Settings"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Fine-tune response creativity and consistency by configuring temperature settings for your API keys."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Understanding Temperature"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Temperature controls the randomness of the model's responses. Lower values make responses more focused and deterministic, while higher values increase creativity and variability."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"text-center p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:"0.0 - 0.3"}),(0,a.jsx)("div",{className:"font-semibold text-gray-900 mb-2",children:"Conservative"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Focused, deterministic responses. Best for factual questions, code generation, and analysis."})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-green-600 mb-2",children:"0.4 - 0.7"}),(0,a.jsx)("div",{className:"font-semibold text-gray-900 mb-2",children:"Balanced"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Good balance of creativity and consistency. Ideal for most general-purpose applications."})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-purple-600 mb-2",children:"0.8 - 2.0"}),(0,a.jsx)("div",{className:"font-semibold text-gray-900 mb-2",children:"Creative"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"High creativity and variability. Perfect for creative writing, brainstorming, and diverse outputs."})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Use Case Examples"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-xl",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Code Generation"}),(0,a.jsx)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm",children:"Temperature: 0.1"})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Low temperature ensures consistent, syntactically correct code with minimal randomness."})]}),(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-xl",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Customer Support"}),(0,a.jsx)("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded text-sm",children:"Temperature: 0.5"})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Balanced temperature provides helpful responses while maintaining consistency in tone."})]}),(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-xl",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Creative Writing"}),(0,a.jsx)("span",{className:"bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm",children:"Temperature: 0.9"})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Higher temperature encourages creative, diverse, and imaginative responses."})]})]})]})]}),"sdks"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"SDKs & Libraries"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Official SDKs and community libraries for RouKey."})]}),(0,a.jsxs)("div",{className:"card p-8 text-center",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Coming Soon"}),(0,a.jsx)("p",{className:"text-gray-600",children:"SDK documentation is being prepared."})]})]}),"limits"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Limits & Pricing"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Understanding RouKey's usage limits and pricing structure."})]}),(0,a.jsxs)("div",{className:"card p-8 text-center",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Coming Soon"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Limits and pricing documentation is being prepared."})]})]}),"future-releases-q1-2025"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Q1 2025 - Workflow Automation"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Advanced workflow automation capabilities for complex multi-step AI tasks and orchestration."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Planned Features"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Hierarchical Workflow Engine"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Advanced workflow orchestration similar to n8n, but specifically designed for AI tasks with memory and context preservation."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Visual workflow builder with drag-and-drop interface"}),(0,a.jsx)("li",{children:"• Memory persistence across workflow steps"}),(0,a.jsx)("li",{children:"• Conditional branching and loops"}),(0,a.jsx)("li",{children:"• Integration with external APIs and databases"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Multi-Agent Orchestration"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Coordinate multiple AI agents working together on complex tasks with role-based specialization."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Agent role definitions and capabilities"}),(0,a.jsx)("li",{children:"• Inter-agent communication protocols"}),(0,a.jsx)("li",{children:"• Task delegation and result aggregation"}),(0,a.jsx)("li",{children:"• Conflict resolution and consensus mechanisms"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Workflow Templates"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Pre-built workflow templates for common use cases like research, content creation, and data analysis."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Research and literature review workflows"}),(0,a.jsx)("li",{children:"• Content creation and editing pipelines"}),(0,a.jsx)("li",{children:"• Data analysis and reporting workflows"}),(0,a.jsx)("li",{children:"• Custom template creation and sharing"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Expected Benefits"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-4 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Productivity Boost"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Automate complex multi-step tasks that currently require manual coordination."})]}),(0,a.jsxs)("div",{className:"p-4 bg-red-50 rounded-xl border border-red-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Cost Efficiency"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Optimize model usage across workflow steps for maximum cost savings."})]})]})]})]}),"future-releases-q2-2025"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Q2 2025 - Performance & Scale"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Enhanced performance optimizations and enterprise-scale features for high-volume applications."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Performance Enhancements"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-green-50 rounded-xl border border-green-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Advanced Caching Layer"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Multi-tier caching system with semantic similarity matching and intelligent cache invalidation."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Semantic cache with vector similarity matching"}),(0,a.jsx)("li",{children:"• Distributed cache across multiple regions"}),(0,a.jsx)("li",{children:"• Smart cache warming and preloading"}),(0,a.jsx)("li",{children:"• Cache analytics and optimization recommendations"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Edge Computing"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Deploy RouKey routing logic closer to your users for reduced latency and improved performance."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Global edge network deployment"}),(0,a.jsx)("li",{children:"• Regional model routing optimization"}),(0,a.jsx)("li",{children:"• Latency-based provider selection"}),(0,a.jsx)("li",{children:"• Edge caching for frequently used responses"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Enterprise Scale Features"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-purple-50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Multi-Tenant Architecture"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Complete isolation and resource management for enterprise customers."})]}),(0,a.jsxs)("div",{className:"p-4 bg-yellow-50 rounded-xl border border-yellow-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Advanced Analytics"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Real-time performance monitoring and predictive analytics."})]}),(0,a.jsxs)("div",{className:"p-4 bg-red-50 rounded-xl border border-red-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Auto-Scaling"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Automatic resource scaling based on usage patterns and demand."})]})]})]})]}),"future-releases-q3-2025"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Q3 2025 - AI-Powered Features"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6 leading-relaxed",children:"Next-generation AI features including fine-tuning, custom model training, and advanced optimization."})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"AI Enhancement Features"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-[#ff6b35]/10 rounded-xl border border-[#ff6b35]/20",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Custom Model Fine-Tuning"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Fine-tune models on your specific data and use cases for improved performance and specialized capabilities."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Upload and process training datasets"}),(0,a.jsx)("li",{children:"• Automated fine-tuning pipeline"}),(0,a.jsx)("li",{children:"• Model performance evaluation and comparison"}),(0,a.jsx)("li",{children:"• Integration with existing routing strategies"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-indigo-50 rounded-xl border border-indigo-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Intelligent Model Selection"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"AI-powered model selection that learns from your usage patterns and automatically optimizes routing decisions."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Machine learning-based routing optimization"}),(0,a.jsx)("li",{children:"• Continuous learning from user feedback"}),(0,a.jsx)("li",{children:"• Predictive model performance scoring"}),(0,a.jsx)("li",{children:"• Automatic A/B testing of routing strategies"})]})]})]})]})]}),"future-releases-q4-2025"===e&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Q4 2025 - Enterprise Features"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Advanced enterprise features including on-premises deployment, advanced security, and compliance tools."})]}),(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Enterprise Deployment Options"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"On-Premises Deployment"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Deploy RouKey within your own infrastructure for maximum security and control."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Self-hosted RouKey instances"}),(0,a.jsx)("li",{children:"• Air-gapped deployment options"}),(0,a.jsx)("li",{children:"• Custom security configurations"}),(0,a.jsx)("li",{children:"• Integration with existing enterprise systems"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-blue-50 rounded-xl border border-blue-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Advanced Security & Compliance"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Enterprise-grade security features and compliance certifications for regulated industries."}),(0,a.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• SOC 2 Type II certification"}),(0,a.jsx)("li",{children:"• HIPAA compliance for healthcare"}),(0,a.jsx)("li",{children:"• GDPR compliance for EU operations"}),(0,a.jsx)("li",{children:"• Advanced audit logging and monitoring"})]})]})]})]})]}),"future-releases"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Future Releases"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6 leading-relaxed",children:"Discover what's coming next in RouKey's roadmap and planned features for upcoming releases."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Q1 2025 - Workflow Automation"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Advanced workflow automation and multi-agent orchestration capabilities."}),(0,a.jsx)("div",{className:"text-[#ff6b35] font-medium",children:"→ Q1 2025 Details"})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Q2 2025 - Performance & Scale"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Enhanced performance optimizations and enterprise-scale features."}),(0,a.jsx)("div",{className:"text-[#ff6b35] font-medium",children:"→ Q2 2025 Details"})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Q3 2025 - AI-Powered Features"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Custom model fine-tuning and intelligent optimization features."}),(0,a.jsx)("div",{className:"text-[#ff6b35] font-medium",children:"→ Q3 2025 Details"})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Q4 2025 - Enterprise Features"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"On-premises deployment and advanced enterprise security features."}),(0,a.jsx)("div",{className:"text-[#ff6b35] font-medium",children:"→ Q4 2025 Details"})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Q1 2025 - Enhanced Workflow Automation"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.P,{className:"h-5 w-5 text-blue-600"}),"Advanced Workflow Engine"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Full n8n-style workflow automation with visual workflow builder and advanced task orchestration."}),(0,a.jsx)("div",{className:"text-sm text-blue-600 font-medium",children:"Status: In Development"})]}),(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,a.jsx)(l.BZ,{className:"h-5 w-5 text-green-600"}),"Enhanced Memory System"]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Persistent memory across workflow executions with intelligent context management and sub-task tracking."}),(0,a.jsx)("div",{className:"text-sm text-green-600 font-medium",children:"Status: Design Phase"})]})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Q2 2025 - Performance & Scale"}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Ultra-Low Latency"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Target latency reduction to 50-100ms range for lightning-fast responses."}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Current: 100-500ms"})]}),(0,a.jsxs)("div",{className:"p-6 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Global Edge Network"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Worldwide edge deployment for reduced latency and improved reliability."}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Planned: 15+ regions"})]}),(0,a.jsxs)("div",{className:"p-6 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Advanced Caching"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Multi-layer caching with predictive pre-loading and intelligent invalidation."}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Target: 90% cache hit rate"})]})]})})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Q3 2025 - AI-Powered Features"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Intelligent Cost Prediction"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"AI-powered cost forecasting and budget optimization with predictive analytics."}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Monthly cost predictions"}),(0,a.jsx)("li",{children:"• Usage pattern analysis"}),(0,a.jsx)("li",{children:"• Automatic budget alerts"}),(0,a.jsx)("li",{children:"• Optimization recommendations"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Auto-Scaling Routing"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Dynamic routing strategies that adapt to traffic patterns and model performance in real-time."}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"• Traffic-based scaling"}),(0,a.jsx)("li",{children:"• Performance monitoring"}),(0,a.jsx)("li",{children:"• Automatic strategy switching"}),(0,a.jsx)("li",{children:"• Load balancing optimization"})]})]})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Q4 2025 - Enterprise Features"}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Advanced Analytics Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Comprehensive analytics with custom reporting, cost breakdowns, and performance insights."}),(0,a.jsxs)("ul",{className:"space-y-1 text-gray-600 text-sm",children:[(0,a.jsx)("li",{children:"• Custom dashboards"}),(0,a.jsx)("li",{children:"• Real-time monitoring"}),(0,a.jsx)("li",{children:"• Cost attribution"}),(0,a.jsx)("li",{children:"• Performance metrics"})]})]}),(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Enterprise Security"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Advanced security features including SSO, audit logs, and compliance certifications."}),(0,a.jsxs)("ul",{className:"space-y-1 text-gray-600 text-sm",children:[(0,a.jsx)("li",{children:"• Single Sign-On (SSO)"}),(0,a.jsx)("li",{children:"• Audit logging"}),(0,a.jsx)("li",{children:"• SOC 2 compliance"}),(0,a.jsx)("li",{children:"• Role-based access control"})]})]})]})})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-6 rounded-xl text-white",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Want to influence our roadmap?"}),(0,a.jsx)("p",{className:"text-white/90 mb-4",children:"We value feedback from our community. Share your feature requests and help shape RouKey's future."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors text-center",children:"Submit Feature Request"}),(0,a.jsx)("a",{href:"https://github.com/DRIM-ai/RouKey",target:"_blank",rel:"noopener noreferrer",className:"bg-white/20 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/30 transition-colors text-center",children:"View on GitHub"})]})]})]}),"faq"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Frequently Asked Questions"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6 leading-relaxed",children:"Find answers to common questions about RouKey's features, pricing, and implementation."})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"How does RouKey reduce API costs?"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"RouKey reduces costs through intelligent routing strategies that automatically select the most cost-effective model for each request. Our complexity-based routing can route simple tasks to cheaper models (like GPT o3) while reserving premium models (like GPT-4) for complex tasks. Additionally, our semantic caching system prevents duplicate API calls for similar requests, further reducing costs."})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Is RouKey compatible with existing OpenAI code?"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Yes! RouKey is fully compatible with OpenAI's API. You can use existing OpenAI SDKs and simply change the base URL to RouKey's endpoint. All OpenAI parameters are supported, plus RouKey adds additional parameters like \"role\" for enhanced routing capabilities."})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"What happens if a model is unavailable?"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"RouKey includes automatic failover mechanisms. If your primary model is unavailable, RouKey will automatically route to backup models based on your configured strategy. Our strict fallback strategy provides guaranteed failover chains, while other strategies include intelligent fallback logic to ensure high availability."})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"How secure are my API keys?"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Your API keys are encrypted using enterprise-grade AES-256 encryption and stored securely in our database. RouKey follows a BYOK (Bring Your Own Keys) model, meaning you maintain control of your API keys. We never store or log your actual API responses, and all communications are encrypted in transit."})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Can I use RouKey for production applications?"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Absolutely! RouKey is designed for production use with enterprise-grade reliability, security, and performance. We offer 99.9% uptime SLA, comprehensive monitoring, and 24/7 support for professional and enterprise plans. Many companies use RouKey to handle millions of requests per month."})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"What's the difference between routing strategies?"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed mb-4",children:"RouKey offers several routing strategies for different use cases:"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600 ml-4",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Intelligent Role:"})," AI classifies requests by role (coding, writing, etc.) and routes accordingly"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Complexity-Based:"})," Analyzes prompt complexity and routes to appropriate models for cost optimization"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Strict Fallback:"})," Ordered failover sequence for maximum reliability"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Cost-Optimized:"})," Learns your usage patterns and optimizes for cost while maintaining quality"]})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Do you support streaming responses?"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Yes! RouKey fully supports streaming responses and we actually recommend using streaming for complex multi-role tasks. Streaming helps avoid timeout issues on platforms like Vercel and provides a better user experience with real-time responses. Our multi-role orchestration system works seamlessly with streaming."})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"How does the free tier work?"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"The free tier includes unlimited API requests, 1 custom configuration, up to 3 API keys, access to all 300+ models, and strict fallback routing. However, it doesn't include advanced routing strategies, custom roles, or premium features. It's perfect for testing RouKey and small projects."})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Can I cancel my subscription anytime?"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Yes, you can cancel your subscription at any time. Your service will continue until the end of your current billing period, and you can always reactivate later. We also offer a 14-day money-back guarantee for new subscribers."})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"How do I get support?"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed mb-4",children:"We offer multiple support channels:"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600 ml-4",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Email:"})," <EMAIL> for general inquiries"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Technical:"})," <EMAIL> for technical support"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Documentation:"})," Comprehensive guides and examples"]})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-6 rounded-xl text-white",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Still have questions?"}),(0,a.jsx)("p",{className:"text-white/90 mb-4",children:"Can't find what you're looking for? Our team is here to help with any questions about RouKey."}),(0,a.jsx)("div",{className:"flex flex-col sm:flex-row gap-4",children:(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors text-center",children:"Contact Support"})})]})]})]},e)})})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[7125,5738,9968,6060,4755,563,2662,8669,4703,3269,9173,9219,7859,4288,7706,7544,1142,2993,1561,9248,6761,7358],()=>s(39660)),_N_E=e.O()}]);