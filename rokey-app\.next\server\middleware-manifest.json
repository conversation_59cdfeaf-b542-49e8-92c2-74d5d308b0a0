{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "G8nWFclPfsinXz5HNpLjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/n1rBof4iKYQGZTsbWUUh9wfp20szbskf7hOJtF1b60=", "__NEXT_PREVIEW_MODE_ID": "ba5fc01f69c6ba2067b70288bcc1e43f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84fe1ebea370d9db48608d57fdf26cf1f71a3d529cf2ce1f2606775ed2ee918e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b816bbd9b5243e52b4056bd6109285d41c03d091f78008ec37b9aae7344f70e8"}}}, "functions": {"/api/external/v1/configs/[configId]/keys/[keyId]/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/[configId]/keys/[keyId]/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/[configId]/keys/[keyId]/route.js"], "name": "app/api/external/v1/configs/[configId]/keys/[keyId]/route", "page": "/api/external/v1/configs/[configId]/keys/[keyId]/route", "matchers": [{"regexp": "^/api/external/v1/configs/(?<configId>[^/]+?)/keys/(?<keyId>[^/]+?)$", "originalSource": "/api/external/v1/configs/[configId]/keys/[keyId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "G8nWFclPfsinXz5HNpLjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/n1rBof4iKYQGZTsbWUUh9wfp20szbskf7hOJtF1b60=", "__NEXT_PREVIEW_MODE_ID": "ba5fc01f69c6ba2067b70288bcc1e43f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84fe1ebea370d9db48608d57fdf26cf1f71a3d529cf2ce1f2606775ed2ee918e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b816bbd9b5243e52b4056bd6109285d41c03d091f78008ec37b9aae7344f70e8"}}, "/api/external/v1/api-keys/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/api-keys/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/api-keys/route.js"], "name": "app/api/external/v1/api-keys/route", "page": "/api/external/v1/api-keys/route", "matchers": [{"regexp": "^/api/external/v1/api\\-keys$", "originalSource": "/api/external/v1/api-keys"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "G8nWFclPfsinXz5HNpLjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/n1rBof4iKYQGZTsbWUUh9wfp20szbskf7hOJtF1b60=", "__NEXT_PREVIEW_MODE_ID": "ba5fc01f69c6ba2067b70288bcc1e43f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84fe1ebea370d9db48608d57fdf26cf1f71a3d529cf2ce1f2606775ed2ee918e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b816bbd9b5243e52b4056bd6109285d41c03d091f78008ec37b9aae7344f70e8"}}, "/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/833.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "G8nWFclPfsinXz5HNpLjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/n1rBof4iKYQGZTsbWUUh9wfp20szbskf7hOJtF1b60=", "__NEXT_PREVIEW_MODE_ID": "ba5fc01f69c6ba2067b70288bcc1e43f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84fe1ebea370d9db48608d57fdf26cf1f71a3d529cf2ce1f2606775ed2ee918e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b816bbd9b5243e52b4056bd6109285d41c03d091f78008ec37b9aae7344f70e8"}}, "/api/external/v1/api-keys/[keyId]/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/api-keys/[keyId]/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/api-keys/[keyId]/route.js"], "name": "app/api/external/v1/api-keys/[keyId]/route", "page": "/api/external/v1/api-keys/[keyId]/route", "matchers": [{"regexp": "^/api/external/v1/api\\-keys/(?<keyId>[^/]+?)$", "originalSource": "/api/external/v1/api-keys/[keyId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "G8nWFclPfsinXz5HNpLjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/n1rBof4iKYQGZTsbWUUh9wfp20szbskf7hOJtF1b60=", "__NEXT_PREVIEW_MODE_ID": "ba5fc01f69c6ba2067b70288bcc1e43f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84fe1ebea370d9db48608d57fdf26cf1f71a3d529cf2ce1f2606775ed2ee918e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b816bbd9b5243e52b4056bd6109285d41c03d091f78008ec37b9aae7344f70e8"}}, "/api/external/v1/configs/[configId]/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/[configId]/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/[configId]/route.js"], "name": "app/api/external/v1/configs/[configId]/route", "page": "/api/external/v1/configs/[configId]/route", "matchers": [{"regexp": "^/api/external/v1/configs/(?<configId>[^/]+?)$", "originalSource": "/api/external/v1/configs/[configId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "G8nWFclPfsinXz5HNpLjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/n1rBof4iKYQGZTsbWUUh9wfp20szbskf7hOJtF1b60=", "__NEXT_PREVIEW_MODE_ID": "ba5fc01f69c6ba2067b70288bcc1e43f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84fe1ebea370d9db48608d57fdf26cf1f71a3d529cf2ce1f2606775ed2ee918e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b816bbd9b5243e52b4056bd6109285d41c03d091f78008ec37b9aae7344f70e8"}}, "/api/external/v1/models/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/models/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/models/route.js"], "name": "app/api/external/v1/models/route", "page": "/api/external/v1/models/route", "matchers": [{"regexp": "^/api/external/v1/models$", "originalSource": "/api/external/v1/models"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "G8nWFclPfsinXz5HNpLjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/n1rBof4iKYQGZTsbWUUh9wfp20szbskf7hOJtF1b60=", "__NEXT_PREVIEW_MODE_ID": "ba5fc01f69c6ba2067b70288bcc1e43f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84fe1ebea370d9db48608d57fdf26cf1f71a3d529cf2ce1f2606775ed2ee918e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b816bbd9b5243e52b4056bd6109285d41c03d091f78008ec37b9aae7344f70e8"}}, "/api/external/v1/docs/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/docs/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/app/api/external/v1/docs/route.js"], "name": "app/api/external/v1/docs/route", "page": "/api/external/v1/docs/route", "matchers": [{"regexp": "^/api/external/v1/docs$", "originalSource": "/api/external/v1/docs"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "G8nWFclPfsinXz5HNpLjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/n1rBof4iKYQGZTsbWUUh9wfp20szbskf7hOJtF1b60=", "__NEXT_PREVIEW_MODE_ID": "ba5fc01f69c6ba2067b70288bcc1e43f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84fe1ebea370d9db48608d57fdf26cf1f71a3d529cf2ce1f2606775ed2ee918e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b816bbd9b5243e52b4056bd6109285d41c03d091f78008ec37b9aae7344f70e8"}}, "/api/external/v1/providers/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/providers/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/833.js", "server/app/api/external/v1/providers/route.js"], "name": "app/api/external/v1/providers/route", "page": "/api/external/v1/providers/route", "matchers": [{"regexp": "^/api/external/v1/providers$", "originalSource": "/api/external/v1/providers"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "G8nWFclPfsinXz5HNpLjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/n1rBof4iKYQGZTsbWUUh9wfp20szbskf7hOJtF1b60=", "__NEXT_PREVIEW_MODE_ID": "ba5fc01f69c6ba2067b70288bcc1e43f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84fe1ebea370d9db48608d57fdf26cf1f71a3d529cf2ce1f2606775ed2ee918e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b816bbd9b5243e52b4056bd6109285d41c03d091f78008ec37b9aae7344f70e8"}}, "/api/external/v1/usage/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/usage/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/usage/route.js"], "name": "app/api/external/v1/usage/route", "page": "/api/external/v1/usage/route", "matchers": [{"regexp": "^/api/external/v1/usage$", "originalSource": "/api/external/v1/usage"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "G8nWFclPfsinXz5HNpLjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/n1rBof4iKYQGZTsbWUUh9wfp20szbskf7hOJtF1b60=", "__NEXT_PREVIEW_MODE_ID": "ba5fc01f69c6ba2067b70288bcc1e43f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84fe1ebea370d9db48608d57fdf26cf1f71a3d529cf2ce1f2606775ed2ee918e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b816bbd9b5243e52b4056bd6109285d41c03d091f78008ec37b9aae7344f70e8"}}, "/api/external/v1/configs/[configId]/routing/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/[configId]/routing/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/[configId]/routing/route.js"], "name": "app/api/external/v1/configs/[configId]/routing/route", "page": "/api/external/v1/configs/[configId]/routing/route", "matchers": [{"regexp": "^/api/external/v1/configs/(?<configId>[^/]+?)/routing$", "originalSource": "/api/external/v1/configs/[configId]/routing"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "G8nWFclPfsinXz5HNpLjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/n1rBof4iKYQGZTsbWUUh9wfp20szbskf7hOJtF1b60=", "__NEXT_PREVIEW_MODE_ID": "ba5fc01f69c6ba2067b70288bcc1e43f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84fe1ebea370d9db48608d57fdf26cf1f71a3d529cf2ce1f2606775ed2ee918e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b816bbd9b5243e52b4056bd6109285d41c03d091f78008ec37b9aae7344f70e8"}}, "/api/external/v1/configs/[configId]/keys/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/[configId]/keys/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/[configId]/keys/route.js"], "name": "app/api/external/v1/configs/[configId]/keys/route", "page": "/api/external/v1/configs/[configId]/keys/route", "matchers": [{"regexp": "^/api/external/v1/configs/(?<configId>[^/]+?)/keys$", "originalSource": "/api/external/v1/configs/[configId]/keys"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "G8nWFclPfsinXz5HNpLjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/n1rBof4iKYQGZTsbWUUh9wfp20szbskf7hOJtF1b60=", "__NEXT_PREVIEW_MODE_ID": "ba5fc01f69c6ba2067b70288bcc1e43f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84fe1ebea370d9db48608d57fdf26cf1f71a3d529cf2ce1f2606775ed2ee918e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b816bbd9b5243e52b4056bd6109285d41c03d091f78008ec37b9aae7344f70e8"}}, "/api/external/v1/configs/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/route.js"], "name": "app/api/external/v1/configs/route", "page": "/api/external/v1/configs/route", "matchers": [{"regexp": "^/api/external/v1/configs$", "originalSource": "/api/external/v1/configs"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "G8nWFclPfsinXz5HNpLjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/n1rBof4iKYQGZTsbWUUh9wfp20szbskf7hOJtF1b60=", "__NEXT_PREVIEW_MODE_ID": "ba5fc01f69c6ba2067b70288bcc1e43f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84fe1ebea370d9db48608d57fdf26cf1f71a3d529cf2ce1f2606775ed2ee918e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b816bbd9b5243e52b4056bd6109285d41c03d091f78008ec37b9aae7344f70e8"}}}, "sortedMiddleware": ["/"]}