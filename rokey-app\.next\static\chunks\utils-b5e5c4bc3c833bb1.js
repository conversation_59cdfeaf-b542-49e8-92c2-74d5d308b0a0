"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5738],{13744:(e,t,a)=>{a.d(t,{Lz:()=>i});var r=a(49509);let n=!!("localhost"===window.location.hostname||"[::1]"===window.location.hostname||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));function i(e){if(n)return void("serviceWorker"in navigator&&navigator.serviceWorker.ready.then(e=>{e.unregister()}).catch(e=>{}));if("serviceWorker"in navigator){if(new URL(r.env.PUBLIC_URL||"",window.location.href).origin!==window.location.origin)return;window.addEventListener("load",()=>{(function(e,t){navigator.serviceWorker.register(e).then(e=>{e.onupdatefound=()=>{let a=e.installing;null!=a&&(a.onstatechange=()=>{"installed"===a.state&&(navigator.serviceWorker.controller?t&&t.onUpdate&&t.onUpdate(e):t&&t.onSuccess&&t.onSuccess(e))})}}).catch(e=>{t&&t.onError&&t.onError(e)})})("/sw.js",e)})}}},21826:(e,t,a)=>{a.d(t,{zf:()=>o});var r=a(86973);let n={static:{ttl:864e5,priority:"high",tags:["static"]},user:{ttl:12e4,priority:"high",tags:["user"]},system:{ttl:3e4,priority:"low",tags:["system"]},pricing:{ttl:36e5,priority:"medium",tags:["pricing"]}},i={LANDING_FEATURES:"landing:features",PRICING_TIERS:"pricing:tiers",PRICING_COMPARISON:"pricing:comparison",SYSTEM_STATUS:"system:status",SYSTEM_MODELS:"system:models",USER_CONFIGS:"user:configs",USER_ANALYTICS:"user:analytics"};class s{static getInstance(){return s.instance||(s.instance=new s),s.instance}trackNavigation(e,t){let a="".concat(e,"->").concat(t),r=this.userBehavior.get(a)||0;this.userBehavior.set(a,r+1),r>2&&this.schedulePrefetch(t)}schedulePrefetch(e){this.prefetchQueue.has(e)||(this.prefetchQueue.add(e),this.processPrefetchQueue())}async processPrefetchQueue(){if(!this.isProcessing&&0!==this.prefetchQueue.size){for(let e of(this.isProcessing=!0,this.prefetchQueue)){try{await this.prefetchRoute(e),this.prefetchQueue.delete(e)}catch(e){}await new Promise(e=>setTimeout(e,100))}this.isProcessing=!1}}async prefetchRoute(e){let t={"/dashboard":()=>this.prefetchDashboardData(),"/pricing":()=>this.prefetchPricingData(),"/auth/signup":()=>this.prefetchAuthData(),"/features":()=>this.prefetchFeaturesData()}[e];t&&await t()}async prefetchDashboardData(){let e=[this.cacheIfNotExists(i.USER_CONFIGS,"/api/custom-configs",n.user),this.cacheIfNotExists(i.USER_ANALYTICS,"/api/analytics",n.user),this.cacheIfNotExists(i.SYSTEM_STATUS,"/api/system-status",n.system)];await Promise.allSettled(e)}async prefetchPricingData(){let e=[this.cacheIfNotExists(i.PRICING_TIERS,"/api/pricing/tiers",n.pricing),this.cacheIfNotExists(i.PRICING_COMPARISON,"/api/pricing/comparison",n.pricing)];await Promise.allSettled(e)}async prefetchAuthData(){let e=[this.cacheIfNotExists(i.PRICING_TIERS,"/api/pricing/tiers",n.pricing)];await Promise.allSettled(e)}async prefetchFeaturesData(){let e=[this.cacheIfNotExists(i.LANDING_FEATURES,"/api/features",n.static),this.cacheIfNotExists(i.SYSTEM_MODELS,"/api/models",n.static)];await Promise.allSettled(e)}async cacheIfNotExists(e,t,a){let n=r.globalCache.get(e);if(n)return n;try{let n=await fetch(t);if(n.ok){let t=await n.json();return r.globalCache.set(e,t,a),t}}catch(e){}}constructor(){this.prefetchQueue=new Set,this.isProcessing=!1,this.userBehavior=new Map}}let o=s.getInstance()},24403:(e,t,a)=>{a.d(t,{l2:()=>c,mx:()=>l});var r=a(12115);let n=new Map,i={hits:0,misses:0},s=new Set,o=!1,l=e=>{let{configId:t,enablePrefetch:a=!0,cacheTimeout:l=3e5,staleTimeout:c=3e4}=e,[u,d]=(0,r.useState)([]),[m,g]=(0,r.useState)(!1),[h,p]=(0,r.useState)(!1),[f,y]=(0,r.useState)(null),w=(0,r.useRef)(null),v=(0,r.useRef)(null),b=(0,r.useCallback)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];arguments.length>2&&void 0!==arguments[2]&&arguments[2];let r=n.get(e),o=Date.now();if(!t&&r&&o-r.timestamp<l)return i.hits++,o-r.timestamp>c&&!r.isStale&&(r.isStale=!0,a&&(s.add(e),E())),r.data;i.misses++,w.current&&w.current.abort(),w.current=new AbortController;try{let t=e.startsWith("workflow_"),a=t?e.replace("workflow_",""):e,r="/api/chat/conversations?".concat(t?"workflow_id":"custom_api_config_id","=").concat(a),i=await fetch(r,{signal:w.current.signal,headers:{"Cache-Control":"no-cache","X-Requested-With":"XMLHttpRequest"}});if(!i.ok)throw Error("Failed to fetch chat history: ".concat(i.status," ").concat(i.statusText));let s=await i.json();return n.set(e,{data:s,timestamp:o,isStale:!1}),s}catch(e){if("AbortError"===e.name)throw e;if(r&&r.data.length>0)return r.data;throw e}},[l,c,a]),E=(0,r.useCallback)(async()=>{if(!o&&0!==s.size){o=!0;try{let e=Array.from(s);for(let t of(s.clear(),e))try{await b(t,!0,!0),await new Promise(e=>setTimeout(e,100))}catch(e){}}finally{o=!1}}},[b]),k=(0,r.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!t)return;let a=n.get(t);!e&&a&&a.data.length>0&&(d(a.data),p(a.isStale),y(null)),g(!0),v.current=t;try{let a=await b(t,e);v.current===t&&(d(a),p(!1),y(null))}catch(e){"AbortError"!==e.name&&v.current===t&&y("Failed to load chat history: ".concat(e.message))}finally{v.current===t&&g(!1)}},[t,b]),S=(0,r.useCallback)(async e=>{a&&(s.add(e),E())},[a,E]),I=(0,r.useCallback)(e=>{e?n.delete(e):n.clear()},[]),C=(0,r.useCallback)(()=>({size:n.size,hits:i.hits,misses:i.misses}),[]);return(0,r.useEffect)(()=>{t?k():(d([]),g(!1),y(null),p(!1))},[t,k]),(0,r.useEffect)(()=>()=>{w.current&&w.current.abort()},[]),{chatHistory:u,isLoading:m,isStale:h,error:f,refetch:k,prefetch:S,invalidateCache:I,getCacheStats:C}},c=()=>{let e=(0,r.useRef)(new Set);return{prefetchChatHistory:(0,r.useCallback)(async t=>{e.current.has(t)||(e.current.add(t),s.add(t),setTimeout(()=>{s.size>0&&(async()=>{if(!o){o=!0;try{let e=Array.from(s);for(let t of(s.clear(),e)){try{let e="/api/chat/conversations?custom_api_config_id=".concat(t),a=await fetch(e,{headers:{"X-Prefetch":"true"}});if(a.ok){let e=await a.json();n.set(t,{data:e,timestamp:Date.now(),isStale:!1})}}catch(e){}await new Promise(e=>setTimeout(e,100))}}finally{o=!1}}})()},200))},[])}}},28003:(e,t,a)=>{a.d(t,{_:()=>i});var r=a(12115);let n={};function i(){let[e,t]=(0,r.useState)({}),a=(0,r.useRef)({}),i=(0,r.useCallback)(e=>{let t=n[e];return!!t&&!(Date.now()-t.timestamp>3e5)&&!t.isLoading},[]),s=(0,r.useCallback)(e=>{let t=n[e];return!t||t.isLoading?null:Date.now()-t.timestamp>3e5?(delete n[e],null):t.data},[]),o=(0,r.useCallback)(async function(e){var r;let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"medium";if(i(e))return s(e);if(null==(r=n[e])?void 0:r.isLoading)return null;a.current[e]&&a.current[e].abort();let l=new AbortController;a.current[e]=l,n[e]={data:{},timestamp:Date.now(),isLoading:!0},t(t=>({...t,[e]:"loading"}));try{"low"===o?await new Promise(e=>setTimeout(e,200)):"medium"===o&&await new Promise(e=>setTimeout(e,50));let[a,r,i,s,c]=await Promise.allSettled([fetch("/api/custom-configs",{signal:l.signal}),fetch("/api/keys?custom_config_id=".concat(e),{signal:l.signal}),fetch("/api/user/custom-roles",{signal:l.signal}),fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({}),signal:l.signal}),fetch("/api/custom-configs/".concat(e,"/default-chat-key"),{signal:l.signal})]),u=null,d=[],m=[],g=[],h=null;if("fulfilled"===a.status&&a.value.ok&&(u=(await a.value.json()).find(t=>t.id===e)),"fulfilled"===r.status&&r.value.ok&&(d=await r.value.json()),"fulfilled"===i.status&&i.value.ok&&(m=await i.value.json()),"fulfilled"===s.status&&s.value.ok&&(g=(await s.value.json()).models||[]),"fulfilled"===c.status&&c.value.ok){let e=await c.value.json();h=(null==e?void 0:e.id)||null}let p={configDetails:u,apiKeys:d,userCustomRoles:m,models:g,defaultChatKeyId:h};return n[e]={data:p,timestamp:Date.now(),isLoading:!1},t(t=>({...t,[e]:"success"})),p}catch(a){if("AbortError"===a.name)return null;return delete n[e],t(t=>({...t,[e]:"error"})),null}finally{delete a.current[e]}},[i,s]),l=(0,r.useCallback)(e=>({onMouseEnter:()=>{i(e)||o(e,"high")}}),[o,i]),c=(0,r.useCallback)(e=>{delete n[e],t(t=>{let a={...t};return delete a[e],a})},[]),u=(0,r.useCallback)(()=>{Object.keys(n).forEach(e=>{delete n[e]}),t({})},[]);return{prefetchManageKeysData:o,getCachedData:s,isCached:i,createHoverPrefetch:l,clearCache:c,clearAllCache:u,getStatus:(0,r.useCallback)(t=>e[t]||"idle",[e]),getCacheInfo:(0,r.useCallback)(()=>({cachedConfigs:Object.keys(n),cacheSize:Object.keys(n).length,totalCacheAge:Object.values(n).reduce((e,t)=>e+(Date.now()-t.timestamp),0)/Object.keys(n).length}),[]),prefetchStatus:e}}},37843:(e,t,a)=>{a.d(t,{C:()=>o,e:()=>l});var r=a(35695),n=a(12115);class i{setRouter(e){this.router=e}async prefetchRoute(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.router)return;let{priority:a="low",delay:r=0,condition:n}=t;if(n&&!n())return;let i=this.prefetchedRoutes.get(e);i&&i.prefetched&&Date.now()-i.timestamp<3e5||(this.prefetchQueue.push({route:e,options:t}),this.prefetchedRoutes.set(e,{route:e,timestamp:Date.now(),prefetched:!1}),this.processQueue())}async processQueue(){if(!this.isProcessing&&0!==this.prefetchQueue.length){for(this.isProcessing=!0,this.prefetchQueue.sort((e,t)=>{let a={high:0,low:1};return a[e.options.priority||"low"]-a[t.options.priority||"low"]});this.prefetchQueue.length>0;){let{route:e,options:t}=this.prefetchQueue.shift();try{if(t.delay&&t.delay>0&&await new Promise(e=>setTimeout(e,t.delay)),t.condition&&!t.condition())continue;await this.router.prefetch(e),await this.prefetchBundles(e);let a=this.prefetchedRoutes.get(e);a&&(a.prefetched=!0,a.timestamp=Date.now()),await new Promise(e=>setTimeout(e,50))}catch(e){}}this.isProcessing=!1}}async prefetchBundles(e){try{["https://fonts.googleapis.com","https://fonts.gstatic.com"].forEach(e=>{if(document.querySelector('link[href="'.concat(e,'"][rel="preconnect"]')))return;let t=document.createElement("link");t.rel="preconnect",t.href=e,t.crossOrigin="anonymous",document.head.appendChild(t)})}catch(e){}}cleanup(){let e=Date.now();for(let[t,a]of this.prefetchedRoutes.entries())e-a.timestamp>6e5&&this.prefetchedRoutes.delete(t)}constructor(){this.prefetchedRoutes=new Map,this.router=null,this.prefetchQueue=[],this.isProcessing=!1}}let s=new i,o=()=>{let e=(0,r.useRouter)(),t=(0,n.useRef)();(0,n.useEffect)(()=>(s.setRouter(e),t.current=setInterval(()=>{s.cleanup()},3e5),()=>{t.current&&clearInterval(t.current)}),[e]);let a=(0,n.useCallback)((e,t)=>{s.prefetchRoute(e,t)},[]),i=(0,n.useCallback)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;return{onMouseEnter:()=>{a(e,{priority:"high",delay:t})}}},[a]),o=(0,n.useCallback)((e,t)=>{if(!t)return;let r=new IntersectionObserver(t=>{t.forEach(t=>{t.isIntersecting&&(a(e,{priority:"low",delay:200}),r.disconnect())})},{threshold:.1});return r.observe(t),()=>r.disconnect()},[a]);return{prefetchRoute:a,prefetchOnHover:i,prefetchOnVisible:o}},l=()=>{let{prefetchRoute:e}=o(),t=(0,n.useRef)({lastActivity:Date.now(),isIdle:!1,mouseMovements:0});return(0,n.useEffect)(()=>{let e,a,r=()=>{t.current.lastActivity=Date.now(),t.current.isIdle=!1,clearTimeout(e),e=setTimeout(()=>{t.current.isIdle=!0},3e3)},n=()=>{t.current.mouseMovements++,r()},i=()=>{r()};return document.addEventListener("mousemove",n),document.addEventListener("keypress",i),document.addEventListener("click",r),document.addEventListener("scroll",r),a=setInterval(()=>{t.current.mouseMovements=0},1e4),()=>{document.removeEventListener("mousemove",n),document.removeEventListener("keypress",i),document.removeEventListener("click",r),document.removeEventListener("scroll",r),clearTimeout(e),clearInterval(a)}},[]),{prefetchWhenIdle:(0,n.useCallback)(a=>{let r=setInterval(()=>{t.current.isIdle&&t.current.mouseMovements<5&&a.forEach((a,r)=>{e(a,{priority:"low",delay:500*r,condition:()=>t.current.isIdle})})},2e3);return()=>clearInterval(r)},[e]),isUserIdle:()=>t.current.isIdle}}},38456:(e,t,a)=>{a.d(t,{Il:()=>d,Rf:()=>c,aU:()=>u,gI:()=>n});let r=e=>![/default_key/i,/attempt_\d+/i,/status_\d+/i,/failed/i,/success/i,/complexity_rr/i,/fallback_position/i,/^[a-f0-9-]{8,}/i,/_then_/i,/classification_/i,/no_prompt/i,/error/i].some(t=>t.test(e))&&/^[a-z_]+$/i.test(e)&&e.length>2&&e.length<50,n=e=>{if(!e)return{text:"N/A",type:"fallback"};if(r(e))return{text:l(e),type:"role",details:"Role-based routing: ".concat(l(e))};if(e.includes("default_key")&&e.includes("success")){let t=e.match(/attempt_(\d+)/),a=t?parseInt(t[1]):1;return{text:1===a?"Default Key":"Default Key (Attempt ".concat(a,")"),type:"success",details:a>1?"Required ".concat(a," attempts to succeed"):void 0}}if(e.includes("default_key")&&e.includes("failed")){let t=e.match(/attempt_(\d+)/),a=e.match(/status_(\w+)/),r=t?parseInt(t[1]):1,n=a?a[1]:"unknown";return{text:"Failed (Attempt ".concat(r,")"),type:"error",details:"Failed with status: ".concat(n)}}if(e.includes("default_all")&&e.includes("attempts_failed")){let t=e.match(/default_all_(\d+)_attempts/),a=t?parseInt(t[1]):0;return{text:"All Keys Failed (".concat(a," attempts)"),type:"error",details:"Tried ".concat(a," different API keys, all failed")}}if(e.includes("complexity_rr_clsf_")||e.includes("complexity_level_")){let t=e.match(/complexity_rr_clsf_(\d+)_used_lvl_(\d+)/);if(t){let e=t[1],a=t[2];return e===a?{text:"Complexity Level ".concat(a),type:"success",details:"Classified and routed to complexity level ".concat(a)}:{text:"Complexity ".concat(e,"→").concat(a),type:"success",details:"Classified as level ".concat(e,", routed to available level ").concat(a)}}let a=e.match(/complexity_level_(\d+)/);if(a){let e=a[1];return{text:"Complexity Level ".concat(e),type:"success",details:"Routed based on prompt complexity analysis"}}}if(e.includes("fallback_position_")){let t=e.match(/fallback_position_(\d+)/),a=t?parseInt(t[1]):0;return{text:"Fallback Key #".concat(a+1),type:"success",details:"Used fallback key at position ".concat(a+1)}}if(e.includes("intelligent_role_")){let t=e.match(/intelligent_role_(.+)$/),a=t?t[1]:"unknown";return{text:"Smart: ".concat(l(a)),type:"role",details:"AI detected role: ".concat(l(a))}}return i(e)},i=e=>{let t=e.match(/complexity[_\s]*(\d+)/i);if(t){let e=t[1];return{text:"Complexity Level ".concat(e),type:"success",details:"Extracted complexity level ".concat(e," from routing pattern")}}let a=s(e);if(a)return{text:l(a),type:"role",details:"Extracted role: ".concat(l(a))};let r=e.match(/fallback[_\s]*(\d+)/i);if(r){let e=parseInt(r[1]);return{text:"Fallback Key #".concat(e+1),type:"success",details:"Extracted fallback position ".concat(e+1)}}let n=e.match(/attempt[_\s]*(\d+)/i);if(n){let t=parseInt(n[1]),a=e.toLowerCase().includes("success"),r=e.toLowerCase().includes("fail");if(a)return{text:1===t?"Default Key":"Default Key (Attempt ".concat(t,")"),type:"success",details:"Extracted success on attempt ".concat(t)};if(r)return{text:"Failed (Attempt ".concat(t,")"),type:"error",details:"Extracted failure on attempt ".concat(t)}}return{text:o(e),type:"fallback",details:"Raw routing pattern: ".concat(e)}},s=e=>{let t=e.match(/classified_as_([a-z_]+)_/i);if(t&&r(t[1]))return t[1];let a=e.match(/role_([a-z_]+)_/i);if(a&&r(a[1]))return a[1];let n=e.match(/fb_role_([a-z_]+)/i);return n&&r(n[1])?n[1]:null},o=e=>{let t=e.replace(/^default_key_[a-f0-9-]+_/i,"").replace(/_attempt_\d+$/i,"").replace(/_status_\w+$/i,"").replace(/_key_selected$/i,"").replace(/_then_.*$/i,"").replace(/^complexity_rr_/i,"").replace(/_no_.*$/i,"");return t&&t.length>2&&t.length<30&&r(t)?l(t):e.replace(/_/g," ").replace(/([a-z])([A-Z])/g,"$1 $2").split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" ").substring(0,30)+(e.length>30?"...":"")},l=e=>e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),c=e=>{switch(e){case"role":return"inline-block px-2 py-1 rounded text-xs font-medium bg-blue-500/15 text-blue-300 border border-blue-500/25";case"success":return"inline-block px-2 py-1 rounded text-xs font-medium bg-green-500/15 text-green-300 border border-green-500/25";case"error":return"inline-block px-2 py-1 rounded text-xs font-medium bg-red-500/15 text-red-300 border border-red-500/25";default:return"inline-block px-2 py-1 rounded text-xs font-medium bg-orange-500/15 text-orange-300 border border-orange-500/25"}},u=e=>e?({openai:"OpenAI",anthropic:"Anthropic",google:"Google",openrouter:"OpenRouter",deepseek:"DeepSeek",xai:"xAI",langgraph:"RouKey Orchestration",langgraph_orchestration:"RouKey Orchestration",hybrid_orchestration:"RouKey Orchestration",roukey:"RouKey Orchestration"})[e.toLowerCase()]||e:"N/A",d=e=>e?e.replace(/^(gpt-|claude-|gemini-|meta-llama\/|deepseek-|grok-)/,"").replace(/-/g," ").replace(/\b\w/g,e=>e.toUpperCase()):"N/A"},41e3:(e,t,a)=>{a.d(t,{n4:()=>s,w6:()=>i});var r=a(12115);let n={initializing:50,analyzing:150,routing:200,complexity_analysis:250,role_classification:300,preparing:150,connecting:200,generating:400,typing:0,finalizing:100,complete:0};function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{enableAutoProgression:t=!0,stageDurations:a={},onStageChange:i}=e,[s,o]=(0,r.useState)("initializing"),[l,c]=(0,r.useState)(!1),[u,d]=(0,r.useState)([]),m=(0,r.useRef)(0),g=(0,r.useRef)([]);({...n,...a});let h=(0,r.useCallback)(()=>{g.current.forEach(e=>clearTimeout(e)),g.current=[]},[]),p=(0,r.useCallback)(function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],a=Date.now();if("connecting"===e){o(e),d(t=>[...t,{stage:e,timestamp:a}]),null==i||i(e,a);let t=setTimeout(()=>{o("routing"),d(e=>[...e,{stage:"routing",timestamp:Date.now()}]),null==i||i("routing",Date.now())},2e3);g.current.push(t);return}t&&h(),o(e),d(t=>[...t,{stage:e,timestamp:a}]),null==i||i(e,a)},[i,h]),f=(0,r.useCallback)(()=>{let e;c(!0),m.current=Date.now(),d([{stage:"initializing",timestamp:Date.now()}]),o("initializing");let t=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30,a=(Math.random()-.5)*2*(t/100*e);return Math.max(200,Math.round(e+a))},a=setTimeout(()=>{p("analyzing",!1)},e=0+t(900,35)),r=setTimeout(()=>{p("complexity_analysis",!1)},e+=t(1200,40)),n=setTimeout(()=>{p("role_classification",!1)},e+=t(1500,35)),i=setTimeout(()=>{p("preparing",!1)},e+=t(1e3,40)),s=setTimeout(()=>{p("connecting",!1)},e+=t(1200,35)),l=setTimeout(()=>{p("routing",!1)},e+=t(1500,40)),u=setTimeout(()=>{p("generating",!1)},e+=t(1200,35));g.current.push(a,r,n,i,s,l,u)},[i,p]),y=(0,r.useCallback)(()=>{h();let e=Date.now();o("typing"),d(t=>[...t,{stage:"typing",timestamp:e}]),null==i||i("typing",e)},[h,i]),w=(0,r.useCallback)(()=>{h(),p("complete"),c(!1)},[h,p]),v=(0,r.useCallback)(()=>{h();let e=Date.now();o("generating"),d(t=>[...t,{stage:"generating",timestamp:e}]),null==i||i("generating",e)},[h,i]),b=(0,r.useCallback)(e=>{},[]),E=(0,r.useCallback)(()=>{h(),o("initializing"),c(!1),d([]),m.current=0},[h]),k=(0,r.useCallback)(()=>0===m.current?0:Date.now()-m.current,[]);return(0,r.useEffect)(()=>()=>{h()},[h]),{currentStage:s,isActive:l,stageHistory:u,startProcessing:f,updateStage:p,markStreaming:y,markComplete:w,markOrchestrationStarted:v,updateOrchestrationStatus:b,reset:E,getProcessingDuration:k}}(e),[a,i]=(0,r.useState)(new Set),s=(0,r.useCallback)(e=>{e.get("x-rokey-role-used"),e.get("x-rokey-routing-strategy"),e.get("x-rokey-complexity-level"),e.get("x-rokey-api-key-provider"),t.updateStage("generating")},[t]),o=(0,r.useCallback)(e=>{e.includes("[Complexity Classification]")&&!a.has("complexity")&&(t.updateStage("complexity_analysis"),i(e=>new Set([...e,"complexity"]))),e.includes("[Intelligent Role Strategy]")&&!a.has("role")&&(t.updateStage("role_classification"),i(e=>new Set([...e,"role"]))),e.includes("FIRST TOKEN:")&&!a.has("streaming")&&(t.markStreaming(),i(e=>new Set([...e,"streaming"])))},[t,a]),l=(0,r.useCallback)(()=>{t.reset(),i(new Set)},[t]);return{...t,analyzeResponseHeaders:s,analyzeStreamChunk:o,reset:l,detectedStages:Array.from(a)}}function s(e){if(!(e.length<2)){for(let t=1;t<e.length;t++){let a=e[t],r=e[t-1];a.timestamp,r.timestamp}e[e.length-1].timestamp,e[0].timestamp}}},42126:(e,t,a)=>{a.d(t,{v:()=>l});var r=a(12115),n=a(35695),i=a(42724),s=a(37843);let o={maxConcurrent:3,idleTimeout:2e3,hoverDelay:100,backgroundDelay:5e3};function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,n.usePathname)();(0,n.useRouter)();let{predictions:a,isLearning:l}=(0,i.x)(),{prefetchRoute:c}=(0,s.C)(),u={...o,...e},d=(0,r.useRef)([]),m=(0,r.useRef)(new Set),g=(0,r.useRef)(null),h=(0,r.useCallback)(()=>{let e={immediate:[],onIdle:[],onHover:[],background:[]};switch(t){case"/dashboard":e.immediate=["/playground"],e.onIdle=["/my-models","/logs"],e.background=["/routing-setup","/analytics"];break;case"/my-models":e.immediate=["/playground","/routing-setup"],e.onIdle=["/logs"],e.background=["/dashboard","/analytics"];break;case"/playground":e.immediate=["/logs"],e.onIdle=["/my-models"],e.background=["/dashboard","/training"];break;case"/logs":e.immediate=["/playground"],e.onIdle=["/analytics"],e.background=["/my-models","/dashboard"];break;case"/routing-setup":e.immediate=["/playground"],e.onIdle=["/my-models"],e.background=["/logs","/dashboard"];break;default:e.onIdle=["/dashboard","/playground"]}return l&&a.length>0&&(a.slice(0,2).forEach(t=>{e.immediate.includes(t)||e.immediate.unshift(t)}),a.slice(2).forEach(t=>{e.onIdle.includes(t)||e.onIdle.push(t)})),Object.keys(e).forEach(a=>{e[a]=e[a].filter(e=>e!==t)}),e},[t,a,l]),p=(0,r.useCallback)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"medium";if(m.current.has(e)||m.current.size>=u.maxConcurrent)return void d.current.push(e);m.current.add(e);try{await c(e,{priority:"medium"===t?"low":t,delay:"high"===t?0:"medium"===t?100:300})}catch(e){}finally{if(m.current.delete(e),d.current.length>0){let e=d.current.shift();e&&setTimeout(()=>p(e,"low"),100)}}},[c,u.maxConcurrent]);(0,r.useEffect)(()=>{h().immediate.forEach((e,t)=>{setTimeout(()=>{p(e,"high")},50*t)})},[t,h,p]),(0,r.useEffect)(()=>{let e=h();return g.current&&cancelIdleCallback(g.current),g.current=requestIdleCallback(()=>{e.onIdle.forEach((e,t)=>{setTimeout(()=>{p(e,"medium")},200*t)})},{timeout:u.idleTimeout}),()=>{g.current&&cancelIdleCallback(g.current)}},[t,h,p,u.idleTimeout]),(0,r.useEffect)(()=>{let e=h(),t=setTimeout(()=>{e.background.forEach((e,t)=>{setTimeout(()=>{p(e,"low")},500*t)})},u.backgroundDelay);return()=>clearTimeout(t)},[t,h,p,u.backgroundDelay]);let f=(0,r.useCallback)(e=>({onMouseEnter:()=>{setTimeout(()=>{p(e,"high")},u.hoverDelay)}}),[p,u.hoverDelay]);return{preloadRoute:p,createHoverPreloader:f,getStatus:(0,r.useCallback)(()=>({activePreloads:Array.from(m.current),queuedPreloads:[...d.current],strategy:h()}),[h]),clearPreloading:(0,r.useCallback)(()=>{m.current.clear(),d.current=[],g.current&&(cancelIdleCallback(g.current),g.current=null)},[]),isPreloading:m.current.size>0}}},42724:(e,t,a)=>{a.d(t,{G:()=>l,x:()=>o});var r=a(12115),n=a(35695),i=a(37843);let s="rokey_navigation_patterns";function o(){let[e,t]=(0,r.useState)(null),[a,o]=(0,r.useState)([]),l=(0,n.usePathname)(),{prefetchRoute:c}=(0,i.C)(),u=(0,r.useRef)(Date.now()),d=(0,r.useRef)(Date.now());function m(){let e=new Date().getHours();return e>=6&&e<12?"morning":e>=12&&e<17?"afternoon":e>=17&&e<21?"evening":"night"}(0,r.useEffect)(()=>{let e=localStorage.getItem(s);if(e)try{let a=JSON.parse(e);t({...a,sessionStartTime:d.current})}catch(e){}else t({patterns:[],sessionStartTime:d.current,totalNavigations:0,preferredRoutes:[],timeOfDay:m()})},[]);let g=(0,r.useCallback)((a,r)=>{if(!e||a===r)return;let n=Date.now(),i=n-u.current;u.current=n,t(e=>{if(!e)return null;let t=[...e.patterns],o=t.find(e=>e.from===a&&e.to===r);o?(o.frequency+=1,o.lastUsed=n,o.avgTimeSpent=(o.avgTimeSpent+i)/2):t.push({from:a,to:r,frequency:1,lastUsed:n,avgTimeSpent:i});let l=new Map;t.forEach(e=>{l.set(e.to,(l.get(e.to)||0)+e.frequency)});let c=Array.from(l.entries()).sort((e,t)=>t[1]-e[1]).slice(0,5).map(e=>{let[t]=e;return t}),u={...e,patterns:t,totalNavigations:e.totalNavigations+1,preferredRoutes:c,timeOfDay:m()};try{localStorage.setItem(s,JSON.stringify(u))}catch(e){}return u})},[e]),h=(0,r.useCallback)(()=>e&&l?(m(),[...new Set([...e.patterns.filter(e=>e.from===l&&e.frequency>=2).sort((e,t)=>{let a=e.frequency*(1+(Date.now()-e.lastUsed)/864e5);return t.frequency*(1+(Date.now()-t.lastUsed)/864e5)-a}).slice(0,3).map(e=>e.to),...e.patterns.filter(e=>2>=Math.abs(new Date(e.lastUsed).getHours()-new Date().getHours())).sort((e,t)=>t.frequency-e.frequency).slice(0,2).map(e=>e.to)])].slice(0,4)):[],[e,l]);(0,r.useEffect)(()=>{if(e){let e=h();o(e);let t=setTimeout(()=>{e.forEach((e,t)=>{setTimeout(()=>{c(e,{priority:0===t?"high":"low",delay:200*t})},100*t)})},1e3);return()=>clearTimeout(t)}},[e,l,h,c]);let p=(0,r.useRef)(l);(0,r.useEffect)(()=>{p.current&&p.current!==l&&g(p.current,l),p.current=l},[l,g]);let f=(0,r.useCallback)(()=>{if(!e)return[];let t=[];e.preferredRoutes.length>0&&t.push("Most visited: ".concat(e.preferredRoutes[0])),e.totalNavigations>10&&t.push("".concat(e.totalNavigations," total navigations this session"));let a=e.patterns.filter(e=>1>=Math.abs(new Date(e.lastUsed).getHours()-new Date().getHours()));return a.length>0&&t.push("".concat(a.length," patterns match current time")),t},[e]),y=(0,r.useCallback)(()=>{localStorage.removeItem(s),t({patterns:[],sessionStartTime:Date.now(),totalNavigations:0,preferredRoutes:[],timeOfDay:m()}),o([])},[]);return{predictions:a,userBehavior:e,insights:f(),trackNavigation:g,clearPatterns:y,isLearning:null!=e&&!!e.totalNavigations&&e.totalNavigations>5}}function l(){let e=(0,n.usePathname)(),[t,a]=(0,r.useState)([]);return(0,r.useEffect)(()=>{let t=[];switch(e){case"/dashboard":t.push({route:"/playground",reason:"Test your models",priority:"high"},{route:"/my-models",reason:"Manage API keys",priority:"medium"},{route:"/logs",reason:"Check recent activity",priority:"medium"});break;case"/my-models":t.push({route:"/playground",reason:"Test new configuration",priority:"high"},{route:"/routing-setup",reason:"Configure routing",priority:"high"},{route:"/logs",reason:"View API usage",priority:"low"});break;case"/playground":t.push({route:"/logs",reason:"View request details",priority:"medium"},{route:"/my-models",reason:"Switch configuration",priority:"medium"},{route:"/training",reason:"Customize prompts",priority:"low"});break;case"/logs":t.push({route:"/playground",reason:"Test similar requests",priority:"high"},{route:"/analytics",reason:"Detailed analysis",priority:"medium"},{route:"/my-models",reason:"Adjust configuration",priority:"low"})}a(t)},[e]),t}},44042:(e,t,a)=>{a.d(t,{D:()=>n});var r=a(12115);function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{enableMonitoring:a=!0,enableMemoryTracking:n=!0,enableBundleAnalysis:i=!1,enableCacheTracking:s=!0,warningThresholds:o={renderTime:100,memoryUsage:0x3200000,bundleSize:1048576}}=t,[l,c]=(0,r.useState)({renderTime:0}),u=(0,r.useRef)(0),d=(0,r.useRef)(0),m=(0,r.useRef)(0),g=(0,r.useCallback)(()=>{a&&(u.current=performance.now())},[a]),h=(0,r.useCallback)(()=>{if(!a||!u.current)return;let e=performance.now()-u.current;c(t=>({...t,renderTime:e})),o.renderTime,u.current=0},[e,a,o.renderTime]),p=(0,r.useCallback)(()=>{if(!n||!("memory"in performance))return;let e=performance.memory,t={used:e.usedJSHeapSize,total:e.totalJSHeapSize,limit:e.jsHeapSizeLimit};c(e=>({...e,memoryUsage:t})),t.used,o.memoryUsage},[e,n,o.memoryUsage]),f=(0,r.useCallback)(()=>{if(!i)return;let e=performance.getEntriesByType("resource"),t=0;e.forEach(e=>{e.name.includes(".js")&&e.transferSize&&(t+=e.transferSize)}),c(e=>({...e,bundleSize:t})),o.bundleSize},[i,o.bundleSize]),y=(0,r.useCallback)(()=>{if(!s)return;let e=m.current>0?d.current/m.current*100:0;c(t=>({...t,cacheHitRate:e}))},[s]);(0,r.useEffect)(()=>{if(!s)return;let e=e=>{var t,a;(null==(t=e.data)?void 0:t.type)==="CACHE_HIT"?(d.current++,m.current++,y()):(null==(a=e.data)?void 0:a.type)==="CACHE_MISS"&&(m.current++,y())};if("serviceWorker"in navigator)return navigator.serviceWorker.addEventListener("message",e),()=>{navigator.serviceWorker.removeEventListener("message",e)}},[s,y]),(0,r.useEffect)(()=>{if(!a)return;let e=()=>{let e=performance.getEntriesByType("navigation")[0];if(e){let t=e.loadEventEnd-e.startTime;c(e=>({...e,navigationTime:t}))}};if("complete"!==document.readyState)return window.addEventListener("load",e),()=>window.removeEventListener("load",e);e()},[a]),(0,r.useEffect)(()=>{if(!a)return;let e=setInterval(()=>{p(),f()},5e3);return()=>clearInterval(e)},[a,p,f]);let w=(0,r.useCallback)(()=>{let e=[];return l.renderTime>100&&(e.push("Consider memoizing expensive calculations"),e.push("Use React.memo for component optimization"),e.push("Implement virtualization for large lists")),l.memoryUsage&&l.memoryUsage.used>0x3200000&&(e.push("Check for memory leaks"),e.push("Optimize image sizes and formats"),e.push("Implement proper cleanup in useEffect")),l.bundleSize&&l.bundleSize>1048576&&(e.push("Implement code splitting"),e.push("Use dynamic imports for heavy components"),e.push("Remove unused dependencies")),void 0!==l.cacheHitRate&&l.cacheHitRate<70&&(e.push("Improve caching strategy"),e.push("Implement service worker caching"),e.push("Use browser cache headers")),e},[l]),v=(0,r.useCallback)(()=>({component:e,timestamp:new Date().toISOString(),metrics:l,suggestions:w(),userAgent:navigator.userAgent,url:window.location.href}),[e,l,w]);return{metrics:l,startMeasurement:g,endMeasurement:h,trackMemoryUsage:p,analyzeBundleSize:f,trackCacheHitRate:y,getOptimizationSuggestions:w,exportMetrics:v}}},52643:(e,t,a)=>{a.d(t,{createSupabaseBrowserClient:()=>n});var r=a(13418);function n(){return(0,r.createBrowserClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8")}},53951:(e,t,a)=>{a.d(t,{c:()=>i});var r=a(12115);let n={};function i(){let[e,t]=(0,r.useState)({}),a=(0,r.useRef)({}),i=(0,r.useCallback)(e=>{let t=n[e];return!!t&&!(Date.now()-t.timestamp>3e5)&&!t.isLoading},[]),s=(0,r.useCallback)(e=>{let t=n[e];return!t||t.isLoading?null:Date.now()-t.timestamp>3e5?(delete n[e],null):t.data},[]),o=(0,r.useCallback)(async function(e){var r;let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"medium";if(i(e))return s(e);if(null==(r=n[e])?void 0:r.isLoading)return null;a.current[e]&&a.current[e].abort();let l=new AbortController;a.current[e]=l,n[e]={data:{},timestamp:Date.now(),isLoading:!0},t(t=>({...t,[e]:"loading"}));try{"low"===o?await new Promise(e=>setTimeout(e,200)):"medium"===o&&await new Promise(e=>setTimeout(e,50));let[a,r,i]=await Promise.allSettled([fetch("/api/custom-configs/".concat(e),{signal:l.signal}),fetch("/api/keys?custom_config_id=".concat(e),{signal:l.signal}),fetch("/api/complexity-assignments?custom_config_id=".concat(e),{signal:l.signal})]),s=null,c=[],u="none",d={},m=[];"fulfilled"===a.status&&a.value.ok&&(u=(s=await a.value.json()).routing_strategy||"none",d=s.routing_strategy_params||{}),"fulfilled"===r.status&&r.value.ok&&(c=await r.value.json()),"fulfilled"===i.status&&i.value.ok&&(m=await i.value.json());let g={configDetails:s,apiKeys:c,routingStrategy:u,routingParams:d,complexityAssignments:m};return n[e]={data:g,timestamp:Date.now(),isLoading:!1},t(t=>({...t,[e]:"success"})),g}catch(a){if("AbortError"===a.name)return null;return delete n[e],t(t=>({...t,[e]:"error"})),null}finally{delete a.current[e]}},[i,s]),l=(0,r.useCallback)(e=>({onMouseEnter:()=>{i(e)||o(e,"high")}}),[o,i]),c=(0,r.useCallback)(e=>{delete n[e],t(t=>{let a={...t};return delete a[e],a})},[]),u=(0,r.useCallback)(()=>{Object.keys(n).forEach(e=>{delete n[e]}),t({})},[]);return{prefetchRoutingSetupData:o,getCachedData:s,isCached:i,createHoverPrefetch:l,clearCache:c,clearAllCache:u,getStatus:(0,r.useCallback)(t=>e[t]||"idle",[e]),getCacheInfo:(0,r.useCallback)(()=>({cachedConfigs:Object.keys(n),cacheSize:Object.keys(n).length,totalCacheAge:Object.values(n).reduce((e,t)=>e+(Date.now()-t.timestamp),0)/Object.keys(n).length}),[]),prefetchStatus:e}}},63171:(e,t,a)=>{async function r(){try{localStorage.clear()}catch(e){}try{sessionStorage.clear()}catch(e){}try{let{globalCache:e}=await a.e(6703).then(a.bind(a,86973));e.clear()}catch(e){}try{if("caches"in window){let e=await caches.keys();await Promise.all(e.map(e=>caches.delete(e)))}}catch(e){}try{window}catch(e){}}async function n(e){try{let{globalCache:t}=await a.e(6703).then(a.bind(a,86973));t.invalidateByTags(["user","subscription","usage"]),e&&t.invalidateByTags(["user_".concat(e)])}catch(e){}try{{let e=[];for(let t=0;t<localStorage.length;t++){let a=localStorage.key(t);a&&(a.includes("user")||a.includes("subscription")||a.includes("auth"))&&e.push(a)}e.forEach(e=>localStorage.removeItem(e))}}catch(e){}}a.d(t,{clearAllUserCache:()=>r,clearUserSpecificCache:()=>n})},63943:(e,t,a)=>{a.d(t,{AZ:()=>n,iL:()=>i,xX:()=>s});var r=a(12115);let n=()=>{let[e,t]=(0,r.useState)({isMobile:!1,isTablet:!1,isDesktop:!0,isLargeDesktop:!0,isTouchDevice:!1,orientation:"landscape",width:1920,height:1080,safeAreaInsets:{top:0,bottom:0,left:0,right:0}});return(0,r.useEffect)(()=>{let e=()=>{let e=window.innerWidth,a=window.innerHeight,r="ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0,n=getComputedStyle(document.documentElement);t({isMobile:e<768,isTablet:e>=768&&e<1024,isDesktop:e>=1024,isLargeDesktop:e>=1280,isTouchDevice:r,orientation:e>a?"landscape":"portrait",width:e,height:a,safeAreaInsets:{top:parseInt(n.getPropertyValue("env(safe-area-inset-top)")||"0"),bottom:parseInt(n.getPropertyValue("env(safe-area-inset-bottom)")||"0"),left:parseInt(n.getPropertyValue("env(safe-area-inset-left)")||"0"),right:parseInt(n.getPropertyValue("env(safe-area-inset-right)")||"0")}})};return e(),window.addEventListener("resize",e),window.addEventListener("orientationchange",e),()=>{window.removeEventListener("resize",e),window.removeEventListener("orientationchange",e)}},[]),e},i=()=>{let[e,t]=(0,r.useState)(!1),[a,i]=(0,r.useState)(!1),{isMobile:s}=n();return(0,r.useEffect)(()=>{s||(t(!1),i(!1))},[s]),{isMainSidebarOpen:e,isHistorySidebarOpen:a,toggleMainSidebar:()=>{t(!e),s&&!e&&i(!1)},toggleHistorySidebar:()=>{i(!a),s&&!a&&t(!1)},closeAllSidebars:()=>{t(!1),i(!1)},setIsMainSidebarOpen:t,setIsHistorySidebarOpen:i}},s=()=>{let{isMobile:e}=n();return{scrollToTop:function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];window.scrollTo({top:0,behavior:e?"smooth":"auto"})},scrollToElement:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=document.getElementById(e);if(a){let e=a.offsetTop-t;window.scrollTo({top:e,behavior:"smooth"})}},preventBodyScroll:t=>{e&&(document.body.style.overflow=t?"hidden":"")}}}},70036:(e,t,a)=>{a.d(t,{LJ:()=>n});var r=a(12115);function n(e,t){let[a,n]=(0,r.useState)([]),[i,s]=(0,r.useState)(!1),[o,l]=(0,r.useState)(null),[c,u]=(0,r.useState)(null),[d,m]=(0,r.useState)(""),g=(0,r.useRef)(null),h=(0,r.useRef)(null);(0,r.useRef)(null);let p=(0,r.useRef)(0),f=(0,r.useRef)(""),y=(0,r.useRef)(""),w=(0,r.useCallback)(()=>{g.current&&(g.current.close(),g.current=null),h.current&&(clearTimeout(h.current),h.current=null),s(!1)},[]),v=(0,r.useCallback)(()=>{if(!e&&!t)return void l("No execution ID or direct stream URL provided");let a=t||(e?"/api/orchestration/stream/".concat(e):"");if(!a)return void l("No valid stream URL could be determined");if(y.current!==a||!i){w(),f.current=e||"",y.current=a;try{let e=new EventSource(a);g.current=e,e.onopen=()=>{s(!0),l(null),p.current=0},e.onmessage=e=>{try{let t=JSON.parse(e.data);n(e=>[...e,t]),u(t),l(null)}catch(e){l("Error parsing stream data")}},e.addEventListener("orchestration_started",e=>{JSON.parse(e.data)}),e.addEventListener("step_started",e=>{JSON.parse(e.data)}),e.addEventListener("step_progress",e=>{JSON.parse(e.data)}),e.addEventListener("step_completed",e=>{JSON.parse(e.data)}),e.addEventListener("synthesis_started",e=>{JSON.parse(e.data)}),e.addEventListener("orchestration_completed",e=>{JSON.parse(e.data)}),e.onerror=e=>{if(s(!1),p.current<5){let e=1e3*Math.pow(2,p.current);p.current++,l("Connection lost. Reconnecting in ".concat(e/1e3,"s... (attempt ").concat(p.current,"/").concat(5,")")),h.current=setTimeout(()=>{v()},e)}else l("Connection failed after multiple attempts. Please refresh the page.")}}catch(e){l("Failed to establish connection"),s(!1)}}},[e,w]),b=(0,r.useCallback)(()=>{p.current=0,v()},[v]);return(0,r.useEffect)(()=>((e||t)&&v(),()=>{w()}),[e,t,v,w]),(0,r.useEffect)(()=>{let a=()=>{"visible"===document.visibilityState&&!i&&(e||t)&&b()};return document.addEventListener("visibilitychange",a),()=>{document.removeEventListener("visibilitychange",a)}},[i,e,t,b]),(0,r.useEffect)(()=>{let a=()=>{!i&&(e||t)&&b()},r=()=>{l("Network connection lost")};return window.addEventListener("online",a),window.addEventListener("offline",r),()=>{window.removeEventListener("online",a),window.removeEventListener("offline",r)}},[i,e,t,b]),{events:a,isConnected:i,error:o,lastEvent:c,reconnect:b,disconnect:w}}},71118:(e,t,a)=>{a.d(t,{j:()=>s});var r=a(12115),n=a(35695);let i=["/features","/pricing","/about","/auth/signin","/auth/signup","/docs"];function s(){let e=(0,n.useRouter)();return(0,r.useEffect)(()=>{(async()=>{"requestIdleCallback"in window?window.requestIdleCallback(()=>{i.forEach(t=>{e.prefetch(t)})}):setTimeout(()=>{i.forEach(t=>{e.prefetch(t)})},100)})()},[e]),{navigateInstantly:(0,r.useCallback)(t=>{(0,r.startTransition)(()=>{e.push(t)})},[e])}}},74857:(e,t,a)=>{a.d(t,{v7:()=>o,zX:()=>l,Nu:()=>c});var r=a(49509);let n={publishableKey:r.env.STRIPE_LIVE_PUBLISHABLE_KEY,secretKey:r.env.STRIPE_LIVE_SECRET_KEY,webhookSecret:r.env.STRIPE_LIVE_WEBHOOK_SECRET},i={FREE:r.env.STRIPE_LIVE_FREE_PRICE_ID,STARTER:r.env.STRIPE_LIVE_STARTER_PRICE_ID,PROFESSIONAL:r.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,ENTERPRISE:r.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID},s={FREE:r.env.STRIPE_LIVE_FREE_PRODUCT_ID,STARTER:r.env.STRIPE_LIVE_STARTER_PRODUCT_ID,PROFESSIONAL:r.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID,ENTERPRISE:r.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID};r.env.STRIPE_LIVE_FREE_PRICE_ID,r.env.STRIPE_LIVE_STARTER_PRICE_ID,r.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,r.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID,n.publishableKey&&n.publishableKey.substring(0,20),n.secretKey&&n.secretKey.substring(0,20),n.webhookSecret&&n.webhookSecret.substring(0,15);let o={free:{name:"Free",price:"$0",priceId:i.FREE,productId:s.FREE,features:["Unlimited API requests","1 Custom Configuration","3 API Keys per config","All 300+ AI models","Strict fallback routing only","Basic analytics only","No custom roles, basic router only","Limited logs","Community support"],limits:{configurations:1,apiKeysPerConfig:3,apiRequests:999999,canUseAdvancedRouting:!1,canUseCustomRoles:!1,maxCustomRoles:0,canUsePromptEngineering:!1,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1}},starter:{name:"Starter",price:"$19",priceId:i.STARTER,productId:s.STARTER,features:["Unlimited API requests","15 Custom Configurations","5 API Keys per config","All 300+ AI models","Intelligent routing strategies","Up to 3 custom roles","Intelligent role routing","Prompt engineering (no file upload)","Enhanced logs and analytics","Community support"],limits:{configurations:15,apiKeysPerConfig:5,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:3,canUsePromptEngineering:!0,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1}},professional:{name:"Professional",price:"$49",priceId:i.PROFESSIONAL,productId:s.PROFESSIONAL,features:["Unlimited API requests","Unlimited Custom Configurations","Unlimited API Keys per config","All 300+ AI models","All advanced routing strategies","Unlimited custom roles","Prompt engineering + Knowledge base (5 documents)","Semantic caching","Advanced analytics and logging","Priority email support"],limits:{configurations:999999,apiKeysPerConfig:999999,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:999999,canUsePromptEngineering:!0,canUseKnowledgeBase:!0,knowledgeBaseDocuments:5,canUseSemanticCaching:!0}}};function l(e){return o[e]}function c(e,t){let a=o[e].limits;switch(t){case"custom_roles":return a.canUseCustomRoles;case"knowledge_base":return a.canUseKnowledgeBase;case"advanced_routing":return a.canUseAdvancedRouting;case"prompt_engineering":return a.canUsePromptEngineering;case"semantic_caching":return a.canUseSemanticCaching;case"configurations":return a.configurations>0;default:return!1}}},83298:(e,t,a)=>{a.d(t,{R:()=>i});var r=a(12115),n=a(52643);function i(){let[e,t]=(0,r.useState)(null),[i,s]=(0,r.useState)(null),[o,l]=(0,r.useState)(null),[c,u]=(0,r.useState)(!0),[d,m]=(0,r.useState)(null),g=(0,n.createSupabaseBrowserClient)();(0,r.useEffect)(()=>{(async()=>{let{data:{user:e}}=await g.auth.getUser();t(null!=e?e:null),e?(h(e),p(e)):u(!1)})();let{data:{subscription:e}}=g.auth.onAuthStateChange(async(e,r)=>{var n;if(t(null!=(n=null==r?void 0:r.user)?n:null),null==r?void 0:r.user){s(null),l(null),u(!0);try{let{clearUserSpecificCache:e}=await a.e(5738).then(a.bind(a,63171));await e(r.user.id)}catch(e){}await new Promise(e=>setTimeout(e,100)),await Promise.all([h(r.user),p(r.user)])}else s(null),l(null),m(null),u(!1)});return()=>e.unsubscribe()},[]);let h=async e=>{try{let t=Date.now(),a=await fetch("/api/stripe/subscription-status?userId=".concat(e.id,"&_t=").concat(t),{cache:"no-store",headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}});if(!a.ok)throw await a.text(),Error("Failed to fetch subscription status: ".concat(a.status," ").concat(a.statusText));let r=await a.json();s(r)}catch(e){e instanceof Error&&e.message.includes("Failed to fetch")?s({hasActiveSubscription:!1,tier:"free",status:null,currentPeriodEnd:null,cancelAtPeriodEnd:!1,isFree:!0}):m(e instanceof Error?e.message:"Unknown error")}},p=async e=>{try{let t=await fetch("/api/stripe/subscription-status",{method:"POST",cache:"no-store",headers:{"Content-Type":"application/json","Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"},body:JSON.stringify({userId:e.id,_t:Date.now()})});if(!t.ok)throw Error("Failed to fetch usage status");let a=await t.json();l(a)}catch(e){e instanceof Error&&e.message.includes("Failed to fetch")?l({tier:"free",usage:{configurations:0,apiKeys:0,apiRequests:0},limits:{configurations:1,apiKeysPerConfig:3,apiRequests:999999,canUseAdvancedRouting:!1,canUseCustomRoles:!1,maxCustomRoles:0,canUsePromptEngineering:!1,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1},canCreateConfig:!0,canCreateApiKey:!0}):m(e instanceof Error?e.message:"Unknown error")}finally{u(!1)}};return{subscriptionStatus:i,usageStatus:o,loading:c,error:d,createCheckoutSession:async t=>{let a;if(!e)throw Error("User not authenticated");let r=await fetch("/api/stripe/price-ids");if(!r.ok)throw Error("Failed to fetch price IDs");let n=await r.json();switch(t){case"free":a=n.free;break;case"starter":a=n.starter;break;case"professional":a=n.professional;break;default:throw Error("Invalid tier: ".concat(t))}let i=await fetch("/api/stripe/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({priceId:a,userId:e.id,userEmail:e.email,tier:t})});if(!i.ok)throw Error((await i.json()).error||"Failed to create checkout session");let{url:s}=await i.json();window.location.href=s},openCustomerPortal:async t=>{if(!e)throw Error("User not authenticated");try{let a=await fetch("/api/stripe/customer-portal",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e.id,returnUrl:t})});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to open customer portal")}let{url:r}=await a.json();window.location.href=r}catch(e){throw e}},refreshStatus:async()=>{if(e){u(!0),m(null);try{let{clearUserSpecificCache:t}=await a.e(5738).then(a.bind(a,63171));await t(e.id)}catch(e){}try{if("caches"in window){let e=await caches.keys();await Promise.all(e.map(e=>caches.delete(e)))}}catch(e){}await new Promise(e=>setTimeout(e,500));try{s(null),l(null),await Promise.all([h(e),p(e)])}catch(e){m(e instanceof Error?e.message:"Failed to refresh subscription status")}}},isAuthenticated:!!e,user:e}}},87162:(e,t,a)=>{a.d(t,{Z:()=>n});var r=a(12115);function n(){let[e,t]=(0,r.useState)({isOpen:!1,isLoading:!1,title:"",message:"",confirmText:"Confirm",cancelText:"Cancel",type:"danger",onConfirm:()=>{}}),a=(0,r.useCallback)((e,a)=>{t({isOpen:!0,isLoading:!1,title:e.title,message:e.message,confirmText:e.confirmText||"Confirm",cancelText:e.cancelText||"Cancel",type:e.type||"danger",onConfirm:async()=>{t(e=>({...e,isLoading:!0}));try{await a(),t(e=>({...e,isOpen:!1,isLoading:!1}))}catch(e){throw t(e=>({...e,isLoading:!1})),e}}})},[]),n=(0,r.useCallback)(()=>{t(e=>({...e,isOpen:!1,isLoading:!1}))},[]);return{...e,showConfirmation:a,hideConfirmation:n}}},94437:(e,t,a)=>{a.d(t,{Q:()=>n});var r=a(12115);let n=()=>{let[e,t]=(0,r.useState)({isMobile:!1,isTablet:!1,isDesktop:!0,isLargeDesktop:!0,width:1920,height:1080});return(0,r.useEffect)(()=>{let e=()=>{let e=window.innerWidth;t({isMobile:e<768,isTablet:e>=768&&e<1024,isDesktop:e>=1024,isLargeDesktop:e>=1280,width:e,height:window.innerHeight})};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}},96121:()=>{class e{static getInstance(){return e.instance||(e.instance=new e),e.instance}trackParallelFlow(e){let t="".concat(e.provider,"_").concat(e.model,"_parallel");this.parallelMetrics||(this.parallelMetrics=new Map),this.parallelMetrics.has(t)||this.parallelMetrics.set(t,[]);let a=this.parallelMetrics.get(t);a.push({...e,timestamp:Date.now()}),a.length>this.maxSamples&&a.shift(),e.firstTokenTime}trackMessagingFlow(e){let t="".concat(e.provider,"_").concat(e.model);this.metrics.has(t)||this.metrics.set(t,[]);let a=this.metrics.get(t);a.push({timestamp:Date.now(),...e}),a.length>this.maxSamples&&a.shift(),this.logPerformanceInsights(e)}getStats(e,t){let a="".concat(e,"_").concat(t),r=this.metrics.get(a);if(!r||0===r.length)return null;let n=r.filter(e=>e.success);if(0===n.length)return null;let i=n.map(e=>e.timings.total),s=n.map(e=>e.timings.llmApiCall),o=n.map(e=>e.messageLength);return{provider:e,model:t,sampleCount:n.length,averageTotal:this.calculateAverage(i),averageLLM:this.calculateAverage(s),medianTotal:this.calculateMedian(i),medianLLM:this.calculateMedian(s),p95Total:this.calculatePercentile(i,95),p95LLM:this.calculatePercentile(s,95),minTotal:Math.min(...i),maxTotal:Math.max(...i),averageMessageLength:this.calculateAverage(o),streamingUsage:n.filter(e=>e.isStreaming).length/n.length,errorRate:(r.length-n.length)/r.length,recentTrend:this.calculateTrend(i.slice(-10))}}getSummary(){let e=new Set,t=[];for(let a of this.metrics.keys()){let[r,n]=a.split("_");e.add(r);let i=this.getStats(r,n);i&&t.push(i)}if(0===t.length)return{totalProviders:0,totalModels:0,overallAverageTime:0,fastestProvider:null,slowestProvider:null,recommendations:["No messaging data available yet"]};let a=this.calculateAverage(t.map(e=>e.averageTotal)),r=[...t].sort((e,t)=>e.averageTotal-t.averageTotal);return{totalProviders:e.size,totalModels:t.length,overallAverageTime:a,fastestProvider:r[0],slowestProvider:r[r.length-1],recommendations:this.generateRecommendations(t)}}generateRecommendations(e){let t=[],a=e.filter(e=>e.averageTotal>5e3);a.length>0&&t.push("Consider switching from slow providers: ".concat(a.map(e=>e.provider).join(", "))),e.filter(e=>e.streamingUsage<.5).length>0&&t.push("Enable streaming for better perceived performance");let r=e.filter(e=>e.errorRate>.1);r.length>0&&t.push("High error rates detected for: ".concat(r.map(e=>e.provider).join(", ")));let n=e.filter(e=>e.averageTotal<=2e3);return 0===n.length?t.push("No providers meeting 2s target - consider optimizing or switching providers"):t.push("Fast providers (≤2s): ".concat(n.map(e=>e.provider).join(", "))),t.length>0?t:["Performance looks good!"]}logPerformanceInsights(e){let{provider:t,model:a,timings:r,isStreaming:n,streamingMetrics:i}=e;n&&r.timeToFirstToken&&(r.timeToFirstToken<500||r.timeToFirstToken<1e3||r.timeToFirstToken,i&&i.averageTokenLatency),r.total,r.total,r.total,r.llmApiCall,r.llmApiCall,r.total,r.backendProcessing&&r.frontendProcessing&&(r.backendProcessing,r.total,r.frontendProcessing,r.total),n||r.total}calculateAverage(e){return e.reduce((e,t)=>e+t,0)/e.length}calculateMedian(e){let t=[...e].sort((e,t)=>e-t),a=Math.floor(t.length/2);return t.length%2==0?(t[a-1]+t[a])/2:t[a]}calculatePercentile(e,t){let a=[...e].sort((e,t)=>e-t),r=Math.ceil(t/100*a.length)-1;return a[Math.max(0,r)]}calculateTrend(e){if(e.length<5)return"stable";let t=e.slice(0,Math.floor(e.length/2)),a=e.slice(Math.floor(e.length/2)),r=this.calculateAverage(t),n=(this.calculateAverage(a)-r)/r*100;return n<-10?"improving":n>10?"degrading":"stable"}exportData(){let e={};for(let[t,a]of this.metrics.entries())e[t]=[...a];return e}clear(){this.metrics.clear()}constructor(){this.metrics=new Map,this.maxSamples=50,this.parallelMetrics=new Map}}class t{static getInstance(){return t.instance||(t.instance=new t),t.instance}startRequest(e,t,a){this.timingData.set(e,{requestStart:performance.now(),tokenCount:0,provider:t,model:a})}markFirstToken(e){let t=this.timingData.get(e);if(!t)return null;let a=performance.now()-t.requestStart;return t.firstTokenReceived=performance.now(),a}trackToken(e){let t=this.timingData.get(e);t&&t.tokenCount++}completeStream(e){let t=this.timingData.get(e);if(!t||!t.firstTokenReceived)return null;let a=performance.now();t.streamComplete=a;let r=t.firstTokenReceived-t.requestStart,n=a-t.requestStart,i=a-t.firstTokenReceived,s=t.tokenCount>1?i/(t.tokenCount-1):0,o={timeToFirstToken:r,totalStreamTime:n,totalTokens:t.tokenCount,averageTokenLatency:s};return this.timingData.delete(e),o}getStatus(e){let t=this.timingData.get(e);return t?t.firstTokenReceived?t.streamComplete?"Complete":"Streaming in progress":"Waiting for first token":"Not tracked"}clear(){this.timingData.clear()}constructor(){this.timingData=new Map}}let a=e.getInstance(),r=t.getInstance();function n(){let e=a.getSummary();e.fastestProvider,e.slowestProvider,e.recommendations.forEach(e=>console.log("   • ".concat(e)))}function i(){var e;null==(e=r.timingData)||e.size}function s(){}function o(){let e=setInterval(()=>{a.getSummary().totalProviders},3e4);globalThis.__performanceMonitoringInterval=e}function l(){let e=globalThis.__performanceMonitoringInterval;e&&(clearInterval(e),delete globalThis.__performanceMonitoringInterval)}function c(){let e=a.getSummary();0!==e.totalProviders&&(e.overallAverageTime<2e3||e.overallAverageTime<5e3||e.overallAverageTime,e.fastestProvider,e.slowestProvider)}"undefined"!=typeof globalThis&&(globalThis.logComprehensivePerformanceReport=n,globalThis.logFirstTokenReport=i,globalThis.logGoogleStreamingDebug=s,globalThis.quickPerformanceCheck=c,globalThis.startPerformanceMonitoring=o,globalThis.stopPerformanceMonitoring=l,globalThis.performanceLogs={comprehensive:n,firstToken:i,googleDebug:s,quick:c,startMonitoring:o,stopMonitoring:l})}}]);