(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3882],{11485:(e,t,a)=>{"use strict";a.d(t,{B:()=>n.A,v:()=>r.A});var r=a(30192),n=a(86474)},22261:(e,t,a)=>{"use strict";a.d(t,{G:()=>i,c:()=>l});var r=a(95155),n=a(12115);let s=(0,n.createContext)(void 0);function i(e){let{children:t}=e,[a,i]=(0,n.useState)(!0),[l,o]=(0,n.useState)(!1),[c,d]=(0,n.useState)(!1);return(0,r.jsx)(s.Provider,{value:{isCollapsed:a,isHovered:l,isHoverDisabled:c,toggleSidebar:()=>i(!a),collapseSidebar:()=>i(!0),expandSidebar:()=>i(!1),setHovered:e=>{c||o(e)},setHoverDisabled:e=>{d(e),e&&o(!1)}},children:t})}function l(){let e=(0,n.useContext)(s);if(void 0===e)throw Error("useSidebar must be used within a SidebarProvider");return e}},24766:(e,t,a)=>{Promise.resolve().then(a.bind(a,79021))},33654:(e,t,a)=>{"use strict";a.d(t,{S:()=>r.A});var r=a(29337)},59513:(e,t,a)=>{"use strict";a.d(t,{EF:()=>r.A,DQ:()=>n.A,C1:()=>s.A,Pp:()=>l,DP:()=>o.A,nr:()=>c,Y3:()=>d,XL:()=>u.A,$p:()=>h.A,R2:()=>m.A,P:()=>x.A,Zu:()=>g.A,BZ:()=>p.A,Gg:()=>f.A,K6:()=>v.A});var r=a(5279),n=a(64274),s=a(6865),i=a(12115);let l=i.forwardRef(function(e,t){let{title:a,titleId:r,...n}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?i.createElement("title",{id:r},a):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"}))});var o=a(5246);let c=i.forwardRef(function(e,t){let{title:a,titleId:r,...n}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?i.createElement("title",{id:r},a):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m6.75 7.5 3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0 0 21 18V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v12a2.25 2.25 0 0 0 2.25 2.25Z"}))}),d=i.forwardRef(function(e,t){let{title:a,titleId:r,...n}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?i.createElement("title",{id:r},a):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.362 5.214A8.252 8.252 0 0 1 12 21 8.25 8.25 0 0 1 6.038 7.047 8.287 8.287 0 0 0 9 9.601a8.983 8.983 0 0 1 3.361-6.867 8.21 8.21 0 0 0 3 2.48Z"}),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 18a3.75 3.75 0 0 0 .495-7.468 5.99 5.99 0 0 0-1.925 3.547 5.975 5.975 0 0 1-2.133-1.001A3.75 3.75 0 0 0 12 18Z"}))});var u=a(48666),h=a(78046),m=a(61316),x=a(65946),g=a(8246),p=a(86474),f=a(27305),v=a(64219)},60323:(e,t,a)=>{"use strict";a.d(t,{D:()=>r.A});var r=a(63418)},64134:(e,t,a)=>{"use strict";a.d(t,{g:()=>n});var r=a(12115);let n=r.forwardRef(function(e,t){let{title:a,titleId:n,...s}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},s),a?r.createElement("title",{id:n},a):null,r.createElement("path",{fillRule:"evenodd",d:"M5.25 2.25a3 3 0 0 0-3 3v4.318a3 3 0 0 0 .879 2.121l9.58 9.581c.92.92 2.39 1.186 3.548.428a18.849 18.849 0 0 0 5.441-5.44c.758-1.16.492-2.629-.428-3.548l-9.58-9.581a3 3 0 0 0-2.122-.879H5.25ZM6.375 7.5a1.125 1.125 0 1 0 0-2.25 1.125 1.125 0 0 0 0 2.25Z",clipRule:"evenodd"}))})},79021:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>C});var r=a(95155),n=a(12115);let s=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M19.916 4.626a.75.75 0 0 1 .208 1.04l-9 13.5a.75.75 0 0 1-1.154.114l-6-6a.75.75 0 0 1 1.06-1.06l5.353 5.353 8.493-12.74a.75.75 0 0 1 1.04-.207Z",clipRule:"evenodd"}))}),i=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{d:"M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z"}))}),l=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M18.97 3.659a2.25 2.25 0 0 0-3.182 0l-10.94 10.94a3.75 3.75 0 1 0 5.304 5.303l7.693-7.693a.75.75 0 0 1 1.06 1.06l-7.693 7.693a5.25 5.25 0 1 1-7.424-7.424l10.939-10.94a3.75 3.75 0 1 1 5.303 5.304L9.097 18.835l-.008.008-.007.007-.002.002-.003.002A2.25 2.25 0 0 1 5.91 15.66l7.81-7.81a.75.75 0 0 1 1.061 1.06l-7.81 7.81a.75.75 0 0 0 1.054 1.068L18.97 6.84a2.25 2.25 0 0 0 0-3.182Z",clipRule:"evenodd"}))}),o=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M16.5 4.478v.227a48.816 48.816 0 0 1 3.878.512.75.75 0 1 1-.256 1.478l-.209-.035-1.005 13.07a3 3 0 0 1-2.991 2.77H8.084a3 3 0 0 1-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 0 1-.256-1.478A48.567 48.567 0 0 1 7.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 0 1 3.369 0c1.603.051 2.815 1.387 2.815 2.951Zm-6.136-1.452a51.196 51.196 0 0 1 3.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 0 0-6 0v-.113c0-.794.609-1.428 1.364-1.452Zm-.355 5.945a.75.75 0 1 0-1.5.058l.347 9a.75.75 0 1 0 1.499-.058l-.346-9Zm5.48.058a.75.75 0 1 0-1.498-.058l-.347 9a.75.75 0 0 0 1.5.058l.345-9Z",clipRule:"evenodd"}))}),c=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",clipRule:"evenodd"}))}),d=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"}))});var u=a(79112),h=a(95803),m=a(8413),x=a(43456),g=a(4654),p=a(22261),f=a(24403),v=a(41e3),y=a(83298),w=a(94437),b=a(63943);a(96121);let j=(0,n.lazy)(()=>Promise.all([a.e(3466),a.e(5738),a.e(9968),a.e(6060),a.e(4755),a.e(563),a.e(2662),a.e(8669),a.e(4703),a.e(3269),a.e(9173),a.e(9219),a.e(5721)]).then(a.bind(a,35079)).then(e=>({default:e.OrchestrationCanvas}))),N=(0,n.memo)(e=>{let{msg:t,index:a,visibleMessages:n,isCanvasOpen:i,isCanvasMinimized:l,editingMessageId:o,editingText:x,setEditingText:g,saveEditedMessage:p,cancelEditingMessage:f,startEditingMessage:v,handleRetryMessage:y,selectedConfigId:w,isLoading:b}=e,j=a>0?n[a-1]:null,N=j&&"user"===j.role&&"assistant"===t.role;return(0,r.jsx)("div",{"data-message-id":t.id,className:"flex ".concat("user"===t.role?"justify-end":"justify-start"," group ").concat(i&&!l?"-ml-96":""," ").concat("assistant"===t.role&&i&&!l?"ml-8":""," ").concat(0===a?"pt-3":N?"mt-6":"mt-32"),children:(0,r.jsxs)("div",{className:"".concat("user"===t.role?i&&!l?"max-w-[60%]":"max-w-[50%]":"max-w-[100%]"," relative ").concat("user"===t.role?"bg-gray-700/60 text-white rounded-2xl rounded-br-lg shadow-sm border border-gray-600/30":"assistant"===t.role?"text-white":"system"===t.role?"bg-amber-500/20 text-amber-300 rounded-xl border border-amber-500/30":"bg-red-500/20 text-red-300 rounded-xl border border-red-500/30"," ").concat("user"===t.role?"px-4 py-3":"assistant"===t.role?"px-0 py-0":"px-4 py-3"),children:["user"===t.role&&(0,r.jsxs)("div",{className:"absolute -bottom-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[(0,r.jsx)(h.A,{text:t.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n"),variant:"message",size:"sm",title:"Copy message",className:"text-gray-400 hover:text-white hover:bg-white/20 rounded-lg cursor-pointer"}),(0,r.jsx)("button",{onClick:()=>v(t.id,t.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n")),className:"p-1.5 text-gray-400 hover:text-white hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer",title:"Edit message",children:(0,r.jsx)(d,{className:"w-4 h-4 stroke-2"})})]}),"user"!==t.role&&(0,r.jsxs)("div",{className:"absolute -bottom-8 left-0 z-10 flex items-center space-x-2",children:[(0,r.jsx)(h.A,{text:t.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n"),variant:"message",size:"sm",title:"Copy message"}),"assistant"===t.role&&w&&(0,r.jsx)(m.A,{configId:w,onRetry:e=>y(a,e),disabled:b})]}),(0,r.jsx)("div",{className:"space-y-2 chat-message-content",children:"user"===t.role&&o===t.id?(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("textarea",{value:x,onChange:e=>g(e.target.value),className:"w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none",placeholder:"Edit your message...",rows:3,autoFocus:!0}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{onClick:p,disabled:!x.trim(),className:"flex items-center space-x-1 px-3 py-1.5 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 text-white text-sm rounded-lg transition-all duration-200",children:[(0,r.jsx)(s,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Save & Continue"})]}),(0,r.jsxs)("button",{onClick:f,className:"flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200",children:[(0,r.jsx)(c,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Cancel"})]})]}),(0,r.jsx)("p",{className:"text-white/70 text-xs",children:"\uD83D\uDCA1 Saving will restart the conversation from this point, removing all messages that came after."})]}):t.content.map((e,a)=>{if("text"===e.type)if("assistant"===t.role)return(0,r.jsx)(u.A,{content:e.text,className:"text-[16.5px]"},a);else return(0,r.jsx)("div",{className:"whitespace-pre-wrap break-words leading-relaxed text-[16.5px]",children:e.text},a);return"image_url"===e.type?(0,r.jsx)("img",{src:e.image_url.url,alt:"uploaded content",className:"max-w-full max-h-48 rounded-xl shadow-sm"},a):null})})]})},t.id)},(e,t)=>e.msg.id===t.msg.id&&e.msg.content===t.msg.content&&e.editingMessageId===t.editingMessageId&&e.editingText===t.editingText&&e.isCanvasOpen===t.isCanvasOpen&&e.isCanvasMinimized===t.isCanvasMinimized&&e.selectedConfigId===t.selectedConfigId&&e.isLoading===t.isLoading),S=(e,t)=>{},k=n.memo(e=>{let{chat:t,currentConversation:a,onLoadChat:n,onDeleteChat:s}=e,i=(null==a?void 0:a.id)===t.id;return(0,r.jsxs)("div",{className:"relative group p-3 hover:bg-white/10 rounded-xl transition-all duration-200 ".concat(i?"bg-blue-500/20 border border-blue-400/30":""),children:[(0,r.jsx)("button",{onClick:()=>n(t),className:"w-full text-left",children:(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-white truncate mb-1",children:t.title}),t.last_message_preview&&(0,r.jsx)("p",{className:"text-xs text-gray-400 line-clamp-2 mb-2",children:t.last_message_preview}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,r.jsxs)("span",{children:[t.message_count," messages"]}),(0,r.jsx)("span",{children:new Date(t.updated_at).toLocaleDateString()})]})]})})}),(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),s(t.id)},className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-400 hover:bg-red-500/20 rounded transition-all duration-200",title:"Delete conversation",children:(0,r.jsx)(o,{className:"w-4 h-4"})})]})});function C(){var e,t,a;let{isCollapsed:s,isHovered:o,setHoverDisabled:d}=(0,p.c)(),{user:u}=(0,y.R)(),{isMobile:h,isTablet:m,isDesktop:C}=(0,w.Q)(),{isMobile:T,isTouchDevice:E,orientation:_}=(0,b.AZ)(),{isMainSidebarOpen:A,isHistorySidebarOpen:O,toggleMainSidebar:L,toggleHistorySidebar:M,closeAllSidebars:R}=(0,b.iL)(),{preventBodyScroll:I}=(0,b.xX)(),H=!s||o?256:64,D=(null==u||null==(e=u.user_metadata)?void 0:e.first_name)||(null==u||null==(a=u.user_metadata)||null==(t=a.full_name)?void 0:t.split(" ")[0])||"",[P,z]=(0,n.useState)([]),[B,W]=(0,n.useState)(""),[F,U]=(0,n.useState)(!0),q=(0,n.useCallback)(async e=>{if(e)try{let t=await fetch("/api/keys?custom_config_id=".concat(e),{cache:"force-cache",headers:{"Cache-Control":"max-age=300"}});t.ok&&await t.json()}catch(e){}},[]);(0,n.useEffect)(()=>{if(B){let e=setTimeout(()=>{q(B)},1e3);return()=>clearTimeout(e)}},[B,q]);let[Z,Y]=(0,n.useState)(""),[J,V]=(0,n.useState)([]),[X,K]=(0,n.useState)(!1),[G,Q]=(0,n.useState)(null),[$,ee]=(0,n.useState)(!0),[et,ea]=(0,n.useState)(!1),[er,en]=(0,n.useState)(null),[es,ei]=(0,n.useState)([]),[el,eo]=(0,n.useState)([]),ec=(0,n.useRef)(null),ed=(0,n.useRef)(null),eu=(0,n.useRef)(null),[eh,em]=(0,n.useState)(!1),[ex,eg]=(0,n.useState)(null),[ep,ef]=(0,n.useState)(null),[ev,ey]=(0,n.useState)(""),[ew,eb]=(0,n.useState)(!1),[ej,eN]=(0,n.useState)(null),[eS,ek]=(0,n.useState)(!1),[eC,eT]=(0,n.useState)(!1),[eE,e_]=(0,n.useState)(!1),[eA,eO]=(0,n.useState)(!1),[eL,eM]=(0,n.useState)(!1);(0,n.useEffect)(()=>{d(eE&&!eA)},[eE,eA,d]),(0,n.useEffect)(()=>{if(!C&&(A||O))return I(!0),()=>I(!1)},[C,A,O,I]),(0,n.useEffect)(()=>{},[eS,ej,eE,eA]);let eR=(0,v.w6)({enableAutoProgression:!0,onStageChange:void 0}),[eI,eH]=(0,n.useState)(""),[eD,eP]=(0,n.useState)(null),[ez,eB]=(0,n.useState)(!1),eW=(e,t)=>{let a="";if(e.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**"))a="Multi-Role AI Orchestration Started";else if(e.includes("\uD83D\uDCCB **Orchestration Plan:**"))a="Planning specialist assignments";else if(e.includes("\uD83E\uDD16 **Moderator:**"))a="Moderator coordinating specialists";else if(e.includes("Specialist:")&&e.includes("Working...")){let t=e.match(/(\w+)\s+Specialist:/);a=t?"".concat(t[1]," Specialist working"):"Specialist working on your request"}else e.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")?a="Synthesizing specialist responses":e.includes("Analyzing and processing")&&(a="Analyzing and processing with specialized expertise");a&&a!==eI&&(eH(a),t.updateOrchestrationStatus(a))},eF=async()=>{if(B&&ex){K(!0),eH("Continuing synthesis automatically..."),eR.startProcessing();try{let a,r={id:Date.now().toString()+"-continue",role:"user",content:[{type:"text",text:"continue"}]};V(e=>[...e,r]),await tt(ex.id,r),a={custom_api_config_id:B,messages:[...J.map(e=>({role:e.role,content:1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content})),{role:"user",content:"continue"}],stream:$,...(null==u?void 0:u.id)&&{_internal_user_id:u.id}};let n=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(a),cache:"no-store"});if(n.ok){let a,r=await n.text();try{a=JSON.parse(r)}catch(e){a=null}if((null==a?void 0:a.error)==="synthesis_complete"){V(e=>e.slice(0,-1)),K(!1),eH(""),eR.markComplete(),Y("continue"),setTimeout(()=>{tu()},100);return}let s=new Response(r,{status:n.status,statusText:n.statusText,headers:n.headers});if($&&s.body){let a=s.body.getReader(),r=new TextDecoder,n=Date.now().toString()+"-assistant-continue",i={id:n,role:"assistant",content:[{type:"text",text:""}]};V(e=>[...e,i]);let l="",o=!1,c=null,d=s.headers.get("X-Synthesis-Progress"),u=s.headers.get("X-Synthesis-Complete"),h=null!==d;for(h?(eR.markStreaming(),eH("")):(eR.markOrchestrationStarted(),eH("Continuing synthesis..."),c=setTimeout(()=>{o||(eR.markStreaming(),eH(""))},800));;){let{done:s,value:d}=await a.read();if(s)break;for(let a of r.decode(d,{stream:!0}).split("\n"))if(a.startsWith("data: ")){let r=a.substring(6);if("[DONE]"===r.trim())break;try{var e,t;let a=JSON.parse(r);if(a.choices&&(null==(t=a.choices[0])||null==(e=t.delta)?void 0:e.content)){let e=a.choices[0].delta.content;l+=e,!h&&!o&&(e.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||e.includes("\uD83D\uDCCB **Orchestration Plan:**")||e.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||e.includes("\uD83E\uDD16 **Moderator:**")||e.includes("Specialist:"))?(o=!0,c&&(clearTimeout(c),c=null),eW(e,eR)):!h&&o&&eW(e,eR);let t=i.content[0];t.text=l,V(e=>e.map(e=>e.id===n?{...e,content:[t]}:e))}}catch(e){}}}if(c&&clearTimeout(c),l){let e={...i,content:[{type:"text",text:l}]};h&&"true"!==u&&l.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")?(await tt(ex.id,e),setTimeout(()=>{eF()},1e3)):await tt(ex.id,e)}}}else throw Error("Auto-continuation failed: ".concat(n.status))}catch(t){let e={id:Date.now().toString()+"-error-continue",role:"error",content:[{type:"text",text:"Auto-continuation failed: ".concat(t instanceof Error?t.message:"Unknown error")}]};V(t=>[...t,e])}finally{K(!1),eH(""),eR.markComplete()}}},{chatHistory:eU,isLoading:eq,isStale:eZ,error:eY,refetch:eJ,prefetch:eV,invalidateCache:eX}=(0,f.mx)({configId:B,enablePrefetch:!0,cacheTimeout:3e5,staleTimeout:3e4}),{prefetchChatHistory:eK}=(0,f.l2)(),eG=(0,n.useMemo)(()=>[{id:"explain-concept",title:"Explain a concept",description:"Get clear explanations on any topic",icon:"\uD83D\uDCA1",color:"bg-blue-100 text-blue-700",prompt:"Explain how machine learning works in simple terms"},{id:"write-code",title:"Write code",description:"Generate code snippets and solutions",icon:"\uD83D\uDCBB",color:"bg-green-100 text-green-700",prompt:"Write a Python function that calculates the fibonacci sequence"},{id:"brainstorm-ideas",title:"Brainstorm ideas",description:"Generate creative solutions and ideas",icon:"\uD83E\uDDE0",color:"bg-purple-100 text-purple-700",prompt:"Help me brainstorm innovative features for a mobile app"},{id:"analyze-data",title:"Analyze data",description:"Get insights from data and trends",icon:"\uD83D\uDCCA",color:"bg-amber-100 text-amber-700",prompt:"What are the key trends in artificial intelligence for 2025?"}],[]);(0,n.useEffect)(()=>{let e=async()=>{let e=performance.now();try{F&&await new Promise(e=>setTimeout(e,50));let t=await fetch("/api/custom-configs");if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to fetch configurations")}let a=await t.json();z(a),a.length>0&&W(a[0].id),U(!1),S("Config fetch",e)}catch(t){Q("Failed to load configurations: ".concat(t.message)),z([]),U(!1),S("Config fetch (failed)",e)}};u&&F&&e()},[F,u]);let eQ=e=>new Promise((t,a)=>{let r=document.createElement("canvas"),n=r.getContext("2d");if(!n)return void a(Error("Canvas context not available"));let s=new Image;s.onload=()=>{try{let e,a,i,{width:l,height:o}=s,c=l*o;if(c>8e6?(e=1920,a=1080,i=.6):c>2e6?(e=2560,a=1440,i=.7):(e=3840,a=2160,i=.8),l>e||o>a){let t=Math.min(e/l,a/o);l=Math.floor(l*t),o=Math.floor(o*t)}r.width=l,r.height=o,n.drawImage(s,0,0,l,o);let d=r.toDataURL("image/jpeg",i);URL.revokeObjectURL(s.src),t(d)}catch(e){URL.revokeObjectURL(s.src),a(e)}},s.onerror=()=>{URL.revokeObjectURL(s.src),a(Error("Failed to load image for compression"))},s.src=URL.createObjectURL(e)}),e$=e=>new Promise((t,a)=>{let r=new FileReader;r.readAsDataURL(e),r.onload=()=>t(r.result),r.onerror=e=>a(e)}),e0=async e=>{let t=Array.from(e.target.files||[]);if(0===t.length)return;let a=t.filter(e=>e.size>0x6400000);if(a.length>0)return void Q("Some images are too large. Please use images smaller than 100MB. ".concat(a.length," images were rejected."));let r=es.length,n=t.slice(0,5-r);n.length<t.length&&Q("You can only upload up to 5 images. ".concat(t.length-n.length," images were not added."));try{n.length>1&&Q("Processing ".concat(n.length," images..."));let e=[];for(let t=0;t<n.length;t++){let a=n[t];a.size,n.length>1&&Q("Processing image ".concat(t+1," of ").concat(n.length,"..."));let r=a.type.startsWith("image/")?await eQ(a):await e$(a);r.length,e.push(r)}ei(e=>[...e,...n]),eo(t=>[...t,...e]),Q("")}catch(e){Q("Failed to process one or more images. Please try again.")}ec.current&&(ec.current.value="")},e1=e=>{void 0!==e?(ei(t=>t.filter((t,a)=>a!==e)),eo(t=>t.filter((t,a)=>a!==e))):(ei([]),eo([])),ec.current&&(ec.current.value="")},e2=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];eu.current&&eu.current.scrollTo({top:eu.current.scrollHeight,behavior:e?"smooth":"auto"})},e5=function(){if(arguments.length>0&&void 0!==arguments[0]&&arguments[0],eu.current){let e=eu.current;setTimeout(()=>{let t=e.clientHeight,a=e.scrollHeight-.8*t;e.scrollTo({top:a,behavior:"smooth"}),setTimeout(()=>{Math.abs(e.scrollTop-a)>50&&(e.scrollTop=a)},100),setTimeout(()=>{let t=e.querySelectorAll("[data-message-id]"),a=t[t.length-1];a&&a.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})},200)},100)}},e4=e=>{let t=e.currentTarget;ea(!(t.scrollHeight-t.scrollTop-t.clientHeight<100)&&J.length>0)},e3=(0,n.useMemo)(()=>(J.length,J.length>30)?J.slice(-30):J,[J]),e8=(0,n.useCallback)(e=>{requestAnimationFrame(()=>{e4(e)})},[e4]);(0,n.useEffect)(()=>{J.length>150&&V(J.slice(-150))},[J.length]),(0,n.useEffect)(()=>{if(J.length>0){let e=J[J.length-1];requestAnimationFrame(()=>{(null==e?void 0:e.role)==="user"?(e5(),setTimeout(()=>{if(eu.current){let e=eu.current;e.scrollHeight-e.scrollTop-e.clientHeight<200||e2(!0)}},300)):e2()})}},[J.length]),(0,n.useEffect)(()=>{X&&J.length>0&&requestAnimationFrame(()=>{e2()})},[J,X]),(0,n.useEffect)(()=>{if(X&&J.length>0){let e=J[J.length-1];e&&"assistant"===e.role&&requestAnimationFrame(()=>{e2()})}},[J,X]),(0,n.useEffect)(()=>{let e=setTimeout(()=>{J.length>0&&requestAnimationFrame(()=>{if(eu.current){let e=eu.current;e.scrollHeight-e.scrollTop-e.clientHeight<100&&e2()}})},200);return()=>clearTimeout(e)},[s,o,eh,J.length]),(0,n.useEffect)(()=>{if(B&&P.length>0){let e=P.filter(e=>e.id!==B).slice(0,3),t=setTimeout(()=>{e.forEach(e=>{eK(e.id)})},2e3);return()=>clearTimeout(t)}},[B,P,eK]);let e6=async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t||eb(!0);try{let a=t?J.length:0,r=Date.now(),n=await fetch("/api/chat/messages?conversation_id=".concat(e.id,"&limit=").concat(25,"&offset=").concat(a,"&latest=").concat(!t,"&_cb=").concat(r),{cache:"no-store",headers:{"Cache-Control":"no-cache"}});if(!n.ok)throw Error("Failed to load conversation messages");let s=(await n.json()).map(e=>({id:e.id,role:e.role,content:e.content.map(e=>{var t;return"text"===e.type&&e.text?{type:"text",text:e.text}:"image_url"===e.type&&(null==(t=e.image_url)?void 0:t.url)?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:""}})}));t?V(e=>[...s,...e]):(V(s),ex&&ex.id===e.id||eg(e)),Q(null)}catch(e){Q("Failed to load conversation: ".concat(e.message))}finally{t||eb(!1)}},e7=async()=>{if(!B||0===J.length)return null;try{let e=null==ex?void 0:ex.id;if(!e){let t=J[0],a="New Chat";if(t&&t.content.length>0){let e=t.content.find(e=>"text"===e.type);e&&e.text&&(a=e.text.slice(0,50)+(e.text.length>50?"...":""))}let r={title:a};r.custom_api_config_id=B;let n=await fetch("/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!n.ok)throw Error("Failed to create conversation");let s=await n.json();e=s.id,eg(s)}for(let t of J){if(t.id.includes("-")&&t.id.length>20)continue;let a={conversation_id:e,role:t.role,content:t.content};await fetch("/api/chat/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})}return ex||eJ(!0),e}catch(e){return Q("Failed to save conversation: ".concat(e.message)),null}},e9=async e=>{try{if(!(await fetch("/api/chat/conversations?id=".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete conversation");(null==ex?void 0:ex.id)===e&&(eg(null),V([])),eJ(!0)}catch(e){Q("Failed to delete conversation: ".concat(e.message))}},te=async e=>{if(!B)return null;try{let t="New Chat";if(e.content.length>0){let a=e.content.find(e=>"text"===e.type);a&&a.text&&(t=a.text.slice(0,50)+(a.text.length>50?"...":""))}let a={title:t};a.custom_api_config_id=B;let r=await fetch("/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!r.ok)throw Error("Failed to create conversation");let n=await r.json();return eg(n),n.id}catch(e){return Q("Failed to create conversation: ".concat(e.message)),null}},tt=async(e,t)=>{try{let a={conversation_id:e,role:t.role,content:t.content},r=await fetch("/api/chat/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!r.ok)throw Error("Failed to save message");return await r.json()}catch(e){}},ta=e=>{Y(e),setTimeout(()=>{let e=document.querySelector('textarea[placeholder*="Type a message"]');e&&(e.focus(),e.setSelectionRange(e.value.length,e.value.length))},100)},tr=async()=>{J.length>0&&await e7(),V([]),eg(null),Y(""),Q(null),e1(),eR.reset()},tn=(0,n.useCallback)(async e=>{if(e===B)return;J.length>0&&await tr(),W(e);let t=P.find(t=>t.id===e);t&&t.name},[B,J.length,tr,W,P]),ts=async e=>{eg(e),V([]),Y(""),Q(null),e1();let t=(async()=>{if(J.length>0&&!ex)try{await e7()}catch(e){}})();try{await e6(e)}catch(e){Q("Failed to load conversation: ".concat(e.message))}await t},ti=(e,t)=>{ef(e),ey(t)},tl=()=>{ef(null),ey("")},to=async()=>{if(!ep||!ev.trim()||!B)return;let e=J.findIndex(e=>e.id===ep);if(-1===e)return;let t=[...J];t[e]={...t[e],content:[{type:"text",text:ev.trim()}]};let a=t.slice(0,e+1);if(V(a),ef(null),ey(""),ex)try{if(J.slice(e+1).length>0){let t=J[e];if(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(t.id)){let e=await fetch("/api/chat/messages?conversation_id=".concat(ex.id,"&limit=1&latest=false"));if(e.ok){let a=(await e.json()).find(e=>e.id===t.id);if(a){let e=new Date(a.created_at).getTime(),t=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:ex.id,after_timestamp:e})});t.ok&&await t.json()}}}else{let e=parseInt(t.id)||Date.now(),a=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:ex.id,after_timestamp:e})});a.ok&&await a.json()}}let t=a[e];if(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(t.id)){let e=await fetch("/api/chat/messages?id=".concat(t.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:t.content})});if(e.ok)await e.json();else{let t=await e.text();throw Error("Failed to update message: ".concat(t))}}else await tt(ex.id,t);eJ(!0),Object.keys(localStorage).filter(e=>e.startsWith("chat_")||e.startsWith("conversation_")).forEach(e=>localStorage.removeItem(e))}catch(e){Q("Failed to update conversation: ".concat(e.message))}await tc(a)},tc=async e=>{if(!B||0===e.length)return;K(!0),Q(null),eR.startProcessing();let t={custom_api_config_id:B,messages:e.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let a=e.content[0];t=a&&"text"===a.type?a.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),stream:$,...(null==u?void 0:u.id)&&{_internal_user_id:u.id}};try{var a,r,n,s,i,l,o,c,d;eR.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(t),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||"API Error: ".concat(e.statusText," (Status: ").concat(e.status,")"))}if(eR.analyzeResponseHeaders(e.headers),setTimeout(()=>{$&&eR.markStreaming()},400),$&&e.body){let t=e.body.getReader(),n=new TextDecoder,s=Date.now().toString()+"-assistant",i={id:s,role:"assistant",content:[{type:"text",text:""}]};V(e=>[...e,i]);let l="",o=!1,c=null,d="";for(c=setTimeout(()=>{o||eR.markStreaming()},400);;){let{done:e,value:u}=await t.read();if(e)break;for(let e of n.decode(u,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.substring(6);if("[DONE]"===t.trim())break;try{let e=JSON.parse(t);if("orchestration.progress"===e.object){o=!0,d=e.data.message,c&&(clearTimeout(c),c=null),eR.markOrchestrationStarted(),eH(d);return}if(e.choices&&(null==(r=e.choices[0])||null==(a=r.delta)?void 0:a.content)){let t=e.choices[0].delta.content;l+=t,!o&&(t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:"))&&(o=!0,c&&(clearTimeout(c),c=null),eR.markOrchestrationStarted()),o&&!d&&eW(t,eR);let a=i.content[0];a.text=l,V(e=>e.map(e=>e.id===s?{...e,content:[a]}:e))}}catch(e){}}}if(c&&clearTimeout(c),l&&ex){let e={...i,content:[{type:"text",text:l}]};l.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")||l.includes("*The response will continue automatically in a new message...*")?(await tt(ex.id,e),setTimeout(()=>{eF()},2e3)):await tt(ex.id,e)}}else{let t=await e.json(),a="Could not parse assistant's response.";(null==(i=t.choices)||null==(s=i[0])||null==(n=s.message)?void 0:n.content)?a=t.choices[0].message.content:(null==(o=t.content)||null==(l=o[0])?void 0:l.text)?a=t.content[0].text:"string"==typeof t.text&&(a=t.text);let r=(null==(c=t.rokey_metadata)?void 0:c.routing_strategy)==="multi_role_orchestration",u=null==(d=t.rokey_metadata)?void 0:d.orchestration_progress;if(r&&u){eR.markOrchestrationStarted();let e=0,t=()=>{if(e<u.length){let a=u[e];eH(a),eR.updateOrchestrationStatus(a),e++,setTimeout(t,800)}else setTimeout(()=>{eH(""),eR.markComplete()},500)};t()}let h={id:Date.now().toString()+"-assistant",role:"assistant",content:[{type:"text",text:a}]};V(e=>[...e,h]),ex&&await tt(ex.id,h),r||eR.markComplete()}}catch(t){let e={id:Date.now().toString()+"-error",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred."}]};V(t=>[...t,e]),Q(t.message)}finally{K(!1),eR.markComplete()}},td=async(e,t)=>{if(!B||e<0||e>=J.length||"assistant"!==J[e].role)return;K(!0),Q(null),eH(""),eR.startProcessing();let a=J.slice(0,e);if(V(a),ex)try{if(J.slice(e).length>0){let t=J[e],a=parseInt(t.id)||Date.now(),r=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:ex.id,from_timestamp:a})});r.ok&&await r.json()}eJ(!0)}catch(e){}let r={custom_api_config_id:B,messages:a.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let a=e.content[0];t=a&&"text"===a.type?a.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),stream:$,...t&&{specific_api_key_id:t},...(null==u?void 0:u.id)&&{_internal_user_id:u.id}};try{var n,s,i,l,o;eR.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(r),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||"HTTP ".concat(e.status,": ").concat(e.statusText))}eR.analyzeResponseHeaders(e.headers);let t=e.headers.get("X-RoKey-Orchestration-ID"),a=e.headers.get("X-RoKey-Orchestration-Active");if(t&&"true"===a&&(eN(t),ek(!0),eT(!1)),setTimeout(()=>{$&&eR.markStreaming()},400),$&&e.body){let t=e.body.getReader(),a=new TextDecoder,r="",l={id:Date.now().toString()+"-assistant-retry",role:"assistant",content:[{type:"text",text:""}]};V(e=>[...e,l]);try{for(;;){let{done:e,value:o}=await t.read();if(e)break;for(let e of a.decode(o,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{let e=JSON.parse(t);if(null==(i=e.choices)||null==(s=i[0])||null==(n=s.delta)?void 0:n.content){let t=e.choices[0].delta.content;r+=t,t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:")||t.includes("\uD83E\uDD16 **Agent Mode")||t.includes("\uD83E\uDDE0 **Complexity")||t.includes("\uD83D\uDC65 **Agent")||t.includes("⚡ **Selecting")?(eR.markOrchestrationStarted(),eW(t,eR)):eI&&eW(t,eR),V(e=>e.map(e=>e.id===l.id?{...e,content:[{type:"text",text:r}]}:e))}}catch(e){}}}}finally{t.releaseLock()}if(r&&ex){let e={...l,content:[{type:"text",text:r}]};await tt(ex.id,e)}}else{let t=await e.json(),a="";t.choices&&t.choices.length>0&&t.choices[0].message?a=t.choices[0].message.content:t.content&&Array.isArray(t.content)&&t.content.length>0&&(a=t.content[0].text);let r=(null==(l=t.rokey_metadata)?void 0:l.routing_strategy)==="multi_role_orchestration",n=null==(o=t.rokey_metadata)?void 0:o.orchestration_progress;if(r&&n){eR.markOrchestrationStarted();let e=0,t=()=>{if(e<n.length){let a=n[e];eH(a),eR.updateOrchestrationStatus(a),e++,setTimeout(t,800)}else setTimeout(()=>{eH(""),eR.markComplete()},500)};t()}let s={id:Date.now().toString()+"-assistant-retry",role:"assistant",content:[{type:"text",text:a}]};V(e=>[...e,s]),ex&&await tt(ex.id,s),r||eR.markComplete()}}catch(t){let e={id:Date.now().toString()+"-error-retry",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred during retry."}]};V(t=>[...t,e]),Q(t.message),ex&&await tt(ex.id,e)}finally{K(!1),eR.markComplete()}},tu=(0,n.useCallback)(async e=>{let t;if(e&&e.preventDefault(),!Z.trim()&&0===es.length||!B)return;if("continue"===Z.trim().toLowerCase()&&J.length>0){Y(""),await eF();return}K(!0),Q(null),eH("");let a=new AbortController;en(a),eR.startProcessing(),performance.now();let r=Z.trim(),n=[...es],s=[...el];Y(""),e1();let i=[],l=[];if(r&&(i.push({type:"text",text:r}),l.push({type:"text",text:r})),n.length>0)try{for(let e=0;e<n.length;e++){let t=n[e],a=s[e],r=t.type.startsWith("image/")?await eQ(t):await e$(t);i.push({type:"image_url",image_url:{url:a}}),l.push({type:"image_url",image_url:{url:r}})}}catch(e){Q("Failed to process one or more images. Please try again."),K(!1),Y(r),ei(n),eo(s);return}let o={id:Date.now().toString(),role:"user",content:i};V(e=>[...e,o]);let c=null==ex?void 0:ex.id,d=Promise.resolve(c||null);Promise.resolve(),c||ex||(d=te(o)),d.then(async e=>{e&&await tt(e,o)}).catch(e=>{});let h=J.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let a=e.content[0];t=a&&"text"===a.type?a.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),m={"Content-Type":"application/json"};{m.Authorization="Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13");let e=null==u?void 0:u.id;t={custom_api_config_id:B,messages:[...h,{role:"user",content:1===l.length&&"text"===l[0].type?l[0].text:l}],stream:$,...e?{_internal_user_id:e}:{}}}try{var x,g,p,f,y,w,b,j,N,S;if(eR.updateStage("connecting"),performance.now(),null==(x=t.messages)?void 0:x.some(e=>Array.isArray(e.content)&&e.content.some(e=>"image_url"===e.type))){let e=JSON.stringify(t).length;if(t.messages.reduce((e,t)=>Array.isArray(t.content)?e+t.content.filter(e=>"image_url"===e.type).length:e,0),e>0x1e00000)throw Error("Request payload too large (".concat((e/1048576).toFixed(2),"MB). Please use smaller images or fewer images."))}let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:m,body:JSON.stringify(t),cache:"no-store",signal:a.signal});if(performance.now(),!e.ok){let t;try{t=await e.json()}catch(a){let t=await e.text().catch(()=>"Unknown error");throw Error("API Error: ".concat(e.statusText," (Status: ").concat(e.status,") - ").concat(t))}throw Error(t.error||"API Error: ".concat(e.statusText," (Status: ").concat(e.status,")"))}eR.analyzeResponseHeaders(e.headers);let r=e.headers.get("X-RoKey-Orchestration-ID"),n=e.headers.get("X-RoKey-Orchestration-Active");if(r&&"true"===n&&(eN(r),ek(!0),eT(!1)),$&&e.body){let t=e.body.getReader(),a=new TextDecoder,r=Date.now().toString()+"-assistant",n={id:r,role:"assistant",content:[{type:"text",text:""}]};V(e=>[...e,n]);let s="",i=!1,l=null,o="";for(l=setTimeout(()=>{i||eR.markStreaming()},400);;){let{done:e,value:c}=await t.read();if(e)break;for(let e of a.decode(c,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.substring(6);if("[DONE]"===t.trim())break;try{let e=JSON.parse(t);if("orchestration.progress"===e.object){i=!0,o=e.data.message,l&&(clearTimeout(l),l=null),eR.markOrchestrationStarted(),eH(o);continue}if("browsing.progress"===e.object){eB(!0),eP(e.data);continue}if(e.result,e.choices&&(null==(p=e.choices[0])||null==(g=p.delta)?void 0:g.content)){let t=e.choices[0].delta.content;s+=t,!i&&(t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:"))&&(i=!0,l&&(clearTimeout(l),l=null),eR.markOrchestrationStarted()),i&&!o&&eW(t,eR);let a=n.content[0];a.text=s,V(e=>e.map(e=>e.id===r?{...e,content:[a]}:e))}}catch(e){}}}if(l&&clearTimeout(l),s){let t={...n,content:[{type:"text",text:s}]},a=e.headers.get("X-Synthesis-Progress");e.headers.get("X-Synthesis-Complete"),s.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")||s.includes("*The response will continue automatically in a new message...*")?(d.then(async e=>{e&&await tt(e,t)}),setTimeout(()=>{eF()},null!==a?1e3:2e3)):d.then(async e=>{e&&await tt(e,t)})}}else{let t=await e.json(),a="Could not parse assistant's response.";(null==(w=t.choices)||null==(y=w[0])||null==(f=y.message)?void 0:f.content)?a=t.choices[0].message.content:(null==(j=t.content)||null==(b=j[0])?void 0:b.text)?a=t.content[0].text:"string"==typeof t.text&&(a=t.text);let r=(null==(N=t.rokey_metadata)?void 0:N.routing_strategy)==="multi_role_orchestration",n=null==(S=t.rokey_metadata)?void 0:S.orchestration_progress;if(r&&n){eR.markOrchestrationStarted();let e=0,t=()=>{if(e<n.length){let a=n[e];eH(a),eR.updateOrchestrationStatus(a),e++,setTimeout(t,800)}else setTimeout(()=>{eH(""),eR.markComplete()},500)};t()}let s={id:Date.now().toString()+"-assistant",role:"assistant",content:[{type:"text",text:a}]};V(e=>[...e,s]),d.then(async e=>{e&&await tt(e,s)}),r||eR.markComplete()}}catch(e){if("AbortError"===e.name){let e={id:Date.now().toString()+"-stopped",role:"system",content:[{type:"text",text:"⏹️ Request stopped by user"}]};V(t=>[...t,e]),Q(null)}else{let t={id:Date.now().toString()+"-error",role:"error",content:[{type:"text",text:e.message||"An unexpected error occurred."}]};V(e=>[...e,t]),Q(e.message),d.then(async e=>{e&&await tt(e,t)}).catch(e=>{})}}finally{K(!1),en(null),eR.markComplete(),(0,v.n4)(eR.stageHistory),performance.now(),d.then(async e=>{e&&!ex&&eJ(!0)}).catch(e=>{})}},[Z,es,B,X,null==ex?void 0:ex.id,J.length,$]);return(0,r.jsxs)("div",{className:"min-h-screen bg-[#040716] flex relative",children:[(0,r.jsxs)("div",{className:"flex-1 flex flex-col transition-all duration-300 ease-in-out ".concat(C?"":"w-full"),style:{marginLeft:C?"".concat(H,"px"):"0px",marginRight:C?eE&&!eA?"50%":eh?"0px":"320px":"0px"},children:[(0,r.jsx)("div",{className:"fixed top-0 z-40 bg-[#040716]/95 backdrop-blur-sm border-b border-gray-800/50 transition-all duration-300 ease-in-out",style:{left:C?"".concat(H,"px"):"0px",right:C?eE&&!eA?"50%":eh?"0px":"320px":"0px"},children:(0,r.jsx)("div",{className:"px-4 sm:px-6 py-2 ".concat(C?"":"px-3"),children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[!C&&(0,r.jsx)("button",{onClick:L,className:"p-2 text-gray-400 hover:text-white transition-colors lg:hidden touch-friendly","aria-label":"Toggle sidebar",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),!C&&(0,r.jsx)("button",{onClick:M,className:"p-2 text-gray-400 hover:text-white transition-colors lg:hidden touch-friendly","aria-label":"Toggle history",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("div",{className:"flex items-center space-x-2 ".concat(C?"":"hidden"),children:B?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:"Connected"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-400",children:"Not Connected"})]})}),(0,r.jsxs)("div",{className:"relative flex-1 max-w-xs sm:max-w-sm",children:[(0,r.jsxs)("select",{value:B,onChange:e=>tn(e.target.value),disabled:0===P.length,className:"appearance-none px-3 py-2.5 pr-8 bg-gray-800/50 border border-gray-700/50 rounded-xl text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-gray-600 transition-all duration-200 shadow-sm hover:shadow-md w-full ".concat(C?"min-w-[200px]":"text-xs px-2 py-2"),children:[(0,r.jsx)("option",{value:"",children:"Select Router Configuration"}),P.length>0&&(0,r.jsx)("optgroup",{label:"\uD83D\uDD00 Router Configurations",children:P.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))})]}),(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:"Streaming"}),(0,r.jsx)("button",{onClick:()=>ee(!$),className:"relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20 shadow-sm ".concat($?"bg-blue-500 shadow-blue-200":"bg-gray-600"),children:(0,r.jsx)("span",{className:"inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm ".concat($?"translate-x-6":"translate-x-1")})})]})]})})}),(0,r.jsx)("div",{className:"flex-1 flex flex-col pt-0 pb-32",children:0!==J.length||ex?(0,r.jsxs)("div",{className:"flex-1 relative ".concat(eE&&!eA?"overflow-visible":"overflow-hidden"),children:[(0,r.jsx)("div",{className:"h-full flex ".concat(eE&&!eA?"justify-start":"justify-center"),children:(0,r.jsx)("div",{ref:eu,className:"w-full h-full overflow-y-auto px-6 transition-all duration-300 ".concat(eE&&!eA?"max-w-2xl -ml-32":"max-w-3xl"),onScroll:e8,children:(0,r.jsxs)("div",{className:"pt-0 pb-80",children:[ex&&J.length>=50&&(0,r.jsx)("div",{className:"text-center py-4",children:(0,r.jsx)("button",{onClick:()=>e6(ex,!0),disabled:eq,className:"px-4 py-2 text-sm text-blue-400 hover:text-blue-300 hover:bg-blue-500/20 rounded-lg transition-colors duration-200 disabled:opacity-50",children:eq?"Loading...":"Load Earlier Messages"})}),ew&&0===J.length&&(0,r.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((e,t)=>(0,r.jsxs)("div",{className:"flex justify-start",children:[(0,r.jsx)("div",{className:"w-7 h-7 rounded-full bg-gray-200 animate-pulse mr-3 mt-1 flex-shrink-0"}),(0,r.jsx)("div",{className:"max-w-[65%] bg-gray-100 rounded-2xl rounded-bl-lg px-4 py-3 animate-pulse",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]})})]},t))}),J.length>100&&(0,r.jsx)("div",{className:"text-center py-4",children:(0,r.jsxs)("div",{className:"text-sm text-gray-400 bg-white/10 rounded-lg px-4 py-2 inline-block",children:["Showing last 50 of ",J.length," messages for better performance"]})}),e3.map((e,t)=>(0,r.jsx)(N,{msg:e,index:t,visibleMessages:e3,isCanvasOpen:eE,isCanvasMinimized:eA,editingMessageId:ep,editingText:ev,setEditingText:ey,saveEditedMessage:to,cancelEditingMessage:tl,startEditingMessage:ti,handleRetryMessage:td,selectedConfigId:B,isLoading:X},e.id)),eS&&ej&&eA&&(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)(g.f,{orchestrationComplete:eC,onMaximize:()=>{eM(!0),setTimeout(()=>eM(!1),100)},isCanvasOpen:eE,isCanvasMinimized:eA})}),X&&(0,r.jsx)("div",{className:"flex justify-start group",children:(0,r.jsx)(x.A,{currentStage:eR.currentStage,isStreaming:$&&"typing"===eR.currentStage,orchestrationStatus:eI,onStageChange:e=>{}})}),eS&&ej&&(0,r.jsx)(n.Suspense,{fallback:(0,r.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"}),(0,r.jsx)("span",{className:"ml-3 text-gray-600",children:"Loading orchestration canvas..."})]}),children:(0,r.jsx)(j,{executionId:ej,onCanvasStateChange:(e,t)=>{e_(e),eO(t),e&&!t&&em(!0)},forceMaximize:eL,onComplete:e=>{if(null==ej?void 0:ej.startsWith("test-execution-id"))return void eT(!0);eT(!0);let t={id:Date.now().toString()+"-orchestration-final",role:"assistant",content:[{type:"text",text:e}]};V(e=>[...e,t]),ek(!1),eN(null),eT(!1),(null==ex?void 0:ex.id)&&tt(ex.id,t).catch(e=>{})},onError:e=>{null!=ej&&ej.startsWith("test-execution-id")||(Q("Orchestration error: ".concat(e)),ek(!1),eN(null))}})}),(0,r.jsx)("div",{ref:ed})]})})}),et&&(0,r.jsx)("div",{className:"absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10",children:(0,r.jsx)("button",{onClick:()=>e2(!0),className:"w-12 h-12 bg-gray-800/50 rounded-full shadow-lg border border-gray-700/50 flex items-center justify-center hover:shadow-xl transition-all duration-200 hover:scale-105 group","aria-label":"Scroll to bottom",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-gray-400 group-hover:text-blue-400 transition-colors",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})})})]}):(0,r.jsx)("div",{className:"fixed inset-0 flex items-center justify-center px-4 sm:px-6",style:{top:C?"80px":"70px",left:C?H:"0px",right:C?eE&&!eA?"50%":eh?"0px":"320px":"0px",bottom:C?"120px":"140px"},children:(0,r.jsx)("div",{className:"w-full mx-auto transition-all duration-300 ".concat(eE&&!eA?"max-w-2xl":"max-w-3xl"),children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsxs)("div",{className:"text-center mb-8 sm:mb-12",children:[(0,r.jsx)("h1",{className:"font-bold mb-4 ".concat(C?"text-4xl":"text-2xl sm:text-3xl"),children:(0,r.jsxs)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400",children:["Hello",D?" ".concat(D):""]})}),(0,r.jsx)("p",{className:"text-gray-400 max-w-md mx-auto ".concat(C?"text-lg":"text-sm sm:text-base"),children:"Select a router configuration and explore RouKey's intelligent routing capabilities below."})]}),(0,r.jsx)("div",{className:"grid gap-3 sm:gap-4 w-full max-w-2xl ".concat(C?"grid-cols-2":"grid-cols-1 sm:grid-cols-2"),children:eG.map(e=>(0,r.jsxs)("button",{onClick:()=>ta(e.prompt),disabled:!B,className:"group relative p-6 bg-gray-800/30 rounded-2xl border border-gray-700/50 hover:border-gray-600/70 hover:bg-gray-800/50 transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed ".concat(B?"cursor-pointer hover:scale-[1.02]":"cursor-not-allowed"),children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-xl flex items-center justify-center text-xl bg-white/10 text-white group-hover:scale-110 transition-transform duration-200",children:e.icon}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"font-semibold text-white mb-1 group-hover:text-blue-400 transition-colors",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-400 leading-relaxed",children:e.description})]})]}),(0,r.jsx)("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4l8 8-8 8M4 12h16"})})})]},e.id))})]})})})}),(0,r.jsx)("div",{className:"fixed bottom-0 z-50 transition-all duration-300 ease-in-out bg-gradient-to-t from-[#040716] via-[#040716]/95 to-transparent",style:{left:C?H:"0px",right:C?eE&&!eA?"50%":eh?"0px":"320px":"0px"},children:(0,r.jsx)("div",{className:"px-4 sm:px-6 pt-16 sm:pt-24 pb-2 flex justify-center ".concat(C?"":"pb-4"),children:(0,r.jsxs)("div",{className:"w-full transition-all duration-300 ".concat(eE&&!eA?"max-w-2xl":"max-w-3xl"),children:[G&&(0,r.jsx)("div",{className:"mb-4 bg-red-500/20 border border-red-500/30 rounded-2xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsx)("p",{className:"text-red-800 text-sm font-medium",children:G})]})}),(0,r.jsxs)("form",{onSubmit:tu,children:[el.length>0&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-white",children:[el.length," image",el.length>1?"s":""," attached"]})]}),(0,r.jsx)("button",{type:"button",onClick:()=>e1(),className:"text-xs text-gray-400 hover:text-red-400 transition-colors duration-200 font-medium",children:"Clear all"})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3",children:el.map((e,t)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsxs)("div",{className:"relative overflow-hidden rounded-xl border-2 border-gray-700/50 bg-gray-800/30 shadow-sm hover:shadow-md transition-all duration-200 aspect-square",children:[(0,r.jsx)("img",{src:e,alt:"Preview ".concat(t+1),className:"w-full h-full object-cover"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200"}),(0,r.jsx)("button",{type:"button",onClick:()=>e1(t),className:"absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100","aria-label":"Remove image ".concat(t+1),children:(0,r.jsx)(c,{className:"w-3.5 h-3.5"})})]}),(0,r.jsx)("div",{className:"absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-md font-medium",children:t+1})]},t))})]}),(0,r.jsx)("div",{className:"relative bg-gray-800/30 rounded-2xl border border-gray-700/50 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-blue-500/20 focus-within:border-gray-600",children:(0,r.jsxs)("div",{className:"flex items-end p-4 space-x-3",children:[(0,r.jsx)("input",{type:"file",accept:"image/*",multiple:!0,onChange:e0,ref:ec,className:"hidden",id:"imageUpload"}),(0,r.jsxs)("button",{type:"button",onClick:()=>{var e;return null==(e=ec.current)?void 0:e.click()},disabled:es.length>=5,className:"relative p-2 rounded-xl transition-all duration-200 flex-shrink-0 ".concat(es.length>=5?"text-gray-600 cursor-not-allowed":"text-gray-400 hover:text-blue-400 hover:bg-white/10"),"aria-label":es.length>=5?"Maximum 5 images reached":"Attach images",title:es.length>=5?"Maximum 5 images reached":"Attach images (up to 5, up to 100MB each, auto-compressed)",children:[(0,r.jsx)(l,{className:"w-5 h-5"}),es.length>0&&(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold",children:es.length})]}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("textarea",{value:Z,onChange:e=>Y(e.target.value),placeholder:B?"Type a message...":"Select a router configuration first",disabled:!B||X,rows:1,className:"w-full px-0 py-2 bg-transparent border-0 text-white placeholder-gray-500 focus:outline-none focus-visible:outline-none focus:border-none focus:ring-0 disabled:opacity-50 resize-none text-base leading-relaxed [&:focus]:outline-none [&:focus-visible]:outline-none [&:focus]:border-none [&:focus]:ring-0 [&:focus]:shadow-none [&:active]:outline-none [&:active]:border-none [&:active]:ring-0 [&:active]:shadow-none",onKeyDown:e=>{"Enter"===e.key&&!e.shiftKey&&(e.preventDefault(),(Z.trim()||es.length>0)&&B&&!X&&tu())},style:{minHeight:"24px",maxHeight:"120px",outline:"none !important",border:"none !important",boxShadow:"none !important",WebkitAppearance:"none",MozAppearance:"none"},onInput:e=>{let t=e.target;t.style.height="auto",t.style.height=Math.min(t.scrollHeight,120)+"px"}})}),(0,r.jsx)("button",{type:X?"button":"submit",onClick:X?()=>{er&&(er.abort(),en(null)),K(!1),eH(""),eR.reset()}:void 0,disabled:!B||!X&&!Z.trim()&&0===es.length,className:"p-2.5 ".concat(X?"bg-red-500 hover:bg-red-600":"bg-blue-500 hover:bg-blue-600"," disabled:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl flex-shrink-0"),"aria-label":X?"Stop request":"Send message",title:X?"Stop request":"Send message",children:X?(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 6h12v12H6z"})}):(0,r.jsx)(i,{className:"w-5 h-5"})})]})})]})]})})})]}),(0,r.jsx)("div",{className:"fixed top-0 right-0 h-full bg-[#030614] border-l border-gray-800/50 shadow-xl transition-all duration-300 ease-in-out z-30 ".concat(C?eh?"w-0 overflow-hidden":"w-80":O?"w-full":"w-0 overflow-hidden"),style:{transform:C?eh?"translateX(100%)":"translateX(0)":O?"translateX(0)":"translateX(100%)",opacity:C?+!eh:+!!O},children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-800/50",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"font-semibold text-white",children:"History"}),(0,r.jsxs)("p",{className:"text-xs text-gray-400",children:[eU.length," conversations"]})]})]}),(0,r.jsx)("button",{onClick:()=>C?em(!eh):M(),className:"p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200 hover:scale-105 touch-friendly","aria-label":"Toggle history sidebar",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsx)("div",{className:"p-4 border-b border-gray-800/50",children:(0,r.jsxs)("button",{onClick:tr,className:"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl transition-all duration-200 shadow-sm hover:shadow-md",children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),(0,r.jsx)("span",{className:"font-medium",children:"New Chat"})]})}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-4 space-y-2",children:eq?(0,r.jsx)("div",{className:"space-y-2 p-4",children:Array.from({length:8}).map((e,t)=>(0,r.jsxs)("div",{className:"p-3 rounded-xl border border-gray-700/50 animate-pulse",children:[(0,r.jsx)("div",{className:"bg-gray-700 h-4 w-3/4 rounded mb-2"}),(0,r.jsx)("div",{className:"bg-gray-700 h-3 w-1/2 rounded"})]},t))}):0===eU.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-white/10 rounded-xl flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"No conversations yet"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Start chatting to see your history"})]}):(0,r.jsx)(r.Fragment,{children:eU.map(e=>(0,r.jsx)(k,{chat:e,currentConversation:ex,onLoadChat:ts,onDeleteChat:e9},e.id))})}),eZ&&(0,r.jsx)("div",{className:"px-4 py-2 bg-orange-500/20 border-t border-orange-500/30",children:(0,r.jsxs)("div",{className:"flex items-center text-xs text-orange-600",children:[(0,r.jsx)("svg",{className:"w-3 h-3 mr-1 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Updating..."]})}),eY&&(0,r.jsx)("div",{className:"px-4 py-2 bg-red-500/20 border-t border-red-500/30",children:(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-red-600",children:[(0,r.jsx)("span",{children:"Failed to load history"}),(0,r.jsx)("button",{onClick:()=>eJ(!0),className:"text-red-700 hover:text-red-800 font-medium",children:"Retry"})]})})]})}),!C&&(A||O)&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 z-20 transition-opacity duration-300",onClick:R,"aria-label":"Close sidebar"}),C&&(0,r.jsx)("div",{className:"fixed top-20 right-4 z-40 transition-all duration-300 ease-in-out ".concat(eh?"opacity-100 scale-100 translate-x-0":"opacity-0 scale-95 translate-x-4 pointer-events-none"),children:(0,r.jsx)("button",{onClick:()=>em(!1),className:"p-3 bg-gray-800/50 border border-gray-700/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-gray-400 hover:text-blue-400 hover:scale-105","aria-label":"Show history sidebar",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})})})]})}k.displayName="ChatHistoryItem"}},e=>{var t=t=>e(e.s=t);e.O(0,[8888,1459,5738,9968,6060,4755,563,2662,8669,4703,3269,9173,9219,4288,7706,7544,1142,2993,1561,9248,6761,7358],()=>t(24766)),_N_E=e.O()}]);