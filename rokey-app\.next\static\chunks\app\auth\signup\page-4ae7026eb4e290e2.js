(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2778],{11387:(e,a,t)=>{Promise.resolve().then(t.bind(t,11730))},11730:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>p});var s=t(95155),r=t(12115),l=t(6874),n=t.n(l),i=t(66766),o=t(29337),c=t(10184),d=t(48987),m=t(52643),u=t(35695),x=t(64198);function h(){let[e,a]=(0,r.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:""}),[t,l]=(0,r.useState)(!1),[h,p]=(0,r.useState)(!1),[g,f]=(0,r.useState)(!1),[b,y]=(0,r.useState)(""),[N,w]=(0,r.useState)(!1),j=(0,u.useRouter)(),v=(0,u.useSearchParams)(),P=(0,m.createSupabaseBrowserClient)(),{success:k,error:C}=(0,x.dj)(),S=v.get("plan")||"free";(0,r.useEffect)(()=>{},[S]);let A=e=>{a(a=>({...a,[e.target.name]:e.target.value}))},R=async a=>{if(a.preventDefault(),f(!0),y(""),e.password!==e.confirmPassword){y("Passwords do not match"),f(!1);return}if(e.password.length<8){y("Password must be at least 8 characters long"),f(!1);return}if(!N){y("Please agree to the Terms of Service and Privacy Policy"),f(!1);return}try{let a={exists:!1};try{let t=await fetch("/api/auth/check-pending-payment",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e.email})});if(t.ok){let e=await t.text();try{a=JSON.parse(e)}catch(e){y("Server error: Invalid response format. Please try again."),f(!1);return}}else{await t.text(),y("Server error (".concat(t.status,"): Please try again.")),f(!1);return}}catch(e){y("Network error: Please check your connection and try again."),f(!1);return}if(a.exists){if("hasPendingPayment"in a&&a.hasPendingPayment){k("Welcome back! Redirecting to complete your checkout...");let t="/checkout?plan=".concat(a.plan,"&user_id=").concat(a.userId,"&email=").concat(encodeURIComponent(e.email));j.push(t);return}else if("hasActiveSubscription"in a&&a.hasActiveSubscription){let{data:a,error:t}=await P.auth.signInWithPassword({email:e.email,password:e.password});if(t){y("An account with this email already exists. Please sign in instead or use a different email."),f(!1);return}if(a.user){k("Welcome back! You have been signed in to your existing account."),j.push("/dashboard");return}}}if("free"===S){let a=await fetch("/api/auth/free-signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e.email,password:e.password,fullName:"".concat(e.firstName," ").concat(e.lastName)})}),t=await a.json();if(!a.ok){y(t.error||"Failed to create account"),f(!1);return}let{data:s,error:r}=await P.auth.signInWithPassword({email:e.email,password:e.password});if(r){y("Account created but failed to sign in. Please try signing in manually."),f(!1);return}k("Account created successfully! Welcome to RouKey."),j.push("/dashboard")}else{let a=await fetch("/api/auth/paid-signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e.email,password:e.password,firstName:e.firstName,lastName:e.lastName,plan:S})}),t=await a.json();if(!a.ok){y(t.error||"Failed to create account"),f(!1);return}k("Account created! Redirecting to complete your payment...");let s="/checkout?plan=".concat(S,"&user_id=").concat(t.user.id,"&email=").concat(encodeURIComponent(e.email));j.push(s)}}catch(e){y("An unexpected error occurred. Please try again.")}finally{f(!1)}},O=[{text:"At least 8 characters",met:e.password.length>=8},{text:"Contains uppercase letter",met:/[A-Z]/.test(e.password)},{text:"Contains lowercase letter",met:/[a-z]/.test(e.password)},{text:"Contains number",met:/\d/.test(e.password)}];return(0,s.jsxs)("div",{className:"min-h-screen flex bg-gradient-to-br from-[#1C051C] to-[#040716]",children:[(0,s.jsxs)("div",{className:"hidden lg:flex lg:w-1/2 items-center justify-center p-12 relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.15]",style:{backgroundImage:"linear-gradient(rgba(255, 255, 255, 0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.3) 1px, transparent 1px)",backgroundSize:"40px 40px",maskImage:"radial-gradient(ellipse at center, black 30%, transparent 80%)",WebkitMaskImage:"radial-gradient(ellipse at center, black 30%, transparent 80%)"}}),(0,s.jsxs)("div",{className:"max-w-lg relative z-10",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)(n(),{href:"/",className:"inline-flex items-center space-x-3",children:[(0,s.jsx)(i.default,{src:"/RouKey_Logo_GLOW.png",alt:"RouKey",width:48,height:48,className:"w-12 h-12"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-white",children:"RouKey"})]})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-white leading-tight",children:"Welcome to RouKey!"}),(0,s.jsx)("p",{className:"text-xl text-white/80 leading-relaxed",children:"Create your account and start building with our powerful routing platform."}),(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsxs)("p",{className:"text-white/60 text-sm",children:["Already have an account?"," ",(0,s.jsx)(n(),{href:"/auth/signin",className:"text-white font-medium hover:text-white/80 transition-colors",children:"Sign in here"})]})})]})]})]}),(0,s.jsx)("div",{className:"flex-1 flex items-center justify-center p-6 bg-white",children:(0,s.jsxs)("div",{className:"w-full max-w-md space-y-4",children:[(0,s.jsx)("div",{className:"lg:hidden text-center",children:(0,s.jsxs)(n(),{href:"/",className:"inline-flex items-center space-x-2",children:[(0,s.jsx)(i.default,{src:"/RouKey_Logo_NOGLOW.png",alt:"RouKey",width:32,height:32,className:"w-8 h-8"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"RouKey"})]})}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Sign up"}),(0,s.jsxs)("p",{className:"mt-1 text-gray-600 text-sm",children:["Create your account for the"," ",(0,s.jsx)("span",{className:"text-pink-600 font-semibold capitalize",children:S})," ","plan"]})]}),S&&"free"!==S&&(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("h3",{className:"text-sm font-semibold text-blue-900 capitalize",children:[S," Plan Selected"]}),(0,s.jsxs)("p",{className:"text-blue-700 text-xs mt-1",children:["starter"===S&&"$24/month - Perfect for small teams","professional"===S&&"$60/month - Advanced features included","enterprise"===S&&"$170/month - Full enterprise solution"]})]})}),b&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,s.jsx)("p",{className:"text-red-600 text-sm",children:b})}),(0,s.jsxs)("form",{onSubmit:R,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-2",children:"First name"}),(0,s.jsx)("input",{id:"firstName",name:"firstName",type:"text",autoComplete:"given-name",required:!0,value:e.firstName,onChange:A,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"First name"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Last name"}),(0,s.jsx)("input",{id:"lastName",name:"lastName",type:"text",autoComplete:"family-name",required:!0,value:e.lastName,onChange:A,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Last name"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email address"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:A,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Enter your email"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"password",name:"password",type:t?"text":"password",autoComplete:"new-password",required:!0,value:e.password,onChange:A,className:"w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Create a password"}),(0,s.jsx)("button",{type:"button",onClick:()=>l(!t),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:t?(0,s.jsx)(d.A,{className:"h-5 w-5"}):(0,s.jsx)(c.A,{className:"h-5 w-5"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:h?"text":"password",autoComplete:"new-password",required:!0,value:e.confirmPassword,onChange:A,className:"w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Confirm your password"}),(0,s.jsx)("button",{type:"button",onClick:()=>p(!h),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:h?(0,s.jsx)(d.A,{className:"h-5 w-5"}):(0,s.jsx)(c.A,{className:"h-5 w-5"})})]})]}),e.password&&(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"Password requirements:"}),(0,s.jsx)("div",{className:"space-y-2",children:O.map((e,a)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(o.A,{className:"h-4 w-4 ".concat(e.met?"text-green-500":"text-gray-300")}),(0,s.jsx)("span",{className:"text-sm ".concat(e.met?"text-green-700":"text-gray-500"),children:e.text})]},a))})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("input",{id:"terms",type:"checkbox",checked:N,onChange:e=>w(e.target.checked),className:"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsxs)("label",{htmlFor:"terms",className:"text-sm text-gray-600",children:["I agree to the"," ",(0,s.jsx)(n(),{href:"/terms",className:"text-blue-600 hover:text-blue-500",children:"Terms of Service"})," ","and"," ",(0,s.jsx)(n(),{href:"/privacy",className:"text-blue-600 hover:text-blue-500",children:"Privacy Policy"})]})]}),(0,s.jsx)("button",{type:"submit",disabled:g,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:g?"Creating account...":"Create account"})]}),(0,s.jsx)("div",{className:"text-center lg:hidden",children:(0,s.jsxs)("p",{className:"text-gray-600 text-sm",children:["Already have an account?"," ",(0,s.jsx)(n(),{href:"/auth/signin",className:"text-blue-600 font-medium hover:text-blue-500",children:"Sign in"})]})})]})})]})}function p(){return(0,s.jsx)(r.Suspense,{fallback:(0,s.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,s.jsx)(h,{})})}},21884:(e,a,t)=>{"use strict";t.d(a,{C1:()=>s.A,KS:()=>l.A,Pi:()=>r.A,fK:()=>i.A,qh:()=>n.A});var s=t(6865),r=t(55628),l=t(67695),n=t(52589),i=t(74500)}},e=>{var a=a=>e(e.s=a);e.O(0,[8888,1459,5738,9968,6060,4755,563,2662,8669,4703,3269,9173,9219,4288,7706,7544,1142,2993,1561,9248,6761,7358],()=>a(11387)),_N_E=e.O()}]);