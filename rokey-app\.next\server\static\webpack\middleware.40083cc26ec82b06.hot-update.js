"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(req) {\n    let res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n        request: {\n            headers: req.headers\n        }\n    });\n    // Skip middleware for static files, API routes, and Next.js internal routes\n    const pathname = req.nextUrl.pathname;\n    if (pathname.startsWith('/_next/static') || pathname.startsWith('/_next/image') || pathname.startsWith('/favicon.ico') || pathname.startsWith('/public/') || pathname.includes('.')) {\n        return res;\n    }\n    // Block workflow-related API routes (temporarily disabled for launch)\n    if (pathname.startsWith('/api/manual-build') || pathname.startsWith('/api/workflows') || pathname.startsWith('/api/workflow/')) {\n        console.log('🚫 MIDDLEWARE: Blocking access to workflow API routes (temporarily disabled for launch)');\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            error: 'Manual Build workflows are temporarily unavailable. Please use router configurations instead.'\n        }, {\n            status: 503\n        });\n    }\n    // Skip other API routes\n    if (pathname.startsWith('/api/')) {\n        return res;\n    }\n    // Temporary bypass for development if Supabase connectivity issues\n    if ( true && process.env.BYPASS_AUTH_MIDDLEWARE === 'true') {\n        console.log('🔥 MIDDLEWARE: Bypassing auth checks due to BYPASS_AUTH_MIDDLEWARE=true');\n        return res;\n    }\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return req.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n                req.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove (name, options) {\n                req.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n            }\n        }\n    });\n    // Get the pathname (already defined above)\n    // Public routes that don't require authentication\n    const publicRoutes = [\n        '/',\n        '/pricing',\n        '/auth/signin',\n        '/auth/signup',\n        '/auth/callback',\n        '/auth/verify-email',\n        '/checkout',\n        '/api/stripe/webhooks',\n        // Policy and informational pages\n        '/terms',\n        '/privacy',\n        '/cookies',\n        '/security',\n        '/about',\n        '/contact',\n        '/features',\n        '/docs',\n        '/blog',\n        '/routing-strategies'\n    ];\n    // API routes that don't require authentication\n    const publicApiRoutes = [\n        '/api/stripe/webhooks',\n        '/api/system-status',\n        '/api/debug',\n        '/api/pricing',\n        '/api/external'\n    ];\n    // Check if the route is public\n    const isPublicRoute = publicRoutes.some((route)=>{\n        // Special case for root route - only match exactly\n        if (route === '/') {\n            return pathname === '/';\n        }\n        // For other routes, allow exact match or startsWith\n        return pathname === route || pathname.startsWith(route);\n    });\n    const isPublicApiRoute = publicApiRoutes.some((route)=>pathname.startsWith(route));\n    // Allow public routes and API routes\n    if (isPublicRoute || isPublicApiRoute) {\n        return res;\n    }\n    // Get the user with error handling for network issues (more secure than getSession)\n    let session = null;\n    try {\n        const { data: { user } } = await supabase.auth.getUser();\n        // Create a session-like object for compatibility\n        session = user ? {\n            user\n        } : null;\n    } catch (error) {\n        console.error('Middleware: Failed to get session from Supabase:', error);\n        // If we can't connect to Supabase, allow the request to proceed\n        // This prevents the entire app from being blocked by network issues\n        return res;\n    }\n    // If no session and trying to access protected route, redirect to signin\n    if (!session) {\n        const redirectUrl = new URL('/auth/signin', req.url);\n        redirectUrl.searchParams.set('redirectTo', pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n    }\n    // Block access to manual-build and workflow routes (temporarily disabled for launch)\n    if (pathname.startsWith('/manual-build') || pathname.startsWith('/playground/workflows')) {\n        console.log('🚫 MIDDLEWARE: Blocking access to workflow routes (temporarily disabled for launch)');\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL('/dashboard', req.url));\n    }\n    // For authenticated users, check if they're accessing dashboard routes\n    if (pathname.startsWith('/dashboard') || pathname.startsWith('/playground') || pathname.startsWith('/logs') || pathname.startsWith('/my-models') || pathname.startsWith('/api-keys') || pathname.startsWith('/configurations')) {\n        // Check subscription status for protected app routes\n        try {\n            const { data: profile, error: profileError } = await supabase.from('user_profiles').select('subscription_status, subscription_tier, user_status').eq('id', session.user.id).single();\n            let isNoProfileError = false;\n            if (profileError) {\n                console.error('Middleware: Error fetching user profile:', profileError);\n                // Check if this is a \"no rows\" error (user has no profile) vs a real network error\n                if (profileError.code === 'PGRST116') {\n                    // This means no profile exists - treat as no profile case\n                    isNoProfileError = true;\n                } else {\n                    // Real network error - allow access to prevent app from being blocked\n                    return res;\n                }\n            }\n            // Check if user has pending status (cannot access protected routes)\n            if (profile && profile.user_status === 'pending') {\n                // User exists but has pending status - redirect to complete payment\n                const userPlan = session.user.user_metadata?.plan || profile.subscription_tier;\n                const redirectUrl = new URL('/pricing', req.url);\n                redirectUrl.searchParams.set('message', 'complete_payment');\n                redirectUrl.searchParams.set('plan', userPlan);\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n            }\n            // If no profile exists (either !profile or PGRST116 error), check if this is a pending payment user\n            if (!profile || isNoProfileError) {\n                // Check if user has pending payment status in their metadata (legacy check)\n                const paymentStatus = session.user.user_metadata?.payment_status;\n                const userPlan = session.user.user_metadata?.plan;\n                if (userPlan && [\n                    'starter',\n                    'professional',\n                    'enterprise'\n                ].includes(userPlan)) {\n                    // This user was created for a paid plan - they shouldn't be signed in!\n                    console.log('Middleware: User with paid plan metadata is signed in - this should not happen!');\n                    console.log('Middleware: User plan:', userPlan, 'Payment status:', paymentStatus);\n                    // Sign them out and redirect to pricing\n                    await supabase.auth.signOut();\n                    const redirectUrl = new URL('/pricing', req.url);\n                    redirectUrl.searchParams.set('message', 'account_created_complete_payment');\n                    redirectUrl.searchParams.set('plan', userPlan);\n                    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                }\n                if (paymentStatus === 'pending' && userPlan && [\n                    'starter',\n                    'professional',\n                    'enterprise'\n                ].includes(userPlan)) {\n                    // This is a user who signed up for a paid plan but hasn't completed payment\n                    // Redirect to pricing page for fresh signup process\n                    const redirectUrl = new URL('/pricing', req.url);\n                    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                }\n                // Only create free profiles for users who don't have pending payments and no paid plan metadata\n                console.log('Middleware: Creating default free profile for user:', session.user.id);\n                try {\n                    const { error: createError } = await supabase.from('user_profiles').insert({\n                        id: session.user.id,\n                        full_name: session.user.user_metadata?.full_name || '',\n                        subscription_tier: 'free',\n                        subscription_status: 'active',\n                        user_status: 'active',\n                        created_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString()\n                    });\n                    // Send welcome email for users created through middleware\n                    if (!createError && session.user.email) {\n                        console.log('📧 Sending welcome email to middleware-created user:', session.user.email);\n                        try {\n                            const { sendWelcomeEmail } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/email/welcomeEmail */ \"(middleware)/./src/lib/email/welcomeEmail.ts\"));\n                            const emailSent = await sendWelcomeEmail({\n                                userEmail: session.user.email,\n                                userName: session.user.user_metadata?.full_name || 'New User',\n                                userTier: 'free'\n                            });\n                            if (emailSent) {\n                                console.log('✅ Welcome email sent successfully to:', session.user.email);\n                            } else {\n                                console.log('⚠️ Welcome email failed to send to:', session.user.email);\n                            }\n                        } catch (emailError) {\n                            console.error('❌ Error sending welcome email in middleware:', emailError);\n                        // Don't fail the middleware if email fails\n                        }\n                    }\n                    if (createError) {\n                        console.error('Middleware: Error creating user profile:', createError);\n                        // If we can't create profile, redirect to pricing to be safe\n                        const redirectUrl = new URL('/pricing', req.url);\n                        redirectUrl.searchParams.set('checkout', 'true');\n                        redirectUrl.searchParams.set('message', 'profile_creation_failed');\n                        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                    }\n                    // Profile created successfully, allow access\n                    console.log('Middleware: Successfully created free profile for user:', session.user.id);\n                    return res;\n                } catch (error) {\n                    console.error('Middleware: Exception creating user profile:', error);\n                    const redirectUrl = new URL('/pricing', req.url);\n                    redirectUrl.searchParams.set('checkout', 'true');\n                    redirectUrl.searchParams.set('message', 'profile_creation_failed');\n                    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                }\n            }\n            // Check subscription status - free tier users should always have access\n            // For free tier, we don't require subscription_status to be 'active' since they don't have paid subscriptions\n            const hasActiveSubscription = profile.subscription_status === 'active' || profile.subscription_tier === 'free';\n            if (!hasActiveSubscription) {\n                const redirectUrl = new URL('/pricing', req.url);\n                redirectUrl.searchParams.set('checkout', 'true');\n                redirectUrl.searchParams.set('message', 'subscription_required');\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n            }\n        } catch (error) {\n            console.error('Error checking subscription status in middleware:', error);\n            // On error, redirect to pricing to be safe\n            const redirectUrl = new URL('/pricing', req.url);\n            redirectUrl.searchParams.set('checkout', 'true');\n            redirectUrl.searchParams.set('message', 'subscription_check_failed');\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n    }\n    return res;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */ '/((?!_next/static|_next/image|favicon.ico|public/).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});