(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3270],{42307:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>h});var r=s(95155),t=s(12115),i=s(6874),l=s.n(i),n=s(66766),d=s(55020),c=s(5279),o=s(18593),u=s(21394),x=s(52643),m=s(35695);function b(){let[e,a]=(0,t.useState)(!1),[s,i]=(0,t.useState)(""),[b,h]=(0,t.useState)(""),g=(0,m.useSearchParams)(),f=(0,m.useRouter)(),p=(0,x.createSupabaseBrowserClient)();(0,t.useEffect)(()=>{let e=g.get("email"),a=g.get("payment_success");e&&h(decodeURIComponent(e)),(async()=>{let{data:{user:e}}=await p.auth.getUser();if(e){let{data:s}=await p.from("user_profiles").select("subscription_status").eq("user_id",e.id).single();s&&"active"===s.subscription_status?f.push("/dashboard"):"true"===a&&i("Payment successful! Please verify your email to complete setup.")}})()},[g,f,p]);let y=async()=>{if(!b)return void i("Please enter your email address");a(!0),i("");try{let{error:e}=await p.auth.resend({type:"signup",email:b});if(e)throw e;i("Verification email sent! Please check your inbox.")}catch(e){i(e.message||"Failed to resend email. Please try again.")}finally{a(!1)}};return(0,r.jsxs)("div",{className:"min-h-screen bg-white relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)(u.A,{gridSize:50,opacity:.064,color:"#000000",variant:"subtle",animated:!0,className:"fixed inset-0"}),(0,r.jsx)("div",{className:"absolute inset-0",children:(0,r.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle, rgba(255, 107, 53, 0.12) 1px, transparent 1px)",backgroundSize:"100px 100px",mask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)",WebkitMask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)"}})}),(0,r.jsx)("div",{className:"relative z-10 w-full max-w-md mx-auto",children:(0,r.jsxs)(d.PY1.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)(l(),{href:"/",className:"inline-flex items-center space-x-3 mb-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100 p-1",children:(0,r.jsx)(n.default,{src:"/roukey_logo.png",alt:"RouKey",width:40,height:40,className:"w-full h-full object-contain",priority:!0})}),(0,r.jsx)("span",{className:"text-3xl font-bold text-black",children:"RouKey"})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-5",style:{backgroundImage:"\n                  linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\n                  linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\n                ",backgroundSize:"20px 20px"}}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-full flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)(o.A,{className:"w-10 h-10 text-white"})}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-black mb-4",children:"Check Your Email"}),(0,r.jsx)("p",{className:"text-gray-600 text-lg mb-6",children:"We've sent a verification link to:"}),b&&(0,r.jsx)("div",{className:"bg-gray-50 rounded-xl p-4 mb-6",children:(0,r.jsx)("p",{className:"text-[#ff6b35] font-semibold text-lg",children:b})}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"Click the link in the email to verify your account and complete your registration."}),(0,r.jsxs)("div",{className:"space-y-4",children:[!b&&(0,r.jsx)("div",{children:(0,r.jsx)("input",{type:"email",placeholder:"Enter your email address",value:b,onChange:e=>h(e.target.value),className:"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white mb-4"})}),(0,r.jsx)("button",{onClick:y,disabled:e||!b,className:"w-full bg-white border-2 border-gray-200 text-gray-700 py-3 px-6 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-300 focus:ring-4 focus:ring-gray-200 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center",children:e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.A,{className:"w-5 h-5 mr-2 animate-spin"}),"Sending..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.A,{className:"w-5 h-5 mr-2"}),"Resend verification email"]})}),s&&(0,r.jsx)("div",{className:"p-4 rounded-xl ".concat(s.includes("sent")?"bg-green-50 border border-green-200":"bg-red-50 border border-red-200"),children:(0,r.jsx)("p",{className:"text-sm ".concat(s.includes("sent")?"text-green-600":"text-red-600"),children:s})})]}),(0,r.jsx)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:(0,r.jsxs)("p",{className:"text-gray-600",children:["Already verified?"," ",(0,r.jsx)(l(),{href:"/auth/signin",className:"text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors",children:"Sign in to your account"})]})})]})]})]})})]})}function h(){return(0,r.jsx)(t.Suspense,{fallback:(0,r.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,r.jsx)(b,{})})}},56047:(e,a,s)=>{Promise.resolve().then(s.bind(s,42307))}},e=>{var a=a=>e(e.s=a);e.O(0,[7125,8888,1459,5738,4755,563,2662,8669,4703,3269,9173,9219,4288,7706,7544,1142,2993,1561,9248,6761,7358],()=>a(56047)),_N_E=e.O()}]);