(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7637],{3408:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var n=s(95155),r=s(12115),a=s(73360),i=s(60993),o=s(83298),l=s(87162),c=s(80377);function d(){let{subscriptionStatus:e}=(0,o.R)(),t=(0,l.Z)(),[s,d]=(0,r.useState)([]),[m,u]=(0,r.useState)(""),[h,p]=(0,r.useState)([]),[x,g]=(0,r.useState)(!1),[f,y]=(0,r.useState)(null),[b,v]=(0,r.useState)(null),[j,w]=(0,r.useState)(0),[k,N]=(0,r.useState)(""),A=async e=>{if(e)try{let s=await fetch("/api/training/jobs?custom_api_config_id=".concat(e));if(s.ok){let e=await s.json();if(e.length>0){var t;let s=e[0];(null==(t=s.training_data)?void 0:t.raw_prompts)&&N(s.training_data.raw_prompts)}}}catch(e){}};(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/custom-configs");if(!e.ok)throw Error("Failed to fetch configurations");let t=await e.json();d(t),t.length>0&&(u(t[0].id),A(t[0].id))}catch(e){y("Failed to load configurations: ".concat(e.message))}})()},[]),(0,r.useEffect)(()=>{m&&A(m)},[m]);let _=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;try{let n=Date.now(),r=await fetch("/api/documents/list?configId=".concat(e,"&_t=").concat(n),{cache:"no-store"});if(r.ok){var s;let e=(null==(s=(await r.json()).documents)?void 0:s.length)||0;w(e)}else t<1&&setTimeout(()=>_(e,t+1),1e3)}catch(s){t<1&&setTimeout(()=>_(e,t+1),1e3)}};(0,r.useEffect)(()=>{m&&_(m)},[m]);let C=e=>{let t={system_instructions:"",examples:[],behavior_guidelines:"",general_instructions:""};for(let s of e.split("\n").filter(e=>e.trim())){let e=s.trim();if(e.startsWith("SYSTEM:"))t.system_instructions+=e.replace("SYSTEM:","").trim()+"\n";else if(e.startsWith("BEHAVIOR:"))t.behavior_guidelines+=e.replace("BEHAVIOR:","").trim()+"\n";else if(e.includes("→")||e.includes("->")){let s=e.includes("→")?"→":"->",n=e.split(s);if(n.length>=2){let e=n[0].trim(),r=n.slice(1).join(s).trim();t.examples.push({input:e,output:r})}}else e.length>0&&(t.general_instructions+=e+"\n")}return t},S=async()=>{if(!m||!k.trim())return void y("Please select an API configuration and provide training prompts.");if(!x){g(!0),y(null),v(null);try{var e;let t=C(k),n=(null==(e=s.find(e=>e.id===m))?void 0:e.name)||"Unknown Config",r={custom_api_config_id:m,name:"".concat(n," Training - ").concat(new Date().toLocaleDateString()),description:"Training job for ".concat(n," with ").concat(t.examples.length," examples"),training_data:{processed_prompts:t,raw_prompts:k.trim(),last_prompt_update:new Date().toISOString()},parameters:{training_type:"prompt_engineering",created_via:"training_page",version:"1.0"}},a=await fetch("/api/training/jobs/upsert",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!a.ok){let e=await a.text();throw Error("Failed to save training job: ".concat(a.status," ").concat(e))}let i=await a.json(),o="updated"===i.operation,l="".concat(o?"\uD83D\uDD04":"\uD83C\uDF89"," Prompt Engineering ").concat(o?"updated":"completed"," successfully!\n\n")+'Your "'.concat(n,'" configuration has been ').concat(o?"updated":"enhanced"," with:\n")+"• ".concat(t.examples.length," training examples\n")+"• Custom system instructions and behavior guidelines\n\n✨ All future chats using this configuration will automatically:\n• Follow your training examples\n• Apply your behavior guidelines\n• Maintain consistent personality and responses\n\n"+"\uD83D\uDE80 Try it now in the Playground to see your ".concat(o?"updated":"enhanced"," model in action!\n\n")+"\uD83D\uDCA1 Your training prompts remain here so you can modify them anytime.";v(l)}catch(e){y("Failed to create prompt engineering: ".concat(e.message))}finally{g(!1)}}};return(0,n.jsxs)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:[(0,n.jsx)("div",{className:"border-b border-gray-800/50",children:(0,n.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("div",{className:"flex items-center space-x-8",children:(0,n.jsx)("h1",{className:"text-2xl font-semibold text-white",children:"Training"})}),(0,n.jsx)("div",{className:"flex items-center space-x-4",children:e&&(0,n.jsx)(i.yA,{tier:e.tier,size:"lg",theme:"dark"})})]})})}),(0,n.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8 pb-8",children:(0,n.jsxs)("div",{className:"max-w-6xl mx-auto",children:[f&&(0,n.jsx)("div",{className:"mb-6 bg-red-900/20 border border-red-500/30 rounded-xl p-4",children:(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("svg",{className:"w-5 h-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,n.jsx)("p",{className:"text-red-200 text-sm font-medium",children:f})]})}),b&&(0,n.jsx)("div",{className:"mb-6 bg-green-900/20 border border-green-500/30 rounded-xl p-4",children:(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("svg",{className:"w-5 h-5 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,n.jsx)("p",{className:"text-green-200 text-sm font-medium",children:b})]})}),(0,n.jsx)(i.sU,{feature:"knowledge_base",customMessage:"Knowledge base document upload is available starting with the Professional plan. Upload documents to enhance your AI with proprietary knowledge and create domain-specific assistants.",theme:"dark",children:(0,n.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-8 mb-8 border border-gray-800/50",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-orange-500/20 rounded-lg flex items-center justify-center",children:(0,n.jsx)("svg",{className:"w-5 h-5 text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Knowledge Documents"}),(0,n.jsx)("p",{className:"text-sm text-gray-400",children:"Upload documents to enhance your AI with proprietary knowledge"})]})]}),e&&m&&(0,n.jsx)("div",{className:"mb-4 bg-gray-800/50 border border-gray-700/50 rounded-lg px-4 py-2",children:(0,n.jsx)(i.Jg,{current:j,limit:5*("professional"===e.tier),label:"Knowledge Base Documents",tier:e.tier,showUpgradeHint:!0,theme:"dark"})}),(0,n.jsx)(a.A,{configId:m,theme:"dark",onDocumentUploaded:()=>{m&&setTimeout(()=>{_(m)},500)},onDocumentDeleted:()=>{m&&setTimeout(()=>{_(m)},500)}})]})}),(0,n.jsx)(i.sU,{feature:"prompt_engineering",customMessage:"Prompt engineering is available starting with the Starter plan. Create custom prompts to define AI behavior, provide examples, and enhance your model's responses.",theme:"dark",children:(0,n.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-8 border border-gray-800/50",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center",children:(0,n.jsx)("svg",{className:"w-5 h-5 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Custom Prompts"}),(0,n.jsx)("p",{className:"text-sm text-gray-400",children:"Define behavior, examples, and instructions for your AI"})]})]}),(0,n.jsxs)("div",{className:"space-y-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"configSelect",className:"block text-sm font-medium text-gray-300 mb-2",children:"Select API Configuration"}),(0,n.jsxs)("select",{id:"configSelect",value:m,onChange:e=>u(e.target.value),className:"w-full px-4 py-3 border border-gray-700 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition-colors bg-gray-800 text-white text-sm",children:[(0,n.jsx)("option",{value:"",children:"Choose which model to train..."}),s.map(e=>(0,n.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"trainingPrompts",className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Prompts & Instructions"}),(0,n.jsx)("textarea",{id:"trainingPrompts",value:k,onChange:e=>N(e.target.value),placeholder:"Enter your training prompts using these formats:\n\nSYSTEM: You are a helpful customer service agent for our company\nBEHAVIOR: Always be polite and offer solutions\n\nUser asks about returns → I'd be happy to help with your return! Let me check our policy for you.\nCustomer is frustrated → I understand your frustration. Let me see how I can resolve this for you.\n\nGeneral instructions can be written as regular text.",rows:12,className:"w-full px-4 py-3 border border-gray-700 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition-colors bg-gray-800 text-white text-sm resize-none font-mono placeholder-gray-500"}),(0,n.jsxs)("div",{className:"mt-3 bg-blue-900/20 border border-blue-500/30 rounded-lg p-3",children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-blue-300 mb-2",children:"Training Format Guide:"}),(0,n.jsxs)("ul",{className:"text-xs text-blue-200 space-y-1",children:[(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"SYSTEM:"})," Core instructions for the AI's role and personality"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"BEHAVIOR:"})," Guidelines for how the AI should behave"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Examples:"}),' Use "User input → Expected response" format']}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"General:"})," Any other instructions written as normal text"]})]})]})]}),(0,n.jsxs)("div",{className:"flex justify-between items-center pt-6 border-t border-gray-700/50",children:[(0,n.jsx)("div",{className:"flex space-x-3",children:(0,n.jsx)("button",{type:"button",className:"btn-secondary-dark",onClick:()=>{t.showConfirmation({title:"Clear Training Prompts",message:"Are you sure you want to clear all training prompts? This will remove all your custom instructions, examples, and behavior guidelines. This action cannot be undone.",confirmText:"Clear All",cancelText:"Cancel",type:"warning"},()=>{N("")})},children:"Clear Form"})}),(0,n.jsx)("button",{type:"button",onClick:S,disabled:!m||!k.trim()||x,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:x?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Processing Prompts..."]}):"Save Prompts"})]})]})]})}),(0,n.jsx)(c.A,{isOpen:t.isOpen,onClose:t.hideConfirmation,onConfirm:t.onConfirm,title:t.title,message:t.message,confirmText:t.confirmText,cancelText:t.cancelText,type:t.type,isLoading:t.isLoading})]})})]})}},17974:(e,t,s)=>{"use strict";s.d(t,{BZ:()=>r.A,Gg:()=>a.A,OR:()=>i.A,Zu:()=>n.A});var n=s(78039),r=s(90345),a=s(62486),i=s(67508)},38152:(e,t,s)=>{"use strict";s.d(t,{Pi:()=>n.A,fK:()=>a.A,uc:()=>r.A});var n=s(55628),r=s(31151),a=s(74500)},39499:(e,t,s)=>{"use strict";s.d(t,{Gg:()=>a.A,JD:()=>r.A,Kp:()=>n.A});var n=s(15713),r=s(15442),a=s(27305)},42005:(e,t,s)=>{Promise.resolve().then(s.bind(s,3408))},47321:(e,t,s)=>{"use strict";s.d(t,{C1:()=>n.A,Pi:()=>r.A,qh:()=>a.A});var n=s(6865),r=s(55628),a=s(52589)},91480:(e,t,s)=>{"use strict";s.d(t,{RI:()=>r,rA:()=>a,ZH:()=>i,iU:()=>o,kr:()=>l,_O:()=>c,X:()=>d.A});var n=s(19946);let r=(0,n.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),a=(0,n.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),i=(0,n.A)("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]),o=(0,n.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),l=(0,n.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),c=(0,n.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var d=s(54416)}},e=>{var t=t=>e(e.s=t);e.O(0,[8888,1459,5738,9968,6060,4755,563,2662,8669,4703,3269,9173,9219,4288,7706,7544,1142,2993,1561,9248,6761,7358],()=>t(42005)),_N_E=e.O()}]);