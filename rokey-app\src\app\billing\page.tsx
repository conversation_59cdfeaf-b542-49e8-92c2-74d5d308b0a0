'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  CreditCardIcon,
  CalendarIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { useSubscription } from '@/hooks/useSubscription';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import DebugSubscriptionStatus from '@/components/DebugSubscriptionStatus';
import DebugWebhookLogs from '@/components/DebugWebhookLogs';
import confetti from 'canvas-confetti';
import { useConfirmation } from '@/hooks/useConfirmation';
import emailjs from '@emailjs/browser';

interface PlanFeature {
  name: string;
  included: boolean;
  limit?: string;
}

interface Plan {
  id: string;
  name: string;
  price: number;
  interval: string;
  features: PlanFeature[];
  popular?: boolean;
}

const plans: Plan[] = [
  {
    id: 'free',
    name: 'Free',
    price: 0,
    interval: 'forever',
    features: [
      { name: 'Strict fallback routing only', included: true },
      { name: 'Basic analytics', included: true },
      { name: 'No custom roles', included: false },
      { name: 'Configurations', included: true, limit: '1 max' },
      { name: 'API keys per config', included: true, limit: '3 max' },
      { name: 'User-generated API keys', included: true, limit: '3 max' }
    ]
  },
  {
    id: 'starter',
    name: 'Starter',
    price: 19,
    interval: 'month',
    popular: true,
    features: [
      { name: 'All routing strategies', included: true },
      { name: 'Advanced analytics', included: true },
      { name: 'Custom roles', included: true, limit: 'Up to 3 roles' },
      { name: 'Configurations', included: true, limit: '5 max' },
      { name: 'API keys per config', included: true, limit: '15 max' },
      { name: 'User-generated API keys', included: true, limit: '50 max' },
      { name: 'Browsing tasks', included: true, limit: '15/month' }
    ]
  },
  {
    id: 'professional',
    name: 'Professional',
    price: 49,
    interval: 'month',
    features: [
      { name: 'Everything in Starter', included: true },
      { name: 'Unlimited configurations', included: true },
      { name: 'Unlimited API keys per config', included: true },
      { name: 'Unlimited user-generated API keys', included: true },
      { name: 'Knowledge base documents', included: true, limit: '5 documents' },
      { name: 'Priority support', included: true }
    ]
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 149,
    interval: 'month',
    features: [
      { name: 'Everything in Professional', included: true },
      { name: 'Unlimited knowledge base documents', included: true },
      { name: 'Advanced semantic caching', included: true },
      { name: 'Custom integrations', included: true },
      { name: 'Dedicated support + phone', included: true },
      { name: 'SLA guarantee', included: true }
    ]
  }
];

export default function BillingPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, subscriptionStatus, refreshStatus: refreshSubscription, createCheckoutSession, openCustomerPortal } = useSubscription();
  const confirmation = useConfirmation();
  
  const [loading, setLoading] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [cancelFeedback, setCancelFeedback] = useState('');
  const [tierBeforePortal, setTierBeforePortal] = useState<string | null>(null);

  const currentPlan = plans.find(plan => plan.id === subscriptionStatus?.tier) || plans[0];

  // Calculate days until renewal based on actual subscription data
  const daysUntilRenewal = useMemo(() => {
    if (subscriptionStatus?.tier === 'free' || !subscriptionStatus?.currentPeriodEnd) {
      return null;
    }

    const renewalDate = new Date(subscriptionStatus.currentPeriodEnd);
    const today = new Date();
    const diffTime = renewalDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Return null if subscription has already expired
    return diffDays > 0 ? diffDays : null;
  }, [subscriptionStatus?.tier, subscriptionStatus?.currentPeriodEnd]);

  // Helper function to determine if a tier change is an upgrade
  const isUpgrade = (fromTier: string, toTier: string) => {
    const tierOrder = { 'free': 0, 'starter': 1, 'professional': 2, 'enterprise': 3 };
    return tierOrder[toTier as keyof typeof tierOrder] > tierOrder[fromTier as keyof typeof tierOrder];
  };

  // Handle return from Customer Portal
  useEffect(() => {
    const portalReturn = searchParams.get('portal_return');
    const previousTier = searchParams.get('prev_tier');

    if (portalReturn === 'true') {
      console.log('Returned from Customer Portal, previous tier:', previousTier);

      // Remove the parameters from URL
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('portal_return');
      newUrl.searchParams.delete('prev_tier');
      window.history.replaceState({}, '', newUrl.toString());

      // Wait for webhook to process, then refresh subscription status
      const refreshWithRetry = async () => {
        let attempts = 0;
        const maxAttempts = 8; // Increased attempts
        const initialDelay = 3000; // Longer initial delay for webhook processing
        const retryDelay = 2000; // 2 seconds between retry attempts

        console.log('Starting subscription refresh with retry logic...');

        // Initial delay to allow webhook processing
        await new Promise(resolve => setTimeout(resolve, initialDelay));

        while (attempts < maxAttempts) {
          try {
            console.log(`Refresh attempt ${attempts + 1}/${maxAttempts}`);

            // Force a complete refresh
            await refreshSubscription();

            // Wait a moment for the state to update
            await new Promise(resolve => setTimeout(resolve, 500));

            // Get fresh subscription status after refresh
            const response = await fetch(`/api/stripe/subscription-status?userId=${user?.id}&_t=${Date.now()}`, {
              cache: 'no-store',
              headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
              }
            });

            if (response.ok) {
              const freshStatus = await response.json();
              const currentTier = freshStatus.tier || 'free';

              console.log('Fresh tier from API:', currentTier, 'Previous tier:', previousTier);

              // Check if tier actually changed
              if (currentTier !== previousTier) {
                const wasUpgrade = isUpgrade(previousTier || 'free', currentTier);

                if (wasUpgrade) {
                  // Show upgrade success message with confetti
                  toast.success('Plan upgraded successfully!');

                  // Trigger confetti for upgrades only
                  const triggerConfetti = () => {
                    confetti({
                      particleCount: 100,
                      spread: 70,
                      origin: { y: 0.6 }
                    });
                  };

                  triggerConfetti();
                  setTimeout(triggerConfetti, 500);
                  setTimeout(triggerConfetti, 1000);
                } else {
                  // Show generic success message for downgrades/cancellations
                  toast.success('Billing settings updated successfully!');
                }

                console.log('Plan change detected and processed successfully');
                break;
              } else if (attempts >= 3) {
                // After a few attempts, show success message even if no change detected
                // (user might have just viewed the portal without making changes)
                toast.success('Billing settings updated successfully!');
                console.log('No plan change detected, but showing success message');
                break;
              }
            }

            attempts++;

            if (attempts < maxAttempts) {
              console.log(`No change detected yet, waiting ${retryDelay}ms before next attempt...`);
              await new Promise(resolve => setTimeout(resolve, retryDelay));
            }

          } catch (error) {
            console.error(`Refresh attempt ${attempts + 1} failed:`, error);
            attempts++;

            if (attempts >= maxAttempts) {
              console.log('Max refresh attempts reached, forcing page reload...');
              toast.error('Refreshing page to update subscription status...');
              // Force a page reload as last resort
              setTimeout(() => {
                window.location.reload();
              }, 2000);
            } else {
              await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
          }
        }
      };

      refreshWithRetry();
    }
  }, [searchParams, refreshSubscription, user?.id]); // Removed subscriptionStatus?.tier from dependencies

  const handleChangePlan = async () => {
    console.log('🎯 Manage Subscription button clicked');
    console.log('👤 User:', user?.id);
    console.log('📊 Subscription status:', subscriptionStatus);

    const currentTier = subscriptionStatus?.tier || 'free';

    // Free users should also use Customer Portal for upgrades
    // This ensures immediate charging instead of deferred billing
    if (currentTier === 'free') {
      console.log('🆓 Free user - using Customer Portal for immediate upgrade billing');
      // Continue to Customer Portal logic below instead of redirecting to pricing
    }

    // Paid users use Customer Portal
    try {
      setLoading(true);
      console.log('⏳ Setting loading state to true');

      // Create return URL with current domain, portal return parameter, and current tier
      const returnUrl = `${window.location.origin}/billing?portal_return=true&prev_tier=${currentTier}`;
      console.log('🔗 Generated return URL:', returnUrl);

      await openCustomerPortal(returnUrl);
    } catch (error: any) {
      console.error('💥 Customer portal error:', error);

      // If portal is not configured, fall back to showing a helpful message
      if (error.message.includes('No configuration provided') || error.message.includes('default configuration has not been created')) {
        toast.error('Billing portal is being set up. Please contact support for plan changes.');
      } else {
        toast.error('Failed to open billing portal. Please try again.');
      }
      setLoading(false);
      console.log('⏳ Setting loading state to false after error');
    }
  };

  const handleCancelSubscription = async () => {
    if (!cancelReason.trim()) {
      toast.error('Please select a reason for cancellation');
      return;
    }

    setLoading(true);
    try {
      // Send cancellation feedback via EmailJS
      const templateParams = {
        user_email: user?.email || 'Unknown',
        user_name: user?.user_metadata?.first_name || 'User',
        current_plan: currentPlan.name,
        cancel_reason: cancelReason,
        additional_feedback: cancelFeedback,
        cancel_date: new Date().toLocaleDateString()
      };

      await emailjs.send(
        process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID!,
        process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID!,
        templateParams,
        process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY!
      );

      // Here you would also cancel the subscription in your payment processor
      // For now, we'll simulate the process
      await new Promise(resolve => setTimeout(resolve, 1500));

      toast.success('Subscription cancelled successfully. We\'ve sent your feedback to our team.');
      setShowCancelModal(false);
      setCancelReason('');
      setCancelFeedback('');
      await refreshSubscription();
    } catch (error: any) {
      console.error('Cancellation error:', error);
      toast.error('Failed to cancel subscription. Please contact support.');
    } finally {
      setLoading(false);
    }
  };

  const cancelReasons = [
    'Too expensive',
    'Not using enough features',
    'Found a better alternative',
    'Technical issues',
    'Poor customer support',
    'Missing features I need',
    'Temporary financial constraints',
    'Other'
  ];

  return (
    <div className="min-h-screen w-full bg-[#040716] text-white overflow-x-hidden">
      {/* Header Section - Following analytics design pattern */}
      <div className="border-b border-gray-800/50">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              <h1 className="text-2xl font-semibold text-white">Billing & Plans</h1>
              <div className="flex items-center space-x-1">
                <div className="px-3 py-1 text-sm bg-cyan-500 text-white rounded">
                  Subscription
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {/* Removed refresh button */}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8 space-y-8">



        {/* Current Plan Status - Enhanced Card with Analytics Style */}
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6 hover:shadow-lg transition-all duration-200">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-cyan-500/10 rounded-lg">
                <CreditCardIcon className="h-5 w-5 text-cyan-400" />
              </div>
              <h2 className="text-lg font-semibold text-white">Current Plan</h2>
            </div>
            {subscriptionStatus?.tier !== 'free' && (
              <Button
                variant="outline"
                onClick={() => setShowCancelModal(true)}
                className="text-red-400 border-red-500/20 hover:bg-red-500/10 hover:border-red-500/40 transition-all duration-200 text-sm"
                size="sm"
              >
                Cancel Subscription
              </Button>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Plan Name & Price */}
            <div className="md:col-span-2">
              <div className="flex items-center gap-4 mb-3">
                <h3 className="text-2xl font-bold text-white">{currentPlan.name}</h3>
                {currentPlan.popular && (
                  <span className="px-2 py-1 text-xs bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full font-semibold">
                    ⭐ Popular
                  </span>
                )}
              </div>
              <div className="space-y-2">
                <div className="text-3xl font-bold text-white">
                  {currentPlan.price === 0 ? (
                    <span className="text-green-400">Free</span>
                  ) : (
                    <span>${currentPlan.price}<span className="text-gray-400 text-lg font-normal">/{currentPlan.interval}</span></span>
                  )}
                </div>
                {currentPlan.price === 0 && (
                  <p className="text-green-400 font-medium">Forever free</p>
                )}
              </div>
            </div>

            {/* Renewal Info */}
            <div className="flex flex-col justify-center">
              {daysUntilRenewal && (
                <div className="bg-gray-800/50 rounded-lg p-4 text-center">
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-300 mb-1">
                    <CalendarIcon className="h-4 w-4" />
                    <span>Next Billing</span>
                  </div>
                  <div className="text-white font-semibold">
                    {daysUntilRenewal} days
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Plan Features & Management */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Current Plan Features */}
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 bg-blue-500/10 rounded-lg">
                <CheckCircleIcon className="h-5 w-5 text-blue-400" />
              </div>
              <h3 className="text-lg font-semibold text-white">Plan Features</h3>
            </div>

            <div className="space-y-4">
              {currentPlan.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-3 py-2">
                  <div className="flex-shrink-0">
                    {feature.included ? (
                      <div className="p-1 bg-green-500/10 rounded-full">
                        <CheckCircleIcon className="h-4 w-4 text-green-400" />
                      </div>
                    ) : (
                      <div className="p-1 bg-gray-700/50 rounded-full">
                        <XCircleIcon className="h-4 w-4 text-gray-500" />
                      </div>
                    )}
                  </div>
                  <span className={`text-sm ${
                    feature.included ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    {feature.name}
                    {feature.limit && (
                      <span className="text-gray-400 font-medium"> ({feature.limit})</span>
                    )}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Plan Management Actions */}
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 bg-orange-500/10 rounded-lg">
                <CreditCardIcon className="h-5 w-5 text-orange-400" />
              </div>
              <h3 className="text-lg font-semibold text-white">Manage Subscription</h3>
            </div>

            <div className="space-y-6">
              {/* Current Plan Summary */}
              <div className="text-center p-4 bg-gray-800/30 rounded-lg">
                <div className="inline-flex items-center gap-2 bg-green-500/10 text-green-400 px-3 py-1 rounded-full text-sm font-semibold mb-3 border border-green-500/20">
                  <CheckCircleIcon className="h-4 w-4" />
                  Active Plan
                </div>
                <h4 className="text-xl font-bold text-white mb-1">{currentPlan.name}</h4>
                <div className="text-2xl font-bold text-white">
                  {currentPlan.price === 0 ? (
                    <span className="text-green-400">Free</span>
                  ) : (
                    <span>${currentPlan.price}<span className="text-gray-400 text-base font-normal">/{currentPlan.interval}</span></span>
                  )}
                </div>
              </div>

              {/* Action Button */}
              <Button
                onClick={handleChangePlan}
                disabled={loading}
                className="w-full bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                size="lg"
              >
                {subscriptionStatus?.tier === 'free' ? (
                  <>
                    <ArrowUpIcon className="h-5 w-5 mr-2" />
                    Upgrade Plan
                  </>
                ) : (
                  <>
                    <CreditCardIcon className="h-5 w-5 mr-2" />
                    Manage Subscription
                  </>
                )}
              </Button>

              <p className="text-center text-sm text-gray-400">
                {subscriptionStatus?.tier === 'free'
                  ? 'Unlock advanced features and higher limits'
                  : 'Change plans, update billing, or cancel anytime'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Billing Information & Support */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Billing Information */}
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 bg-green-500/10 rounded-lg">
                <CreditCardIcon className="h-5 w-5 text-green-400" />
              </div>
              <h3 className="text-lg font-semibold text-white">Billing Details</h3>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center py-2 border-b border-gray-700/30">
                <span className="text-gray-400 text-sm">Plan</span>
                <span className="font-medium text-white">{currentPlan.name}</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-700/30">
                <span className="text-gray-400 text-sm">Billing Cycle</span>
                <span className="font-medium text-white">
                  {currentPlan.price === 0 ? 'N/A' : `Monthly`}
                </span>
              </div>
              {daysUntilRenewal && (
                <div className="flex justify-between items-center py-2 border-b border-gray-700/30">
                  <span className="text-gray-400 text-sm">Next Billing</span>
                  <span className="font-medium text-white">
                    {new Date(Date.now() + daysUntilRenewal * 24 * 60 * 60 * 1000).toLocaleDateString()}
                  </span>
                </div>
              )}
              <div className="flex justify-between items-center py-2">
                <span className="text-gray-400 text-sm">Status</span>
                <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                  subscriptionStatus?.tier === 'free'
                    ? 'bg-gray-700/50 text-gray-300'
                    : 'bg-green-500/10 text-green-400'
                }`}>
                  {subscriptionStatus?.tier === 'free' ? 'Free Plan' : 'Active'}
                </span>
              </div>
            </div>
          </div>

          {/* Support & Help */}
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 bg-purple-500/10 rounded-lg">
                <ExclamationTriangleIcon className="h-5 w-5 text-purple-400" />
              </div>
              <h3 className="text-lg font-semibold text-white">Support</h3>
            </div>

            <div className="space-y-4">
              <p className="text-gray-300 text-sm">
                Need help with your subscription or billing? We're here to assist you.
              </p>
              <div className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full justify-start text-gray-300 border-gray-600 hover:bg-gray-800/50 hover:border-gray-500 text-sm"
                  onClick={() => window.open('/contact', '_blank')}
                >
                  <EnvelopeIcon className="h-4 w-4 mr-2" />
                  Contact Support
                </Button>
                <div className="text-xs text-gray-400 text-center">
                  Response time: Usually within 24 hours
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Cancellation Modal */}
        {showCancelModal && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-xl shadow-2xl max-w-md w-full p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-2 bg-red-500/10 rounded-lg">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                </div>
                <h3 className="text-lg font-semibold text-white">Cancel Subscription</h3>
              </div>

              <p className="text-gray-300 mb-6 text-sm">
                We're sorry to see you go! Please help us improve by telling us why you're cancelling.
              </p>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Reason for cancellation *
                  </label>
                  <select
                    value={cancelReason}
                    onChange={(e) => setCancelReason(e.target.value)}
                    className="w-full p-3 border border-gray-600 rounded-lg bg-gray-800 text-white focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                  >
                    <option value="">Select a reason...</option>
                    {cancelReasons.map((reason) => (
                      <option key={reason} value={reason}>{reason}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Additional feedback (optional)
                  </label>
                  <textarea
                    value={cancelFeedback}
                    onChange={(e) => setCancelFeedback(e.target.value)}
                    placeholder="Tell us more about your experience or what we could do better..."
                    rows={3}
                    className="w-full p-3 border border-gray-600 rounded-lg bg-gray-800 text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-sm"
                  />
                </div>
              </div>

              <div className="flex gap-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => setShowCancelModal(false)}
                  className="flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50 text-sm"
                >
                  Keep Subscription
                </Button>
                <Button
                  onClick={handleCancelSubscription}
                  disabled={loading || !cancelReason.trim()}
                  className="flex-1 bg-red-600 hover:bg-red-700 text-white text-sm"
                >
                  {loading ? 'Cancelling...' : 'Cancel Subscription'}
                </Button>
              </div>
            </div>
          </div>
        )}



        {/* Confirmation Modal */}
        <ConfirmationModal
          isOpen={confirmation.isOpen}
          onClose={confirmation.hideConfirmation}
          onConfirm={confirmation.onConfirm}
          title={confirmation.title}
          message={confirmation.message}
          confirmText={confirmation.confirmText}
          cancelText={confirmation.cancelText}
          type={confirmation.type}
          isLoading={confirmation.isLoading}
        />
      </div>
    </div>
  );
}
