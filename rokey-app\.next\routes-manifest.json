{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/api/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=30, stale-while-revalidate=60"}, {"key": "X-Content-Type-Options", "value": "nosniff"}], "regex": "^/api(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/_next/static/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^/_next/static(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/manifest.json", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}], "regex": "^/manifest\\.json(?:/)?$"}, {"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}, {"key": "Service-Worker-Allowed", "value": "/"}], "regex": "^/sw\\.js(?:/)?$"}, {"source": "/", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}], "regex": "^/(?:/)?$"}, {"source": "/(.*)", "headers": [{"key": "X-DNS-Prefetch-Control", "value": "on"}, {"key": "X-Frame-Options", "value": "DENY"}], "regex": "^(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/api/custom-configs/[configId]", "regex": "^/api/custom\\-configs/([^/]+?)(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId"}, "namedRegex": "^/api/custom\\-configs/(?<nxtPconfigId>[^/]+?)(?:/)?$"}, {"page": "/api/custom-configs/[configId]/browsing", "regex": "^/api/custom\\-configs/([^/]+?)/browsing(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId"}, "namedRegex": "^/api/custom\\-configs/(?<nxtPconfigId>[^/]+?)/browsing(?:/)?$"}, {"page": "/api/custom-configs/[configId]/default-chat-key", "regex": "^/api/custom\\-configs/([^/]+?)/default\\-chat\\-key(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId"}, "namedRegex": "^/api/custom\\-configs/(?<nxtPconfigId>[^/]+?)/default\\-chat\\-key(?:/)?$"}, {"page": "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]", "regex": "^/api/custom\\-configs/([^/]+?)/default\\-key\\-handler/([^/]+?)(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId", "nxtPapiKeyId": "nxtPapiKeyId"}, "namedRegex": "^/api/custom\\-configs/(?<nxtPconfigId>[^/]+?)/default\\-key\\-handler/(?<nxtPapiKeyId>[^/]+?)(?:/)?$"}, {"page": "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments", "regex": "^/api/custom\\-configs/([^/]+?)/keys/([^/]+?)/complexity\\-assignments(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId", "nxtPapiKeyId": "nxtPapiKeyId"}, "namedRegex": "^/api/custom\\-configs/(?<nxtPconfigId>[^/]+?)/keys/(?<nxtPapiKeyId>[^/]+?)/complexity\\-assignments(?:/)?$"}, {"page": "/api/custom-configs/[configId]/routing", "regex": "^/api/custom\\-configs/([^/]+?)/routing(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId"}, "namedRegex": "^/api/custom\\-configs/(?<nxtPconfigId>[^/]+?)/routing(?:/)?$"}, {"page": "/api/documents/[documentId]", "regex": "^/api/documents/([^/]+?)(?:/)?$", "routeKeys": {"nxtPdocumentId": "nxtPdocumentId"}, "namedRegex": "^/api/documents/(?<nxtPdocumentId>[^/]+?)(?:/)?$"}, {"page": "/api/external/v1/api-keys/[keyId]", "regex": "^/api/external/v1/api\\-keys/([^/]+?)(?:/)?$", "routeKeys": {"nxtPkeyId": "nxtPkeyId"}, "namedRegex": "^/api/external/v1/api\\-keys/(?<nxtPkeyId>[^/]+?)(?:/)?$"}, {"page": "/api/external/v1/async/result/[jobId]", "regex": "^/api/external/v1/async/result/([^/]+?)(?:/)?$", "routeKeys": {"nxtPjobId": "nxtPjobId"}, "namedRegex": "^/api/external/v1/async/result/(?<nxtPjobId>[^/]+?)(?:/)?$"}, {"page": "/api/external/v1/async/status/[jobId]", "regex": "^/api/external/v1/async/status/([^/]+?)(?:/)?$", "routeKeys": {"nxtPjobId": "nxtPjobId"}, "namedRegex": "^/api/external/v1/async/status/(?<nxtPjobId>[^/]+?)(?:/)?$"}, {"page": "/api/external/v1/configs/[configId]", "regex": "^/api/external/v1/configs/([^/]+?)(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId"}, "namedRegex": "^/api/external/v1/configs/(?<nxtPconfigId>[^/]+?)(?:/)?$"}, {"page": "/api/external/v1/configs/[configId]/keys", "regex": "^/api/external/v1/configs/([^/]+?)/keys(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId"}, "namedRegex": "^/api/external/v1/configs/(?<nxtPconfigId>[^/]+?)/keys(?:/)?$"}, {"page": "/api/external/v1/configs/[configId]/keys/[keyId]", "regex": "^/api/external/v1/configs/([^/]+?)/keys/([^/]+?)(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId", "nxtPkeyId": "nxtPkeyId"}, "namedRegex": "^/api/external/v1/configs/(?<nxtPconfigId>[^/]+?)/keys/(?<nxtPkeyId>[^/]+?)(?:/)?$"}, {"page": "/api/external/v1/configs/[configId]/routing", "regex": "^/api/external/v1/configs/([^/]+?)/routing(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId"}, "namedRegex": "^/api/external/v1/configs/(?<nxtPconfigId>[^/]+?)/routing(?:/)?$"}, {"page": "/api/keys/[apiKeyId]", "regex": "^/api/keys/([^/]+?)(?:/)?$", "routeKeys": {"nxtPapiKeyId": "nxtPapiKeyId"}, "namedRegex": "^/api/keys/(?<nxtPapiKeyId>[^/]+?)(?:/)?$"}, {"page": "/api/keys/[apiKeyId]/roles", "regex": "^/api/keys/([^/]+?)/roles(?:/)?$", "routeKeys": {"nxtPapiKeyId": "nxtPapiKeyId"}, "namedRegex": "^/api/keys/(?<nxtPapiKeyId>[^/]+?)/roles(?:/)?$"}, {"page": "/api/keys/[apiKeyId]/roles/[roleName]", "regex": "^/api/keys/([^/]+?)/roles/([^/]+?)(?:/)?$", "routeKeys": {"nxtPapiKeyId": "nxtPapiKeyId", "nxtProleName": "nxtProleName"}, "namedRegex": "^/api/keys/(?<nxtPapiKeyId>[^/]+?)/roles/(?<nxtProleName>[^/]+?)(?:/)?$"}, {"page": "/api/orchestration/status/[executionId]", "regex": "^/api/orchestration/status/([^/]+?)(?:/)?$", "routeKeys": {"nxtPexecutionId": "nxtPexecutionId"}, "namedRegex": "^/api/orchestration/status/(?<nxtPexecutionId>[^/]+?)(?:/)?$"}, {"page": "/api/orchestration/stream/[executionId]", "regex": "^/api/orchestration/stream/([^/]+?)(?:/)?$", "routeKeys": {"nxtPexecutionId": "nxtPexecutionId"}, "namedRegex": "^/api/orchestration/stream/(?<nxtPexecutionId>[^/]+?)(?:/)?$"}, {"page": "/api/orchestration/synthesis-fallback/[executionId]", "regex": "^/api/orchestration/synthesis\\-fallback/([^/]+?)(?:/)?$", "routeKeys": {"nxtPexecutionId": "nxtPexecutionId"}, "namedRegex": "^/api/orchestration/synthesis\\-fallback/(?<nxtPexecutionId>[^/]+?)(?:/)?$"}, {"page": "/api/orchestration/synthesis-stream/[executionId]", "regex": "^/api/orchestration/synthesis\\-stream/([^/]+?)(?:/)?$", "routeKeys": {"nxtPexecutionId": "nxtPexecutionId"}, "namedRegex": "^/api/orchestration/synthesis\\-stream/(?<nxtPexecutionId>[^/]+?)(?:/)?$"}, {"page": "/api/orchestration/synthesis-stream-direct/[executionId]", "regex": "^/api/orchestration/synthesis\\-stream\\-direct/([^/]+?)(?:/)?$", "routeKeys": {"nxtPexecutionId": "nxtPexecutionId"}, "namedRegex": "^/api/orchestration/synthesis\\-stream\\-direct/(?<nxtPexecutionId>[^/]+?)(?:/)?$"}, {"page": "/api/user/custom-roles/[customRoleId]", "regex": "^/api/user/custom\\-roles/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcustomRoleId": "nxtPcustomRoleId"}, "namedRegex": "^/api/user/custom\\-roles/(?<nxtPcustomRoleId>[^/]+?)(?:/)?$"}, {"page": "/api/user-api-keys/[keyId]", "regex": "^/api/user\\-api\\-keys/([^/]+?)(?:/)?$", "routeKeys": {"nxtPkeyId": "nxtPkeyId"}, "namedRegex": "^/api/user\\-api\\-keys/(?<nxtPkeyId>[^/]+?)(?:/)?$"}, {"page": "/my-models/[configId]", "regex": "^/my\\-models/([^/]+?)(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId"}, "namedRegex": "^/my\\-models/(?<nxtPconfigId>[^/]+?)(?:/)?$"}, {"page": "/routing-setup/[configId]", "regex": "^/routing\\-setup/([^/]+?)(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId"}, "namedRegex": "^/routing\\-setup/(?<nxtPconfigId>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/about-developer", "regex": "^/about\\-developer(?:/)?$", "routeKeys": {}, "namedRegex": "^/about\\-developer(?:/)?$"}, {"page": "/add-keys", "regex": "^/add\\-keys(?:/)?$", "routeKeys": {}, "namedRegex": "^/add\\-keys(?:/)?$"}, {"page": "/analytics", "regex": "^/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/analytics(?:/)?$"}, {"page": "/apple-icon.png", "regex": "^/apple\\-icon\\.png(?:/)?$", "routeKeys": {}, "namedRegex": "^/apple\\-icon\\.png(?:/)?$"}, {"page": "/auth/callback", "regex": "^/auth/callback(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/callback(?:/)?$"}, {"page": "/auth/recover", "regex": "^/auth/recover(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/recover(?:/)?$"}, {"page": "/auth/reset-password", "regex": "^/auth/reset\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/reset\\-password(?:/)?$"}, {"page": "/auth/signin", "regex": "^/auth/signin(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signin(?:/)?$"}, {"page": "/auth/signup", "regex": "^/auth/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signup(?:/)?$"}, {"page": "/auth/verify-email", "regex": "^/auth/verify\\-email(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/verify\\-email(?:/)?$"}, {"page": "/billing", "regex": "^/billing(?:/)?$", "routeKeys": {}, "namedRegex": "^/billing(?:/)?$"}, {"page": "/blog", "regex": "^/blog(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog(?:/)?$"}, {"page": "/blog/ai-api-gateway-2025-guide", "regex": "^/blog/ai\\-api\\-gateway\\-2025\\-guide(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/ai\\-api\\-gateway\\-2025\\-guide(?:/)?$"}, {"page": "/blog/ai-model-selection-guide", "regex": "^/blog/ai\\-model\\-selection\\-guide(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/ai\\-model\\-selection\\-guide(?:/)?$"}, {"page": "/blog/bootstrap-lean-startup-2025", "regex": "^/blog/bootstrap\\-lean\\-startup\\-2025(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/bootstrap\\-lean\\-startup\\-2025(?:/)?$"}, {"page": "/blog/build-ai-powered-saas", "regex": "^/blog/build\\-ai\\-powered\\-saas(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/build\\-ai\\-powered\\-saas(?:/)?$"}, {"page": "/blog/building-ai-apps-byok-advantage-2025", "regex": "^/blog/building\\-ai\\-apps\\-byok\\-advantage\\-2025(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/building\\-ai\\-apps\\-byok\\-advantage\\-2025(?:/)?$"}, {"page": "/blog/cost-effective-ai-development", "regex": "^/blog/cost\\-effective\\-ai\\-development(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/cost\\-effective\\-ai\\-development(?:/)?$"}, {"page": "/blog/openai-vs-claude-vs-gemini-2025", "regex": "^/blog/openai\\-vs\\-claude\\-vs\\-gemini\\-2025(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/openai\\-vs\\-claude\\-vs\\-gemini\\-2025(?:/)?$"}, {"page": "/blog/roukey-ai-routing-strategies", "regex": "^/blog/roukey\\-ai\\-routing\\-strategies(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/roukey\\-ai\\-routing\\-strategies(?:/)?$"}, {"page": "/blog/roukey-vs-openrouter-vs-portkey-2025", "regex": "^/blog/roukey\\-vs\\-openrouter\\-vs\\-portkey\\-2025(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/roukey\\-vs\\-openrouter\\-vs\\-portkey\\-2025(?:/)?$"}, {"page": "/checkout", "regex": "^/checkout(?:/)?$", "routeKeys": {}, "namedRegex": "^/checkout(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/cookies", "regex": "^/cookies(?:/)?$", "routeKeys": {}, "namedRegex": "^/cookies(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/debug-session", "regex": "^/debug\\-session(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug\\-session(?:/)?$"}, {"page": "/docs", "regex": "^/docs(?:/)?$", "routeKeys": {}, "namedRegex": "^/docs(?:/)?$"}, {"page": "/features", "regex": "^/features(?:/)?$", "routeKeys": {}, "namedRegex": "^/features(?:/)?$"}, {"page": "/icon.png", "regex": "^/icon\\.png(?:/)?$", "routeKeys": {}, "namedRegex": "^/icon\\.png(?:/)?$"}, {"page": "/logs", "regex": "^/logs(?:/)?$", "routeKeys": {}, "namedRegex": "^/logs(?:/)?$"}, {"page": "/my-models", "regex": "^/my\\-models(?:/)?$", "routeKeys": {}, "namedRegex": "^/my\\-models(?:/)?$"}, {"page": "/playground", "regex": "^/playground(?:/)?$", "routeKeys": {}, "namedRegex": "^/playground(?:/)?$"}, {"page": "/playground/workflows", "regex": "^/playground/workflows(?:/)?$", "routeKeys": {}, "namedRegex": "^/playground/workflows(?:/)?$"}, {"page": "/pricing", "regex": "^/pricing(?:/)?$", "routeKeys": {}, "namedRegex": "^/pricing(?:/)?$"}, {"page": "/privacy", "regex": "^/privacy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy(?:/)?$"}, {"page": "/routing-setup", "regex": "^/routing\\-setup(?:/)?$", "routeKeys": {}, "namedRegex": "^/routing\\-setup(?:/)?$"}, {"page": "/routing-strategies", "regex": "^/routing\\-strategies(?:/)?$", "routeKeys": {}, "namedRegex": "^/routing\\-strategies(?:/)?$"}, {"page": "/security", "regex": "^/security(?:/)?$", "routeKeys": {}, "namedRegex": "^/security(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/success", "regex": "^/success(?:/)?$", "routeKeys": {}, "namedRegex": "^/success(?:/)?$"}, {"page": "/terms", "regex": "^/terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms(?:/)?$"}, {"page": "/test-full-browsing", "regex": "^/test\\-full\\-browsing(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-full\\-browsing(?:/)?$"}, {"page": "/training", "regex": "^/training(?:/)?$", "routeKeys": {}, "namedRegex": "^/training(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}