{"version": 3, "file": "edge-chunks/44.js", "mappings": "sKA6FA,kBAqSA,EAA0B,OAA2C,CACrE,cACA,kBAAqC,EAAM,kBAC3C,sCAA8C,EAAO,OAAO,EAAW,gJACvE,aACA,cACA,eACA,CAAK,CACL,GACA,aACA,6BACA,OACA,EACA,UACA,CACA,cACA,2BACA,oBAEA,QACA,SC9TA,kBA2OM,EAAoB,OAA2C,CACrE,cACA,CAFuB,GAEvB,cAAqC,EAAM,kBAC3C,sCAA8C,EAAO,OAAO,EAAW,gJACvE,aACA,cACA,eACA,CAAK,CACL,GCpUO,aAEP,kCACA,kCAIA,OAHA,UACA,4BAJA,aAMA,QACA,cACA,uBACA,aACA,qBAIA,6CACA,KACA,aAIA,iBACA,oBACA,uBAMA,cAEA,SAEA,OADA,CAEA,CACA,CACA,gBACA,IAIA,EAJA,kBACA,IAQA,OAEA,WACA,EACA,MACA,kBACA,cACA,eACA,qBAiBA,OAhBA,qCACA,QACA,YAEA,OACA,qCACA,QACA,YACA,aACA,CAAa,CACb,CAAS,CACT,cACA,eACA,CAAK,EACL,0BACA,4BACA,CACA,CAoCA,QACA,eACA,gBACA,CACA,uBACA,uBACA,yBAIA,SAGA,0BACA,uBACA,uBAEA,CACA,UACA,2BACA,uBACA,wBAEA,CACA,CAYA,IAAM,EAAoB,OAA2C,CACrE,cACA,kBAAqC,EAAM,kBAC3C,sCAA8C,EAAO,OAAO,EAAW,kJACvE,aACA,cACA,eACA,CAAK,CACL,GACA,cACA,kCACA,kCACA,MAGA,KACA,qBACA,2CAA+D,SAAa,QAAQ,EAAW,+OAC/F,aACA,cACA,eACA,CAAiB,OACH,6BACd,2CAA+D,SAAa,QAAQ,EAAW,wRAC/F,aACA,cACA,eACA,CAAiB,OACH,qBACd,2CAA+D,SAAa,QAAQ,EAAW,kOAC/F,aACA,cACA,eACA,CAAiB,CACjB,CAEA,wBACA,+DAA2E,SAAa,+EAA+E,EAAW,oJAClL,aACA,cACA,eACA,CAAa,EAEb,KACA,0BAEA,2CAAuE,SAAa,OAAO,GAAY,sJACvG,aACA,cACA,eACA,CAAiB,EACjB,0DACA,EAAc,gCAEd,uDACc,gCAEd,eACA,4DAAkF,SAAa,oDAAoD,EAAW,qGAC9J,aACA,cACA,eACA,CAAiB,CAGjB,OAFA,4BACA,4BACA,CACA,CAAoH,CAEvG,CAGb,QALuB,KAA2F,EAAE,mDEtL7G,SAAS,IAChB,aADyB,MACzB,wCCpCO,OACP,SACA,eACA,YAGA,aACA,ECNA,6BACO,gBACP,SACA,SAEA,uBACA,aAIA,CAIO,kBACP,SAhBO,KAiBP,wBACA,eACA,QAAkB,eAAkB,EAEpC,SACA,kBACA,mBACA,oBAEA,QAIA,iBAEA,SAEA,iBACA,IAGA,wBACA,KACA,CACA,SACA,0BACA,gBACA,WACA,6BAGA,OAEA,CAEA,UACA,mBACA,CACA,sBAAuC,QAAS,EAAI,GAAG,EAAE,WAAU,CACnE,CAEO,sBACP,iBACA,KACA,SAEA,SACA,aAAqB,KACrB,SAA6B,EAAI,GAAG,EAAE,EACtC,aACA,MACA,MAEA,SACA,QACA,WACA,WAEA,IACA,CCnEA,mFAKA,sBAKA,QACA,iBACA,YAAoB,WAAoB,KACxC,QAEA,YAAoB,WAA6B,KACjD,yBAEA,YAAoB,WAAyB,KAC7C,wBAEA,QACA,CAAC,IAQM,cACP,SACA,IACA,IAWA,GADA,SAwFO,KACP,YAAoB,WAAgB,MACpC,sBACA,sBAIA,2BAEA,GADA,8BACA,SACA,IACA,EACA,SA3CO,KACP,iBACA,KAGA,YACA,YACA,YACA,MACA,CACA,aACA,aACA,eACA,YACA,MACA,CACA,eACA,aACA,gBACA,eACA,YACA,MACA,CACA,+CAAuD,eAAuB,EAC9E,EAmBA,IACA,CACA,EAtGA,EATA,IAGA,IAFA,SACA,KACA,OACA,gBACA,aACA,IACA,CACA,GAEA,IAGA,IAFA,QACA,IACA,OACA,gBACA,aACA,IACA,CAEA,iBACA,CAOO,cACP,SACA,MACA,+BACA,EACA,GACA,UACA,WACA,EACA,IACA,IACA,YAAoB,WAAgB,MAEpC,QADA,gBACA,CACA,QAIA,IAFA,SACA,KACA,MACA,CA0EO,gBACP,kBACA,iBACA,KAIA,YAAiC,IAAgB,KACjD,kBACA,YACA,KACA,CAEA,iBACA,sBAEA,iBACA,sBAEA,iBACA,qBAGA,qCAEA,aACA,MACA,gBACA,UACA,qCAEA,iCACA,aACA,eACA,cAEA,EACA,EA/GA,gBACA,UAGA,UAEA,cAGA,6CAA6D,QAAU,gBAAgB,EAAE,EAEzF,CACA,iBACA,CEpGA,gBAiSO,yBAAoC,qCAAyC,IACpF,uBACA,wBACA,cACA,uBACA,oBACA,EACA,gBAA2C,EAAM,UACjD,4BACA,YAA4C,EAAW,OAEvD,6BACA,0BAAkF,EAAW,OAC7F,EAD6F,CAC7F,IACA,iBACA,KAAsC,EAAiB,IAEvD,MAAuB,EAAY,GAFoB,EAOvD,KALmC,EACnC,cACA,gBACA,CAAS,EACT,aACA,CACA,CAAK,EACL,GACA,GAAW,CAAsB,CACjC,KACA,QACA,EACA,GACA,GAAW,CAAsB,CACjC,KACA,OAAgB,EAAsB,OAItC,aAJsC,CAItC,CACA,cACA,YACA,WACA,OACA,SACA,SACA,OACA,aAA6B,UAAa,KAC1C,OACA,QACA,SACA,EAAS,EACT,CACA,cGlVO,kBACP,UACA;AAAA;AAAA;AAAA;AAAA,wDAEA,YAAY,+CHIL,SAAS,CAAwB,IACxC,IAIA,EACA,EALA,UGLwF,CHKxF,OACA,CAFwC,CAExC,iBACA,KACA,KAGA,KACA,cAQA,gBAEA,oBACA,KACA,YAAoC,SAAW,gBAAmB,EAAQ,GAAG,EAAE,GAC/E,EACA,KACA,YAAgC,WAAuB,MACvD,wBACA,yBAGA,QAAkC,kBAA4B,CAC9D,CAEA,QACA,EAEA,GADA,sBACA,wBACA,YACA,YAAoC,WAAuB,MAC3D,IAAgC,0BAAuB,KACvD,EACA,mBAGA,mBAEA,CACA,OAEA,KACA,YACA,ifACA,OAGA,yKAEA,MACA,gBAEA,GADA,4BACA,aACA,gBAEA,KACA,YACA,sVACA,OAGA,mLAKA,8BAA8C,8CAA+D,4GAA4G,IAAS,wIAA+I,QAGjX,OAAgC,IAAS,CAEzC,WACA,CAHyC,GAGzC,EAA2B,QAAK,kBAChC,+BACA,OACA,cACA,EAAa,CACb,EACA,UACA,MACA,iBAAkC,oBAAsB,IACxD,gBAAkC,QAAS,OAC3C,CAAa,CACb,CACA,MACA,KACA,4MAIA,MACA,GAGA,OACA,sQACA,SAEA,EAuGA,CACA,SACA,SACA,WACA,eACA,SAIA,YACA,kBACA,yBACA,YAEA,QACA,YAEA,mBACA,QAA4C,EAAa,YACzD,qBAAuD,EAAM,uBAC7D,EAGA,QAFA,IAGA,CAAiB,EACjB,MACA,YAEA,QAKA,MAJA,oBACA,iBACA,GAA8B,EAAmB,wBAEjD,CACA,CAAa,CACb,qBAKA,8BACA,SACA,SACA,SAEA,UAAoC,MAAc,CAElD,eAAwC,CACnB,EACrB,qCACA,gBACA,CAAqB,EAErB,OACA,YACa,CACb,qBAMA,YACA,OACA,CAAa,CACJ,EAlKT,CACA,SACA,SACA,WACA,eACA,SACA,YACA,kBACA,mBACA,QAAgD,EAAa,YAC7D,qBAA2D,EAAM,uBACjE,EAGA,QAFA,IAGA,CAAqB,EACrB,MACA,YAEA,QAIA,OAHA,iBACA,GAAkC,EAAmB,wBAErD,CACA,CAAiB,CACjB,qBACA,mBAEA,WADA,cAA2D,EAAM,WACjE,UAA+E,EAAW,OAC1F,EAD0F,CAE1F,kBACA,KAAkD,EAAiB,IAEnE,MAAuC,EAAY,GAFgB,EAGnE,KADmD,IACnD,QAA0C,EAAM,IAChD,WACA,CAAqB,EACrB,OACA,GAA2B,CAAsB,CACjD,oBACA,QACA,EACA,GACA,GAA2B,CAAsB,CACjD,oBACA,OAAgC,EAAsB,OAItD,aAJsD,CAItD,CACA,cACA,UACA,gBACA,OACA,SACA,SACA,EAAyB,KACzB,aAA6C,UAAa,KAC1D,OACA,QACA,SACA,EAAyB,EACzB,CACA,YACA,UAEA,CAAiB,CACjB,qBACA,mBAEA,EADA,eAA2D,EAAM,WACjE,UAAuE,EAAW,MAClF,GADkF,GAEvD,CAAsB,CACjD,oBACA,QACA,CAGA,eACA,YACA,mBACA,OACA,SACA,SACA,EAAyB,EAEzB,CAAiB,CACJ,CA6Eb,EGtRwF,CACxF,KACA,6CACA,CAAK,KACL,EAAmB,QAAY,MAC/B,KACA,QACA,aACA,SACA,sBACA,gCAAiD,eAAO,EAAE,MAC1D,CAAa,CACJ,CACT,MACA,0BACA,CAAoB,iCACpB,KACA,WACA,gBACA,oBACA,sBACA,kBACA,SACA,CAAS,CACJ,EAoBL,OAnBA,mCAKA,oDAEA,kBACA,uBACA,oBACA,yBACA,kBACA,+BACA,MAAkB,EAAkB,CAAG,eAAH,CAAG,4BAAwC,EAC/E,qCACA,6CACA,CAAa,CAEb,CAAK,EACL,CACA,gBElDA,IAAa,CA0Eb,cACA,YACA,WAEA,OACA,SACA,mBACA,IACA,GACA,uBACA,UACA,MACA,CADmB,GACnB,cAAuC,KACvC,aACA,QAEA,kBAAsC,SACtC,QACA,CACA,eACA,WACA,eAEA,kBACA,iBACA,WACA,iBACA,OACA,CACA,KACA,EAAM,UACN,QACA,EAzGA,IAAiB,CAmIjB,gBACA,oCACA,cACA,6CAAyD,EAAK,GAE9D,WACA,cACA,4CAAwD,EAAI,GAE5D,cACA,MACA,SACA,sBACA,+BACA,6CAA6D,SAAe,GAE5E,MAAkB,mBAElB,aACA,qBACA,6CAA6D,SAAe,GAE5E,MAAkB,kBAElB,WACA,mBACA,2CAA2D,OAAa,GAExE,MAAkB,cAElB,kBAuEA,EAtEA,eAuEA,6BAtEA,sCACA,8CAA8D,UAAgB,GAE9E,MAAkB,iCAClB,CAUA,GATA,YACA,OAAkB,WAElB,UACA,OAAkB,SAElB,eACA,OAAkB,cAElB,WAIA,OAHA,4BACA,yBACA,QAEA,UACA,MAA0B,cAC1B,KACA,cACA,MAA0B,iBAC1B,KACA,YACA,MAA0B,eAC1B,KACA,SACA,+CAAmE,WAAiB,EACpF,CAEA,cAIA,OAHA,4BACA,yBACA,YAEA,OACA,aACA,MAA0B,iBAC1B,KACA,WACA,MAA0B,cAC1B,KACA,YACA,MAA0B,eAC1B,KACA,SACA,+CAAmE,WAAiB,EACpF,CAEA,QACA,EAxMA,8CAaA,oCAwBA,4BAAoD,KAAK,kCAAkC,KAAK,gBAQhG,oCACA,4BACA,QACA,mBAEA,OADA,gCACA,CACA,CAAC,IAwCD,kBACA,GACA,sBACA,iBACA,QACA,EAAM,YACN,QACA,CACA,kBACA,WACA,wBACA,iBACA,UACA,CACA,QACA,CAkGA,cACA,uBACA,SACA,IACA,4BACA,CACA,SACA,QACA,CACA", "sources": ["webpack://_N_E/./node_modules/next/dist/esm/server/request/cookies.js", "webpack://_N_E/./node_modules/next/dist/esm/server/request/headers.js", "webpack://_N_E/./node_modules/next/dist/esm/server/request/draft-mode.js", "webpack://_N_E/./node_modules/next/dist/esm/api/headers.js", "webpack://_N_E/./node_modules/@supabase/ssr/dist/module/utils/helpers.js", "webpack://_N_E/./node_modules/@supabase/ssr/dist/module/utils/constants.js", "webpack://_N_E/./node_modules/@supabase/ssr/dist/module/utils/chunker.js", "webpack://_N_E/./node_modules/@supabase/ssr/dist/module/utils/base64url.js", "webpack://_N_E/./node_modules/@supabase/ssr/dist/module/utils/index.js", "webpack://_N_E/./node_modules/@supabase/ssr/dist/module/cookies.js", "webpack://_N_E/./node_modules/@supabase/ssr/dist/module/createBrowserClient.js", "webpack://_N_E/./node_modules/@supabase/ssr/dist/module/version.js", "webpack://_N_E/./node_modules/@supabase/ssr/dist/module/createServerClient.js", "webpack://_N_E/./node_modules/@supabase/ssr/dist/module/index.js", "webpack://_N_E/./node_modules/cookie/dist/index.js"], "sourcesContent": ["import { areCookiesMutableInCurrentPhase, RequestCookiesAdapter } from '../web/spec-extension/adapters/request-cookies';\nimport { RequestCookies } from '../web/spec-extension/cookies';\nimport { workAsyncStorage } from '../app-render/work-async-storage.external';\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external';\nimport { postponeWithTracking, abortAndThrowOnSynchronousRequestDataAccess, throwToInterruptStaticGeneration, trackDynamicDataInDynamicRender, trackSynchronousRequestDataAccessInDev } from '../app-render/dynamic-rendering';\nimport { getExpectedRequestStore } from '../app-render/work-unit-async-storage.external';\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout';\nimport { makeHangingPromise } from '../dynamic-rendering-utils';\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger';\nimport { scheduleImmediate } from '../../lib/scheduler';\nimport { isRequestAPICallableInsideAfter } from './utils';\nexport function cookies() {\n    const callingExpression = 'cookies';\n    const workStore = workAsyncStorage.getStore();\n    const workUnitStore = workUnitAsyncStorage.getStore();\n    if (workStore) {\n        if (workUnitStore && workUnitStore.phase === 'after' && !isRequestAPICallableInsideAfter()) {\n            throw Object.defineProperty(new Error(// TODO(after): clarify that this only applies to pages?\n            `Route ${workStore.route} used \"cookies\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"cookies\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`), \"__NEXT_ERROR_CODE\", {\n                value: \"E88\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workStore.forceStatic) {\n            // When using forceStatic we override all other logic and always just return an empty\n            // cookies object without tracking\n            const underlyingCookies = createEmptyCookies();\n            return makeUntrackedExoticCookies(underlyingCookies);\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"cookies\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E398\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"cookies\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E157\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (workStore.dynamicShouldError) {\n            throw Object.defineProperty(new StaticGenBailoutError(`Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`cookies\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n                value: \"E549\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'prerender') {\n                // dynamicIO Prerender\n                // We don't track dynamic access here because access will be tracked when you access\n                // one of the properties of the cookies object.\n                return makeDynamicallyTrackedExoticCookies(workStore.route, workUnitStore);\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // PPR Prerender (no dynamicIO)\n                // We are prerendering with PPR. We need track dynamic access here eagerly\n                // to keep continuity with how cookies has worked in PPR without dynamicIO.\n                postponeWithTracking(workStore.route, callingExpression, workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                // Legacy Prerender\n                // We track dynamic access here so we don't need to wrap the cookies in\n                // individual property access tracking.\n                throwToInterruptStaticGeneration(callingExpression, workStore, workUnitStore);\n            }\n        }\n        // We fall through to the dynamic context below but we still track dynamic access\n        // because in dev we can still error for things like using cookies inside a cache context\n        trackDynamicDataInDynamicRender(workStore, workUnitStore);\n    }\n    // cookies is being called in a dynamic context\n    const requestStore = getExpectedRequestStore(callingExpression);\n    let underlyingCookies;\n    if (areCookiesMutableInCurrentPhase(requestStore)) {\n        // We can't conditionally return different types here based on the context.\n        // To avoid confusion, we always return the readonly type here.\n        underlyingCookies = requestStore.userspaceMutableCookies;\n    } else {\n        underlyingCookies = requestStore.cookies;\n    }\n    if (process.env.NODE_ENV === 'development' && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        return makeUntrackedExoticCookiesWithDevWarnings(underlyingCookies, workStore == null ? void 0 : workStore.route);\n    } else {\n        return makeUntrackedExoticCookies(underlyingCookies);\n    }\n}\nfunction createEmptyCookies() {\n    return RequestCookiesAdapter.seal(new RequestCookies(new Headers({})));\n}\nconst CachedCookies = new WeakMap();\nfunction makeDynamicallyTrackedExoticCookies(route, prerenderStore) {\n    const cachedPromise = CachedCookies.get(prerenderStore);\n    if (cachedPromise) {\n        return cachedPromise;\n    }\n    const promise = makeHangingPromise(prerenderStore.renderSignal, '`cookies()`');\n    CachedCookies.set(prerenderStore, promise);\n    Object.defineProperties(promise, {\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`cookies()[Symbol.iterator]()`';\n                const error = createCookiesAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        size: {\n            get () {\n                const expression = '`cookies().size`';\n                const error = createCookiesAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        get: {\n            value: function get() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().get()`';\n                } else {\n                    expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``;\n                }\n                const error = createCookiesAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        getAll: {\n            value: function getAll() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().getAll()`';\n                } else {\n                    expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``;\n                }\n                const error = createCookiesAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        has: {\n            value: function has() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().has()`';\n                } else {\n                    expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``;\n                }\n                const error = createCookiesAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        set: {\n            value: function set() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().set()`';\n                } else {\n                    const arg = arguments[0];\n                    if (arg) {\n                        expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``;\n                    } else {\n                        expression = '`cookies().set(...)`';\n                    }\n                }\n                const error = createCookiesAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        delete: {\n            value: function() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().delete()`';\n                } else if (arguments.length === 1) {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``;\n                } else {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``;\n                }\n                const error = createCookiesAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        clear: {\n            value: function clear() {\n                const expression = '`cookies().clear()`';\n                const error = createCookiesAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        toString: {\n            value: function toString() {\n                const expression = '`cookies().toString()`';\n                const error = createCookiesAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticCookies(underlyingCookies) {\n    const cachedCookies = CachedCookies.get(underlyingCookies);\n    if (cachedCookies) {\n        return cachedCookies;\n    }\n    const promise = Promise.resolve(underlyingCookies);\n    CachedCookies.set(underlyingCookies, promise);\n    Object.defineProperties(promise, {\n        [Symbol.iterator]: {\n            value: underlyingCookies[Symbol.iterator] ? underlyingCookies[Symbol.iterator].bind(underlyingCookies) : // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesIterator.bind(underlyingCookies)\n        },\n        size: {\n            get () {\n                return underlyingCookies.size;\n            }\n        },\n        get: {\n            value: underlyingCookies.get.bind(underlyingCookies)\n        },\n        getAll: {\n            value: underlyingCookies.getAll.bind(underlyingCookies)\n        },\n        has: {\n            value: underlyingCookies.has.bind(underlyingCookies)\n        },\n        set: {\n            value: underlyingCookies.set.bind(underlyingCookies)\n        },\n        delete: {\n            value: underlyingCookies.delete.bind(underlyingCookies)\n        },\n        clear: {\n            value: // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n            typeof underlyingCookies.clear === 'function' ? underlyingCookies.clear.bind(underlyingCookies) : // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.bind(underlyingCookies, promise)\n        },\n        toString: {\n            value: underlyingCookies.toString.bind(underlyingCookies)\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticCookiesWithDevWarnings(underlyingCookies, route) {\n    const cachedCookies = CachedCookies.get(underlyingCookies);\n    if (cachedCookies) {\n        return cachedCookies;\n    }\n    const promise = new Promise((resolve)=>scheduleImmediate(()=>resolve(underlyingCookies)));\n    CachedCookies.set(underlyingCookies, promise);\n    Object.defineProperties(promise, {\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`...cookies()` or similar iteration';\n                syncIODev(route, expression);\n                return underlyingCookies[Symbol.iterator] ? underlyingCookies[Symbol.iterator].apply(underlyingCookies, arguments) : // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n                // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n                // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n                // has extra properties not available on RequestCookie instances.\n                polyfilledResponseCookiesIterator.call(underlyingCookies);\n            },\n            writable: false\n        },\n        size: {\n            get () {\n                const expression = '`cookies().size`';\n                syncIODev(route, expression);\n                return underlyingCookies.size;\n            }\n        },\n        get: {\n            value: function get() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().get()`';\n                } else {\n                    expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.get.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        getAll: {\n            value: function getAll() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().getAll()`';\n                } else {\n                    expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.getAll.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        has: {\n            value: function get() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().has()`';\n                } else {\n                    expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.has.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        set: {\n            value: function set() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().set()`';\n                } else {\n                    const arg = arguments[0];\n                    if (arg) {\n                        expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``;\n                    } else {\n                        expression = '`cookies().set(...)`';\n                    }\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.set.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        delete: {\n            value: function() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().delete()`';\n                } else if (arguments.length === 1) {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``;\n                } else {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.delete.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        clear: {\n            value: function clear() {\n                const expression = '`cookies().clear()`';\n                syncIODev(route, expression);\n                // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n                return typeof underlyingCookies.clear === 'function' ? underlyingCookies.clear.apply(underlyingCookies, arguments) : // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n                // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n                // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n                // has extra properties not available on RequestCookie instances.\n                polyfilledResponseCookiesClear.call(underlyingCookies, promise);\n            },\n            writable: false\n        },\n        toString: {\n            value: function toString() {\n                const expression = '`cookies().toString()` or implicit casting';\n                syncIODev(route, expression);\n                return underlyingCookies.toString.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        }\n    });\n    return promise;\n}\nfunction describeNameArg(arg) {\n    return typeof arg === 'object' && arg !== null && typeof arg.name === 'string' ? `'${arg.name}'` : typeof arg === 'string' ? `'${arg}'` : '...';\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        trackSynchronousRequestDataAccessInDev(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(createCookiesAccessError);\nfunction createCookiesAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`cookies()\\` should be awaited before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E223\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction polyfilledResponseCookiesIterator() {\n    return this.getAll().map((c)=>[\n            c.name,\n            c\n        ]).values();\n}\nfunction polyfilledResponseCookiesClear(returnable) {\n    for (const cookie of this.getAll()){\n        this.delete(cookie.name);\n    }\n    return returnable;\n}\n\n//# sourceMappingURL=cookies.js.map", "import { HeadersAdapter } from '../web/spec-extension/adapters/headers';\nimport { workAsyncStorage } from '../app-render/work-async-storage.external';\nimport { getExpectedRequestStore } from '../app-render/work-unit-async-storage.external';\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external';\nimport { postponeWithTracking, abortAndThrowOnSynchronousRequestDataAccess, throwToInterruptStaticGeneration, trackDynamicDataInDynamicRender, trackSynchronousRequestDataAccessInDev } from '../app-render/dynamic-rendering';\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout';\nimport { makeHangingPromise } from '../dynamic-rendering-utils';\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger';\nimport { scheduleImmediate } from '../../lib/scheduler';\nimport { isRequestAPICallableInsideAfter } from './utils';\n/**\n * This function allows you to read the HTTP incoming request headers in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers) and\n * [Middleware](https://nextjs.org/docs/app/building-your-application/routing/middleware).\n *\n * Read more: [Next.js Docs: `headers`](https://nextjs.org/docs/app/api-reference/functions/headers)\n */ export function headers() {\n    const workStore = workAsyncStorage.getStore();\n    const workUnitStore = workUnitAsyncStorage.getStore();\n    if (workStore) {\n        if (workUnitStore && workUnitStore.phase === 'after' && !isRequestAPICallableInsideAfter()) {\n            throw Object.defineProperty(new Error(`Route ${workStore.route} used \"headers\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"headers\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`), \"__NEXT_ERROR_CODE\", {\n                value: \"E367\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workStore.forceStatic) {\n            // When using forceStatic we override all other logic and always just return an empty\n            // headers object without tracking\n            const underlyingHeaders = HeadersAdapter.seal(new Headers({}));\n            return makeUntrackedExoticHeaders(underlyingHeaders);\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"headers\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E304\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"headers\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E127\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (workStore.dynamicShouldError) {\n            throw Object.defineProperty(new StaticGenBailoutError(`Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`headers\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n                value: \"E525\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'prerender') {\n                // dynamicIO Prerender\n                // We don't track dynamic access here because access will be tracked when you access\n                // one of the properties of the headers object.\n                return makeDynamicallyTrackedExoticHeaders(workStore.route, workUnitStore);\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // PPR Prerender (no dynamicIO)\n                // We are prerendering with PPR. We need track dynamic access here eagerly\n                // to keep continuity with how headers has worked in PPR without dynamicIO.\n                // TODO consider switching the semantic to throw on property access instead\n                postponeWithTracking(workStore.route, 'headers', workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                // Legacy Prerender\n                // We are in a legacy static generation mode while prerendering\n                // We track dynamic access here so we don't need to wrap the headers in\n                // individual property access tracking.\n                throwToInterruptStaticGeneration('headers', workStore, workUnitStore);\n            }\n        }\n        // We fall through to the dynamic context below but we still track dynamic access\n        // because in dev we can still error for things like using headers inside a cache context\n        trackDynamicDataInDynamicRender(workStore, workUnitStore);\n    }\n    const requestStore = getExpectedRequestStore('headers');\n    if (process.env.NODE_ENV === 'development' && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        return makeUntrackedExoticHeadersWithDevWarnings(requestStore.headers, workStore == null ? void 0 : workStore.route);\n    } else {\n        return makeUntrackedExoticHeaders(requestStore.headers);\n    }\n}\nconst CachedHeaders = new WeakMap();\nfunction makeDynamicallyTrackedExoticHeaders(route, prerenderStore) {\n    const cachedHeaders = CachedHeaders.get(prerenderStore);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = makeHangingPromise(prerenderStore.renderSignal, '`headers()`');\n    CachedHeaders.set(prerenderStore, promise);\n    Object.defineProperties(promise, {\n        append: {\n            value: function append() {\n                const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``;\n                const error = createHeadersAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        delete: {\n            value: function _delete() {\n                const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``;\n                const error = createHeadersAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        get: {\n            value: function get() {\n                const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``;\n                const error = createHeadersAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        has: {\n            value: function has() {\n                const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``;\n                const error = createHeadersAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        set: {\n            value: function set() {\n                const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``;\n                const error = createHeadersAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        getSetCookie: {\n            value: function getSetCookie() {\n                const expression = '`headers().getSetCookie()`';\n                const error = createHeadersAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        forEach: {\n            value: function forEach() {\n                const expression = '`headers().forEach(...)`';\n                const error = createHeadersAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        keys: {\n            value: function keys() {\n                const expression = '`headers().keys()`';\n                const error = createHeadersAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        values: {\n            value: function values() {\n                const expression = '`headers().values()`';\n                const error = createHeadersAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        entries: {\n            value: function entries() {\n                const expression = '`headers().entries()`';\n                const error = createHeadersAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        },\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`headers()[Symbol.iterator]()`';\n                const error = createHeadersAccessError(route, expression);\n                abortAndThrowOnSynchronousRequestDataAccess(route, expression, error, prerenderStore);\n            }\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticHeaders(underlyingHeaders) {\n    const cachedHeaders = CachedHeaders.get(underlyingHeaders);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = Promise.resolve(underlyingHeaders);\n    CachedHeaders.set(underlyingHeaders, promise);\n    Object.defineProperties(promise, {\n        append: {\n            value: underlyingHeaders.append.bind(underlyingHeaders)\n        },\n        delete: {\n            value: underlyingHeaders.delete.bind(underlyingHeaders)\n        },\n        get: {\n            value: underlyingHeaders.get.bind(underlyingHeaders)\n        },\n        has: {\n            value: underlyingHeaders.has.bind(underlyingHeaders)\n        },\n        set: {\n            value: underlyingHeaders.set.bind(underlyingHeaders)\n        },\n        getSetCookie: {\n            value: underlyingHeaders.getSetCookie.bind(underlyingHeaders)\n        },\n        forEach: {\n            value: underlyingHeaders.forEach.bind(underlyingHeaders)\n        },\n        keys: {\n            value: underlyingHeaders.keys.bind(underlyingHeaders)\n        },\n        values: {\n            value: underlyingHeaders.values.bind(underlyingHeaders)\n        },\n        entries: {\n            value: underlyingHeaders.entries.bind(underlyingHeaders)\n        },\n        [Symbol.iterator]: {\n            value: underlyingHeaders[Symbol.iterator].bind(underlyingHeaders)\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticHeadersWithDevWarnings(underlyingHeaders, route) {\n    const cachedHeaders = CachedHeaders.get(underlyingHeaders);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = new Promise((resolve)=>scheduleImmediate(()=>resolve(underlyingHeaders)));\n    CachedHeaders.set(underlyingHeaders, promise);\n    Object.defineProperties(promise, {\n        append: {\n            value: function append() {\n                const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.append.apply(underlyingHeaders, arguments);\n            }\n        },\n        delete: {\n            value: function _delete() {\n                const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.delete.apply(underlyingHeaders, arguments);\n            }\n        },\n        get: {\n            value: function get() {\n                const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.get.apply(underlyingHeaders, arguments);\n            }\n        },\n        has: {\n            value: function has() {\n                const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.has.apply(underlyingHeaders, arguments);\n            }\n        },\n        set: {\n            value: function set() {\n                const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.set.apply(underlyingHeaders, arguments);\n            }\n        },\n        getSetCookie: {\n            value: function getSetCookie() {\n                const expression = '`headers().getSetCookie()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.getSetCookie.apply(underlyingHeaders, arguments);\n            }\n        },\n        forEach: {\n            value: function forEach() {\n                const expression = '`headers().forEach(...)`';\n                syncIODev(route, expression);\n                return underlyingHeaders.forEach.apply(underlyingHeaders, arguments);\n            }\n        },\n        keys: {\n            value: function keys() {\n                const expression = '`headers().keys()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.keys.apply(underlyingHeaders, arguments);\n            }\n        },\n        values: {\n            value: function values() {\n                const expression = '`headers().values()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.values.apply(underlyingHeaders, arguments);\n            }\n        },\n        entries: {\n            value: function entries() {\n                const expression = '`headers().entries()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.entries.apply(underlyingHeaders, arguments);\n            }\n        },\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`...headers()` or similar iteration';\n                syncIODev(route, expression);\n                return underlyingHeaders[Symbol.iterator].apply(underlyingHeaders, arguments);\n            }\n        }\n    });\n    return promise;\n}\nfunction describeNameArg(arg) {\n    return typeof arg === 'string' ? `'${arg}'` : '...';\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        trackSynchronousRequestDataAccessInDev(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(createHeadersAccessError);\nfunction createHeadersAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`headers()\\` should be awaited before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E277\",\n        enumerable: false,\n        configurable: true\n    });\n}\n\n//# sourceMappingURL=headers.js.map", "import { getDraftModeProviderForCacheScope, throwForMissingRequestStore } from '../app-render/work-unit-async-storage.external';\nimport { workAsyncStorage } from '../app-render/work-async-storage.external';\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external';\nimport { abortAndThrowOnSynchronousRequestDataAccess, postponeWithTracking, trackSynchronousRequestDataAccessInDev } from '../app-render/dynamic-rendering';\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger';\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout';\nimport { DynamicServerError } from '../../client/components/hooks-server-context';\nexport function draftMode() {\n    const callingExpression = 'draftMode';\n    const workStore = workAsyncStorage.getStore();\n    const workUnitStore = workUnitAsyncStorage.getStore();\n    if (!workStore || !workUnitStore) {\n        throwForMissingRequestStore(callingExpression);\n    }\n    switch(workUnitStore.type){\n        case 'request':\n            return createOrGetCachedExoticDraftMode(workUnitStore.draftMode, workStore);\n        case 'cache':\n        case 'unstable-cache':\n            // Inside of `\"use cache\"` or `unstable_cache`, draft mode is available if\n            // the outmost work unit store is a request store, and if draft mode is\n            // enabled.\n            const draftModeProvider = getDraftModeProviderForCacheScope(workStore, workUnitStore);\n            if (draftModeProvider) {\n                return createOrGetCachedExoticDraftMode(draftModeProvider, workStore);\n            }\n        // Otherwise, we fall through to providing an empty draft mode.\n        // eslint-disable-next-line no-fallthrough\n        case 'prerender':\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n            // Return empty draft mode\n            if (process.env.NODE_ENV === 'development' && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n                const route = workStore == null ? void 0 : workStore.route;\n                return createExoticDraftModeWithDevWarnings(null, route);\n            } else {\n                return createExoticDraftMode(null);\n            }\n        default:\n            const _exhaustiveCheck = workUnitStore;\n            return _exhaustiveCheck;\n    }\n}\nfunction createOrGetCachedExoticDraftMode(draftModeProvider, workStore) {\n    const cachedDraftMode = CachedDraftModes.get(draftMode);\n    if (cachedDraftMode) {\n        return cachedDraftMode;\n    }\n    let promise;\n    if (process.env.NODE_ENV === 'development' && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        const route = workStore == null ? void 0 : workStore.route;\n        promise = createExoticDraftModeWithDevWarnings(draftModeProvider, route);\n    } else {\n        promise = createExoticDraftMode(draftModeProvider);\n    }\n    CachedDraftModes.set(draftModeProvider, promise);\n    return promise;\n}\nconst CachedDraftModes = new WeakMap();\nfunction createExoticDraftMode(underlyingProvider) {\n    const instance = new DraftMode(underlyingProvider);\n    const promise = Promise.resolve(instance);\n    Object.defineProperty(promise, 'isEnabled', {\n        get () {\n            return instance.isEnabled;\n        },\n        set (newValue) {\n            Object.defineProperty(promise, 'isEnabled', {\n                value: newValue,\n                writable: true,\n                enumerable: true\n            });\n        },\n        enumerable: true,\n        configurable: true\n    });\n    promise.enable = instance.enable.bind(instance);\n    promise.disable = instance.disable.bind(instance);\n    return promise;\n}\nfunction createExoticDraftModeWithDevWarnings(underlyingProvider, route) {\n    const instance = new DraftMode(underlyingProvider);\n    const promise = Promise.resolve(instance);\n    Object.defineProperty(promise, 'isEnabled', {\n        get () {\n            const expression = '`draftMode().isEnabled`';\n            syncIODev(route, expression);\n            return instance.isEnabled;\n        },\n        set (newValue) {\n            Object.defineProperty(promise, 'isEnabled', {\n                value: newValue,\n                writable: true,\n                enumerable: true\n            });\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(promise, 'enable', {\n        value: function get() {\n            const expression = '`draftMode().enable()`';\n            syncIODev(route, expression);\n            return instance.enable.apply(instance, arguments);\n        }\n    });\n    Object.defineProperty(promise, 'disable', {\n        value: function get() {\n            const expression = '`draftMode().disable()`';\n            syncIODev(route, expression);\n            return instance.disable.apply(instance, arguments);\n        }\n    });\n    return promise;\n}\nclass DraftMode {\n    constructor(provider){\n        this._provider = provider;\n    }\n    get isEnabled() {\n        if (this._provider !== null) {\n            return this._provider.isEnabled;\n        }\n        return false;\n    }\n    enable() {\n        // We have a store we want to track dynamic data access to ensure we\n        // don't statically generate routes that manipulate draft mode.\n        trackDynamicDraftMode('draftMode().enable()');\n        if (this._provider !== null) {\n            this._provider.enable();\n        }\n    }\n    disable() {\n        trackDynamicDraftMode('draftMode().disable()');\n        if (this._provider !== null) {\n            this._provider.disable();\n        }\n    }\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        trackSynchronousRequestDataAccessInDev(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(createDraftModeAccessError);\nfunction createDraftModeAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`draftMode()\\` should be awaited before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E377\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction trackDynamicDraftMode(expression) {\n    const store = workAsyncStorage.getStore();\n    const workUnitStore = workUnitAsyncStorage.getStore();\n    if (store) {\n        // We have a store we want to track dynamic data access to ensure we\n        // don't statically generate routes that manipulate draft mode.\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(`Route ${store.route} used \"${expression}\" inside \"use cache\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E246\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(`Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E259\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.phase === 'after') {\n                throw Object.defineProperty(new Error(`Route ${store.route} used \"${expression}\" inside \\`after\\`. The enabled status of draftMode can be read inside \\`after\\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E348\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (store.dynamicShouldError) {\n            throw Object.defineProperty(new StaticGenBailoutError(`Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n                value: \"E553\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'prerender') {\n                // dynamicIO Prerender\n                const error = Object.defineProperty(new Error(`Route ${store.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E126\",\n                    enumerable: false,\n                    configurable: true\n                });\n                abortAndThrowOnSynchronousRequestDataAccess(store.route, expression, error, workUnitStore);\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // PPR Prerender\n                postponeWithTracking(store.route, expression, workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                // legacy Prerender\n                workUnitStore.revalidate = 0;\n                const err = Object.defineProperty(new DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E558\",\n                    enumerable: false,\n                    configurable: true\n                });\n                store.dynamicUsageDescription = expression;\n                store.dynamicUsageStack = err.stack;\n                throw err;\n            } else if (process.env.NODE_ENV === 'development' && workUnitStore && workUnitStore.type === 'request') {\n                workUnitStore.usedDynamic = true;\n            }\n        }\n    }\n}\n\n//# sourceMappingURL=draft-mode.js.map", "export * from '../server/request/cookies';\nexport * from '../server/request/headers';\nexport * from '../server/request/draft-mode';\n\n//# sourceMappingURL=headers.js.map", "import { parse as cookieParse, serialize as cookieSerialize } from \"cookie\";\n/**\n * @deprecated Since v0.4.0: Please use {@link parseCookieHeader}. `parse` will\n * not be available for import starting v1.0.0 of `@supabase/ssr`.\n */\nexport const parse = cookieParse;\n/**\n * @deprecated Since v0.4.0: Please use {@link serializeCookieHeader}.\n * `serialize` will not be available for import starting v1.0.0 of\n * `@supabase/ssr`.\n */\nexport const serialize = cookieSerialize;\n/**\n * Parses the `Cookie` HTTP header into an array of cookie name-value objects.\n *\n * @param header The `Cookie` HTTP header. Decodes cookie names and values from\n * URI encoding first.\n */\nexport function parseCookieHeader(header) {\n    const parsed = cookieParse(header);\n    return Object.keys(parsed ?? {}).map((name) => ({\n        name,\n        value: parsed[name],\n    }));\n}\n/**\n * Converts the arguments to a valid `Set-Cookie` header. Non US-ASCII chars\n * and other forbidden cookie chars will be URI encoded.\n *\n * @param name Name of cookie.\n * @param value Value of cookie.\n */\nexport function serializeCookieHeader(name, value, options) {\n    return cookieSerialize(name, value, options);\n}\nexport function isBrowser() {\n    return (typeof window !== \"undefined\" && typeof window.document !== \"undefined\");\n}\n//# sourceMappingURL=helpers.js.map", "export const DEFAULT_COOKIE_OPTIONS = {\n    path: \"/\",\n    sameSite: \"lax\",\n    httpOnly: false,\n    // https://developer.chrome.com/blog/cookie-max-age-expires\n    // https://httpwg.org/http-extensions/draft-ietf-httpbis-rfc6265bis.html#name-cookie-lifetime-limits\n    maxAge: 400 * 24 * 60 * 60,\n};\n//# sourceMappingURL=constants.js.map", "export const MAX_CHUNK_SIZE = 3180;\nconst CHUNK_LIKE_REGEX = /^(.*)[.](0|[1-9][0-9]*)$/;\nexport function isChunkLike(cookieName, key) {\n    if (cookieName === key) {\n        return true;\n    }\n    const chunkLike = cookieName.match(CHUNK_LIKE_REGEX);\n    if (chunkLike && chunkLike[1] === key) {\n        return true;\n    }\n    return false;\n}\n/**\n * create chunks from a string and return an array of object\n */\nexport function createChunks(key, value, chunkSize) {\n    const resolvedChunkSize = chunkSize ?? MAX_CHUNK_SIZE;\n    let encodedValue = encodeURIComponent(value);\n    if (encodedValue.length <= resolvedChunkSize) {\n        return [{ name: key, value }];\n    }\n    const chunks = [];\n    while (encodedValue.length > 0) {\n        let encodedChunkHead = encodedValue.slice(0, resolvedChunkSize);\n        const lastEscapePos = encodedChunkHead.lastIndexOf(\"%\");\n        // Check if the last escaped character is truncated.\n        if (lastEscapePos > resolvedChunkSize - 3) {\n            // If so, reslice the string to exclude the whole escape sequence.\n            // We only reduce the size of the string as the chunk must\n            // be smaller than the chunk size.\n            encodedChunkHead = encodedChunkHead.slice(0, lastEscapePos);\n        }\n        let valueHead = \"\";\n        // Check if the chunk was split along a valid unicode boundary.\n        while (encodedChunkHead.length > 0) {\n            try {\n                // Try to decode the chunk back and see if it is valid.\n                // Stop when the chunk is valid.\n                valueHead = decodeURIComponent(encodedChunkHead);\n                break;\n            }\n            catch (error) {\n                if (error instanceof URIError &&\n                    encodedChunkHead.at(-3) === \"%\" &&\n                    encodedChunkHead.length > 3) {\n                    encodedChunkHead = encodedChunkHead.slice(0, encodedChunkHead.length - 3);\n                }\n                else {\n                    throw error;\n                }\n            }\n        }\n        chunks.push(valueHead);\n        encodedValue = encodedValue.slice(encodedChunkHead.length);\n    }\n    return chunks.map((value, i) => ({ name: `${key}.${i}`, value }));\n}\n// Get fully constructed chunks\nexport async function combineChunks(key, retrieveChunk) {\n    const value = await retrieveChunk(key);\n    if (value) {\n        return value;\n    }\n    let values = [];\n    for (let i = 0;; i++) {\n        const chunkName = `${key}.${i}`;\n        const chunk = await retrieveChunk(chunkName);\n        if (!chunk) {\n            break;\n        }\n        values.push(chunk);\n    }\n    if (values.length > 0) {\n        return values.join(\"\");\n    }\n    return null;\n}\nexport async function deleteChunks(key, retrieveChunk, removeChunk) {\n    const value = await retrieveChunk(key);\n    if (value) {\n        await removeChunk(key);\n    }\n    for (let i = 0;; i++) {\n        const chunkName = `${key}.${i}`;\n        const chunk = await retrieveChunk(chunkName);\n        if (!chunk) {\n            break;\n        }\n        await removeChunk(chunkName);\n    }\n}\n//# sourceMappingURL=chunker.js.map", "/**\n * Avoid modifying this file. It's part of\n * https://github.com/supabase-community/base64url-js.  Submit all fixes on\n * that repo!\n */\n/**\n * An array of characters that encode 6 bits into a Base64-URL alphabet\n * character.\n */\nconst TO_BASE64URL = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_\".split(\"\");\n/**\n * An array of characters that can appear in a Base64-URL encoded string but\n * should be ignored.\n */\nconst IGNORE_BASE64URL = \" \\t\\n\\r=\".split(\"\");\n/**\n * An array of 128 numbers that map a Base64-URL character to 6 bits, or if -2\n * used to skip the character, or if -1 used to error out.\n */\nconst FROM_BASE64URL = (() => {\n    const charMap = new Array(128);\n    for (let i = 0; i < charMap.length; i += 1) {\n        charMap[i] = -1;\n    }\n    for (let i = 0; i < IGNORE_BASE64URL.length; i += 1) {\n        charMap[IGNORE_BASE64URL[i].charCodeAt(0)] = -2;\n    }\n    for (let i = 0; i < TO_BASE64URL.length; i += 1) {\n        charMap[TO_BASE64URL[i].charCodeAt(0)] = i;\n    }\n    return charMap;\n})();\n/**\n * Converts a JavaScript string (which may include any valid character) into a\n * Base64-URL encoded string. The string is first encoded in UTF-8 which is\n * then encoded as Base64-URL.\n *\n * @param str The string to convert.\n */\nexport function stringToBase64URL(str) {\n    const base64 = [];\n    let queue = 0;\n    let queuedBits = 0;\n    const emitter = (byte) => {\n        queue = (queue << 8) | byte;\n        queuedBits += 8;\n        while (queuedBits >= 6) {\n            const pos = (queue >> (queuedBits - 6)) & 63;\n            base64.push(TO_BASE64URL[pos]);\n            queuedBits -= 6;\n        }\n    };\n    stringToUTF8(str, emitter);\n    if (queuedBits > 0) {\n        queue = queue << (6 - queuedBits);\n        queuedBits = 6;\n        while (queuedBits >= 6) {\n            const pos = (queue >> (queuedBits - 6)) & 63;\n            base64.push(TO_BASE64URL[pos]);\n            queuedBits -= 6;\n        }\n    }\n    return base64.join(\"\");\n}\n/**\n * Converts a Base64-URL encoded string into a JavaScript string. It is assumed\n * that the underlying string has been encoded as UTF-8.\n *\n * @param str The Base64-URL encoded string.\n */\nexport function stringFromBase64URL(str) {\n    const conv = [];\n    const emit = (codepoint) => {\n        conv.push(String.fromCodePoint(codepoint));\n    };\n    const state = {\n        utf8seq: 0,\n        codepoint: 0,\n    };\n    let queue = 0;\n    let queuedBits = 0;\n    for (let i = 0; i < str.length; i += 1) {\n        const codepoint = str.charCodeAt(i);\n        const bits = FROM_BASE64URL[codepoint];\n        if (bits > -1) {\n            // valid Base64-URL character\n            queue = (queue << 6) | bits;\n            queuedBits += 6;\n            while (queuedBits >= 8) {\n                stringFromUTF8((queue >> (queuedBits - 8)) & 0xff, state, emit);\n                queuedBits -= 8;\n            }\n        }\n        else if (bits === -2) {\n            // ignore spaces, tabs, newlines, =\n            continue;\n        }\n        else {\n            throw new Error(`Invalid Base64-URL character \"${str.at(i)}\" at position ${i}`);\n        }\n    }\n    return conv.join(\"\");\n}\n/**\n * Converts a Unicode codepoint to a multi-byte UTF-8 sequence.\n *\n * @param codepoint The Unicode codepoint.\n * @param emit      Function which will be called for each UTF-8 byte that represents the codepoint.\n */\nexport function codepointToUTF8(codepoint, emit) {\n    if (codepoint <= 0x7f) {\n        emit(codepoint);\n        return;\n    }\n    else if (codepoint <= 0x7ff) {\n        emit(0xc0 | (codepoint >> 6));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0xffff) {\n        emit(0xe0 | (codepoint >> 12));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0x10ffff) {\n        emit(0xf0 | (codepoint >> 18));\n        emit(0x80 | ((codepoint >> 12) & 0x3f));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    throw new Error(`Unrecognized Unicode codepoint: ${codepoint.toString(16)}`);\n}\n/**\n * Converts a JavaScript string to a sequence of UTF-8 bytes.\n *\n * @param str  The string to convert to UTF-8.\n * @param emit Function which will be called for each UTF-8 byte of the string.\n */\nexport function stringToUTF8(str, emit) {\n    for (let i = 0; i < str.length; i += 1) {\n        let codepoint = str.charCodeAt(i);\n        if (codepoint > 0xd7ff && codepoint <= 0xdbff) {\n            // most UTF-16 codepoints are Unicode codepoints, except values in this\n            // range where the next UTF-16 codepoint needs to be combined with the\n            // current one to get the Unicode codepoint\n            const highSurrogate = ((codepoint - 0xd800) * 0x400) & 0xffff;\n            const lowSurrogate = (str.charCodeAt(i + 1) - 0xdc00) & 0xffff;\n            codepoint = (lowSurrogate | highSurrogate) + 0x10000;\n            i += 1;\n        }\n        codepointToUTF8(codepoint, emit);\n    }\n}\n/**\n * Converts a UTF-8 byte to a Unicode codepoint.\n *\n * @param byte  The UTF-8 byte next in the sequence.\n * @param state The shared state between consecutive UTF-8 bytes in the\n *              sequence, an object with the shape `{ utf8seq: 0, codepoint: 0 }`.\n * @param emit  Function which will be called for each codepoint.\n */\nexport function stringFromUTF8(byte, state, emit) {\n    if (state.utf8seq === 0) {\n        if (byte <= 0x7f) {\n            emit(byte);\n            return;\n        }\n        // count the number of 1 leading bits until you reach 0\n        for (let leadingBit = 1; leadingBit < 6; leadingBit += 1) {\n            if (((byte >> (7 - leadingBit)) & 1) === 0) {\n                state.utf8seq = leadingBit;\n                break;\n            }\n        }\n        if (state.utf8seq === 2) {\n            state.codepoint = byte & 31;\n        }\n        else if (state.utf8seq === 3) {\n            state.codepoint = byte & 15;\n        }\n        else if (state.utf8seq === 4) {\n            state.codepoint = byte & 7;\n        }\n        else {\n            throw new Error(\"Invalid UTF-8 sequence\");\n        }\n        state.utf8seq -= 1;\n    }\n    else if (state.utf8seq > 0) {\n        if (byte <= 0x7f) {\n            throw new Error(\"Invalid UTF-8 sequence\");\n        }\n        state.codepoint = (state.codepoint << 6) | (byte & 63);\n        state.utf8seq -= 1;\n        if (state.utf8seq === 0) {\n            emit(state.codepoint);\n        }\n    }\n}\n//# sourceMappingURL=base64url.js.map", "export * from \"./helpers\";\nexport * from \"./constants\";\nexport * from \"./chunker\";\nexport * from \"./base64url\";\n//# sourceMappingURL=index.js.map", "import { parse, serialize } from \"cookie\";\nimport { DEFAULT_COOKIE_OPTIONS, combineChunks, createChunks, isBrowser, isChunkLike, stringFromBase64URL, stringToBase64URL, } from \"./utils\";\nconst BASE64_PREFIX = \"base64-\";\n/**\n * Creates a storage client that handles cookies correctly for browser and\n * server clients with or without properly provided cookie methods.\n *\n * @param options The options passed to createBrowserClient or createServer client.\n *\n * @param isServerClient Whether it's called from createServerClient.\n */\nexport function createStorageFromOptions(options, isServerClient) {\n    const cookies = options.cookies ?? null;\n    const cookieEncoding = options.cookieEncoding;\n    const setItems = {};\n    const removedItems = {};\n    let getAll;\n    let setAll;\n    if (cookies) {\n        if (\"get\" in cookies) {\n            // Just get is not enough, because the client needs to see what cookies\n            // are already set and unset them if necessary. To attempt to fix this\n            // behavior for most use cases, we pass \"hints\" which is the keys of the\n            // storage items. They are then converted to their corresponding cookie\n            // chunk names and are fetched with get. Only 5 chunks are fetched, which\n            // should be enough for the majority of use cases, but does not solve\n            // those with very large sessions.\n            const getWithHints = async (keyHints) => {\n                // optimistically find the first 5 potential chunks for the specified key\n                const chunkNames = keyHints.flatMap((keyHint) => [\n                    keyHint,\n                    ...Array.from({ length: 5 }).map((_, i) => `${keyHint}.${i}`),\n                ]);\n                const chunks = [];\n                for (let i = 0; i < chunkNames.length; i += 1) {\n                    const value = await cookies.get(chunkNames[i]);\n                    if (!value && typeof value !== \"string\") {\n                        continue;\n                    }\n                    chunks.push({ name: chunkNames[i], value });\n                }\n                // TODO: detect and log stale chunks error\n                return chunks;\n            };\n            getAll = async (keyHints) => await getWithHints(keyHints);\n            if (\"set\" in cookies && \"remove\" in cookies) {\n                setAll = async (setCookies) => {\n                    for (let i = 0; i < setCookies.length; i += 1) {\n                        const { name, value, options } = setCookies[i];\n                        if (value) {\n                            await cookies.set(name, value, options);\n                        }\n                        else {\n                            await cookies.remove(name, options);\n                        }\n                    }\n                };\n            }\n            else if (isServerClient) {\n                setAll = async () => {\n                    console.warn(\"@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.\");\n                };\n            }\n            else {\n                throw new Error(\"@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)\");\n            }\n        }\n        else if (\"getAll\" in cookies) {\n            getAll = async () => await cookies.getAll();\n            if (\"setAll\" in cookies) {\n                setAll = cookies.setAll;\n            }\n            else if (isServerClient) {\n                setAll = async () => {\n                    console.warn(\"@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.\");\n                };\n            }\n            else {\n                throw new Error(\"@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)\");\n            }\n        }\n        else {\n            // neither get nor getAll is present on cookies, only will occur if pure JavaScript is used, but cookies is an object\n            throw new Error(`@supabase/ssr: ${isServerClient ? \"createServerClient\" : \"createBrowserClient\"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${isBrowser() ? \" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.\" : \"\"}`);\n        }\n    }\n    else if (!isServerClient && isBrowser()) {\n        // The environment is browser, so use the document.cookie API to implement getAll and setAll.\n        const noHintGetAll = () => {\n            const parsed = parse(document.cookie);\n            return Object.keys(parsed).map((name) => ({\n                name,\n                value: parsed[name] ?? \"\",\n            }));\n        };\n        getAll = () => noHintGetAll();\n        setAll = (setCookies) => {\n            setCookies.forEach(({ name, value, options }) => {\n                document.cookie = serialize(name, value, options);\n            });\n        };\n    }\n    else if (isServerClient) {\n        throw new Error(\"@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)\");\n    }\n    else {\n        // getting cookies when there's no window but we're in browser mode can be OK, because the developer probably is not using auth functions\n        getAll = () => {\n            return [];\n        };\n        // this is NOT OK because the developer is using auth functions that require setting some state, so that must error out\n        setAll = () => {\n            throw new Error(\"@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed\");\n        };\n    }\n    if (!isServerClient) {\n        // This is the storage client to be used in browsers. It only\n        // works on the cookies abstraction, unlike the server client\n        // which only uses cookies to read the initial state. When an\n        // item is set, cookies are both cleared and set to values so\n        // that stale chunks are not left remaining.\n        return {\n            getAll, // for type consistency\n            setAll, // for type consistency\n            setItems, // for type consistency\n            removedItems, // for type consistency\n            storage: {\n                isServer: false,\n                getItem: async (key) => {\n                    const allCookies = await getAll([key]);\n                    const chunkedCookie = await combineChunks(key, async (chunkName) => {\n                        const cookie = allCookies?.find(({ name }) => name === chunkName) || null;\n                        if (!cookie) {\n                            return null;\n                        }\n                        return cookie.value;\n                    });\n                    if (!chunkedCookie) {\n                        return null;\n                    }\n                    let decoded = chunkedCookie;\n                    if (chunkedCookie.startsWith(BASE64_PREFIX)) {\n                        decoded = stringFromBase64URL(chunkedCookie.substring(BASE64_PREFIX.length));\n                    }\n                    return decoded;\n                },\n                setItem: async (key, value) => {\n                    const allCookies = await getAll([key]);\n                    const cookieNames = allCookies?.map(({ name }) => name) || [];\n                    const removeCookies = new Set(cookieNames.filter((name) => isChunkLike(name, key)));\n                    let encoded = value;\n                    if (cookieEncoding === \"base64url\") {\n                        encoded = BASE64_PREFIX + stringToBase64URL(value);\n                    }\n                    const setCookies = createChunks(key, encoded);\n                    setCookies.forEach(({ name }) => {\n                        removeCookies.delete(name);\n                    });\n                    const removeCookieOptions = {\n                        ...DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: 0,\n                    };\n                    const setCookieOptions = {\n                        ...DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: DEFAULT_COOKIE_OPTIONS.maxAge,\n                    };\n                    // the NextJS cookieStore API can get confused if the `name` from\n                    // options.cookieOptions leaks\n                    delete removeCookieOptions.name;\n                    delete setCookieOptions.name;\n                    const allToSet = [\n                        ...[...removeCookies].map((name) => ({\n                            name,\n                            value: \"\",\n                            options: removeCookieOptions,\n                        })),\n                        ...setCookies.map(({ name, value }) => ({\n                            name,\n                            value,\n                            options: setCookieOptions,\n                        })),\n                    ];\n                    if (allToSet.length > 0) {\n                        await setAll(allToSet);\n                    }\n                },\n                removeItem: async (key) => {\n                    const allCookies = await getAll([key]);\n                    const cookieNames = allCookies?.map(({ name }) => name) || [];\n                    const removeCookies = cookieNames.filter((name) => isChunkLike(name, key));\n                    const removeCookieOptions = {\n                        ...DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: 0,\n                    };\n                    // the NextJS cookieStore API can get confused if the `name` from\n                    // options.cookieOptions leaks\n                    delete removeCookieOptions.name;\n                    if (removeCookies.length > 0) {\n                        await setAll(removeCookies.map((name) => ({\n                            name,\n                            value: \"\",\n                            options: removeCookieOptions,\n                        })));\n                    }\n                },\n            },\n        };\n    }\n    // This is the server client. It only uses getAll to read the initial\n    // state. Any subsequent changes to the items is persisted in the\n    // setItems and removedItems objects. createServerClient *must* use\n    // getAll, setAll and the values in setItems and removedItems to\n    // persist the changes *at once* when appropriate (usually only when\n    // the TOKEN_REFRESHED, USER_UPDATED or SIGNED_OUT events are fired by\n    // the Supabase Auth client).\n    return {\n        getAll,\n        setAll,\n        setItems,\n        removedItems,\n        storage: {\n            // to signal to the libraries that these cookies are\n            // coming from a server environment and their value\n            // should not be trusted\n            isServer: true,\n            getItem: async (key) => {\n                if (typeof setItems[key] === \"string\") {\n                    return setItems[key];\n                }\n                if (removedItems[key]) {\n                    return null;\n                }\n                const allCookies = await getAll([key]);\n                const chunkedCookie = await combineChunks(key, async (chunkName) => {\n                    const cookie = allCookies?.find(({ name }) => name === chunkName) || null;\n                    if (!cookie) {\n                        return null;\n                    }\n                    return cookie.value;\n                });\n                if (!chunkedCookie) {\n                    return null;\n                }\n                let decoded = chunkedCookie;\n                if (typeof chunkedCookie === \"string\" &&\n                    chunkedCookie.startsWith(BASE64_PREFIX)) {\n                    decoded = stringFromBase64URL(chunkedCookie.substring(BASE64_PREFIX.length));\n                }\n                return decoded;\n            },\n            setItem: async (key, value) => {\n                // We don't have an `onAuthStateChange` event that can let us know that\n                // the PKCE code verifier is being set. Therefore, if we see it being\n                // set, we need to apply the storage (call `setAll` so the cookie is\n                // set properly).\n                if (key.endsWith(\"-code-verifier\")) {\n                    await applyServerStorage({\n                        getAll,\n                        setAll,\n                        // pretend only that the code verifier was set\n                        setItems: { [key]: value },\n                        // pretend that nothing was removed\n                        removedItems: {},\n                    }, {\n                        cookieOptions: options?.cookieOptions ?? null,\n                        cookieEncoding,\n                    });\n                }\n                setItems[key] = value;\n                delete removedItems[key];\n            },\n            removeItem: async (key) => {\n                // Intentionally not applying the storage when the key is the PKCE code\n                // verifier, as usually right after it's removed other items are set,\n                // so application of the storage will be handled by the\n                // `onAuthStateChange` callback that follows removal -- usually as part\n                // of the `exchangeCodeForSession` call.\n                delete setItems[key];\n                removedItems[key] = true;\n            },\n        },\n    };\n}\n/**\n * When createServerClient needs to apply the created storage to cookies, it\n * should call this function which handles correcly setting cookies for stored\n * and removed items in the storage.\n */\nexport async function applyServerStorage({ getAll, setAll, setItems, removedItems, }, options) {\n    const cookieEncoding = options.cookieEncoding;\n    const cookieOptions = options.cookieOptions ?? null;\n    const allCookies = await getAll([\n        ...(setItems ? Object.keys(setItems) : []),\n        ...(removedItems ? Object.keys(removedItems) : []),\n    ]);\n    const cookieNames = allCookies?.map(({ name }) => name) || [];\n    const removeCookies = Object.keys(removedItems).flatMap((itemName) => {\n        return cookieNames.filter((name) => isChunkLike(name, itemName));\n    });\n    const setCookies = Object.keys(setItems).flatMap((itemName) => {\n        const removeExistingCookiesForItem = new Set(cookieNames.filter((name) => isChunkLike(name, itemName)));\n        let encoded = setItems[itemName];\n        if (cookieEncoding === \"base64url\") {\n            encoded = BASE64_PREFIX + stringToBase64URL(encoded);\n        }\n        const chunks = createChunks(itemName, encoded);\n        chunks.forEach((chunk) => {\n            removeExistingCookiesForItem.delete(chunk.name);\n        });\n        removeCookies.push(...removeExistingCookiesForItem);\n        return chunks;\n    });\n    const removeCookieOptions = {\n        ...DEFAULT_COOKIE_OPTIONS,\n        ...cookieOptions,\n        maxAge: 0,\n    };\n    const setCookieOptions = {\n        ...DEFAULT_COOKIE_OPTIONS,\n        ...cookieOptions,\n        maxAge: DEFAULT_COOKIE_OPTIONS.maxAge,\n    };\n    // the NextJS cookieStore API can get confused if the `name` from\n    // options.cookieOptions leaks\n    delete removeCookieOptions.name;\n    delete setCookieOptions.name;\n    await setAll([\n        ...removeCookies.map((name) => ({\n            name,\n            value: \"\",\n            options: removeCookieOptions,\n        })),\n        ...setCookies.map(({ name, value }) => ({\n            name,\n            value,\n            options: setCookieOptions,\n        })),\n    ]);\n}\n//# sourceMappingURL=cookies.js.map", "import { createClient } from \"@supabase/supabase-js\";\nimport { VERSION } from \"./version\";\nimport { isBrowser } from \"./utils\";\nimport { createStorageFromOptions } from \"./cookies\";\nlet cachedBrowserClient;\nexport function createBrowserClient(supabaseUrl, supabaseKey, options) {\n    // singleton client is created only if isSingleton is set to true, or if isSingleton is not defined and we detect a browser\n    const shouldUseSingleton = options?.isSingleton === true ||\n        ((!options || !(\"isSingleton\" in options)) && isBrowser());\n    if (shouldUseSingleton && cachedBrowserClient) {\n        return cachedBrowserClient;\n    }\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(`@supabase/ssr: Your project's URL and API key are required to create a Supabase client!\\n\\nCheck your Supabase project's API settings to find these values\\n\\nhttps://supabase.com/dashboard/project/_/settings/api`);\n    }\n    const { storage } = createStorageFromOptions({\n        ...options,\n        cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n    }, false);\n    const client = createClient(supabaseUrl, supabaseKey, {\n        ...options,\n        global: {\n            ...options?.global,\n            headers: {\n                ...options?.global?.headers,\n                \"X-Client-Info\": `supabase-ssr/${VERSION} createBrowserClient`,\n            },\n        },\n        auth: {\n            ...options?.auth,\n            ...(options?.cookieOptions?.name\n                ? { storageKey: options.cookieOptions.name }\n                : null),\n            flowType: \"pkce\",\n            autoRefreshToken: isBrowser(),\n            detectSessionInUrl: isBrowser(),\n            persistSession: true,\n            storage,\n        },\n    });\n    if (shouldUseSingleton) {\n        cachedBrowserClient = client;\n    }\n    return client;\n}\n//# sourceMappingURL=createBrowserClient.js.map", "export const VERSION = '0.6.1';\n//# sourceMappingURL=version.js.map", "import { createClient, } from \"@supabase/supabase-js\";\nimport { VERSION } from \"./version\";\nimport { createStorageFromOptions, applyServerStorage } from \"./cookies\";\nexport function createServerClient(supabaseUrl, supabaseKey, options) {\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(`Your project's URL and Key are required to create a Supabase client!\\n\\nCheck your Supabase project's API settings to find these values\\n\\nhttps://supabase.com/dashboard/project/_/settings/api`);\n    }\n    const { storage, getAll, setAll, setItems, removedItems } = createStorageFromOptions({\n        ...options,\n        cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n    }, true);\n    const client = createClient(supabaseUrl, supabaseKey, {\n        ...options,\n        global: {\n            ...options?.global,\n            headers: {\n                ...options?.global?.headers,\n                \"X-Client-Info\": `supabase-ssr/${VERSION} createServerClient`,\n            },\n        },\n        auth: {\n            ...(options?.cookieOptions?.name\n                ? { storageKey: options.cookieOptions.name }\n                : null),\n            ...options?.auth,\n            flowType: \"pkce\",\n            autoRefreshToken: false,\n            detectSessionInUrl: false,\n            persistSession: true,\n            storage,\n        },\n    });\n    client.auth.onAuthStateChange(async (event) => {\n        // The SIGNED_IN event is fired very often, but we don't need to\n        // apply the storage each time it fires, only if there are changes\n        // that need to be set -- which is if setItems / removeItems have\n        // data.\n        const hasStorageChanges = Object.keys(setItems).length > 0 || Object.keys(removedItems).length > 0;\n        if (hasStorageChanges &&\n            (event === \"SIGNED_IN\" ||\n                event === \"TOKEN_REFRESHED\" ||\n                event === \"USER_UPDATED\" ||\n                event === \"PASSWORD_RECOVERY\" ||\n                event === \"SIGNED_OUT\" ||\n                event === \"MFA_CHALLENGE_VERIFIED\")) {\n            await applyServerStorage({ getAll, setAll, setItems, removedItems }, {\n                cookieOptions: options?.cookieOptions ?? null,\n                cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n            });\n        }\n    });\n    return client;\n}\n//# sourceMappingURL=createServerClient.js.map", "export * from \"./createBrowserClient\";\nexport * from \"./createServerClient\";\nexport * from \"./types\";\nexport * from \"./utils\";\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parse = parse;\nexports.serialize = serialize;\n/**\n * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n * which has been replaced by the token definition in RFC 7230 appendix B.\n *\n * cookie-name       = token\n * token             = 1*tchar\n * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n *\n * Note: Allowing more characters - https://github.com/jshttp/cookie/issues/191\n * Allow same range as cookie value, except `=`, which delimits end of name.\n */\nconst cookieNameRegExp = /^[\\u0021-\\u003A\\u003C\\u003E-\\u007E]+$/;\n/**\n * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n *\n * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n *                     ; US-ASCII characters excluding CTLs,\n *                     ; whitespace DQUOTE, comma, semicolon,\n *                     ; and backslash\n *\n * Allowing more characters: https://github.com/jshttp/cookie/issues/191\n * Comma, backslash, and DQUOTE are not part of the parsing algorithm.\n */\nconst cookieValueRegExp = /^[\\u0021-\\u003A\\u003C-\\u007E]*$/;\n/**\n * RegExp to match domain-value in RFC 6265 sec 4.1.1\n *\n * domain-value      = <subdomain>\n *                     ; defined in [RFC1034], Section 3.5, as\n *                     ; enhanced by [RFC1123], Section 2.1\n * <subdomain>       = <label> | <subdomain> \".\" <label>\n * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n *                     Labels must be 63 characters or less.\n *                     'let-dig' not 'letter' in the first char, per RFC1123\n * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n * <let-dig-hyp>     = <let-dig> | \"-\"\n * <let-dig>         = <letter> | <digit>\n * <letter>          = any one of the 52 alphabetic characters A through Z in\n *                     upper case and a through z in lower case\n * <digit>           = any one of the ten digits 0 through 9\n *\n * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n *\n * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n * character is not permitted, but a trailing %x2E (\".\"), if present, will\n * cause the user agent to ignore the attribute.)\n */\nconst domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n/**\n * RegExp to match path-value in RFC 6265 sec 4.1.1\n *\n * path-value        = <any CHAR except CTLs or \";\">\n * CHAR              = %x01-7F\n *                     ; defined in RFC 5234 appendix B.1\n */\nconst pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\nconst __toString = Object.prototype.toString;\nconst NullObject = /* @__PURE__ */ (() => {\n    const C = function () { };\n    C.prototype = Object.create(null);\n    return C;\n})();\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n */\nfunction parse(str, options) {\n    const obj = new NullObject();\n    const len = str.length;\n    // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n    if (len < 2)\n        return obj;\n    const dec = options?.decode || decode;\n    let index = 0;\n    do {\n        const eqIdx = str.indexOf(\"=\", index);\n        if (eqIdx === -1)\n            break; // No more cookie pairs.\n        const colonIdx = str.indexOf(\";\", index);\n        const endIdx = colonIdx === -1 ? len : colonIdx;\n        if (eqIdx > endIdx) {\n            // backtrack on prior semicolon\n            index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n            continue;\n        }\n        const keyStartIdx = startIndex(str, index, eqIdx);\n        const keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n        const key = str.slice(keyStartIdx, keyEndIdx);\n        // only assign once\n        if (obj[key] === undefined) {\n            let valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n            let valEndIdx = endIndex(str, endIdx, valStartIdx);\n            const value = dec(str.slice(valStartIdx, valEndIdx));\n            obj[key] = value;\n        }\n        index = endIdx + 1;\n    } while (index < len);\n    return obj;\n}\nfunction startIndex(str, index, max) {\n    do {\n        const code = str.charCodeAt(index);\n        if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */)\n            return index;\n    } while (++index < max);\n    return max;\n}\nfunction endIndex(str, index, min) {\n    while (index > min) {\n        const code = str.charCodeAt(--index);\n        if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */)\n            return index + 1;\n    }\n    return min;\n}\n/**\n * Serialize data into a cookie header.\n *\n * Serialize a name value pair into a cookie string suitable for\n * http headers. An optional options object specifies cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n */\nfunction serialize(name, val, options) {\n    const enc = options?.encode || encodeURIComponent;\n    if (!cookieNameRegExp.test(name)) {\n        throw new TypeError(`argument name is invalid: ${name}`);\n    }\n    const value = enc(val);\n    if (!cookieValueRegExp.test(value)) {\n        throw new TypeError(`argument val is invalid: ${val}`);\n    }\n    let str = name + \"=\" + value;\n    if (!options)\n        return str;\n    if (options.maxAge !== undefined) {\n        if (!Number.isInteger(options.maxAge)) {\n            throw new TypeError(`option maxAge is invalid: ${options.maxAge}`);\n        }\n        str += \"; Max-Age=\" + options.maxAge;\n    }\n    if (options.domain) {\n        if (!domainValueRegExp.test(options.domain)) {\n            throw new TypeError(`option domain is invalid: ${options.domain}`);\n        }\n        str += \"; Domain=\" + options.domain;\n    }\n    if (options.path) {\n        if (!pathValueRegExp.test(options.path)) {\n            throw new TypeError(`option path is invalid: ${options.path}`);\n        }\n        str += \"; Path=\" + options.path;\n    }\n    if (options.expires) {\n        if (!isDate(options.expires) ||\n            !Number.isFinite(options.expires.valueOf())) {\n            throw new TypeError(`option expires is invalid: ${options.expires}`);\n        }\n        str += \"; Expires=\" + options.expires.toUTCString();\n    }\n    if (options.httpOnly) {\n        str += \"; HttpOnly\";\n    }\n    if (options.secure) {\n        str += \"; Secure\";\n    }\n    if (options.partitioned) {\n        str += \"; Partitioned\";\n    }\n    if (options.priority) {\n        const priority = typeof options.priority === \"string\"\n            ? options.priority.toLowerCase()\n            : undefined;\n        switch (priority) {\n            case \"low\":\n                str += \"; Priority=Low\";\n                break;\n            case \"medium\":\n                str += \"; Priority=Medium\";\n                break;\n            case \"high\":\n                str += \"; Priority=High\";\n                break;\n            default:\n                throw new TypeError(`option priority is invalid: ${options.priority}`);\n        }\n    }\n    if (options.sameSite) {\n        const sameSite = typeof options.sameSite === \"string\"\n            ? options.sameSite.toLowerCase()\n            : options.sameSite;\n        switch (sameSite) {\n            case true:\n            case \"strict\":\n                str += \"; SameSite=Strict\";\n                break;\n            case \"lax\":\n                str += \"; SameSite=Lax\";\n                break;\n            case \"none\":\n                str += \"; SameSite=None\";\n                break;\n            default:\n                throw new TypeError(`option sameSite is invalid: ${options.sameSite}`);\n        }\n    }\n    return str;\n}\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n */\nfunction decode(str) {\n    if (str.indexOf(\"%\") === -1)\n        return str;\n    try {\n        return decodeURIComponent(str);\n    }\n    catch (e) {\n        return str;\n    }\n}\n/**\n * Determine if value is a Date.\n */\nfunction isDate(val) {\n    return __toString.call(val) === \"[object Date]\";\n}\n//# sourceMappingURL=index.js.map"], "names": [], "sourceRoot": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]}