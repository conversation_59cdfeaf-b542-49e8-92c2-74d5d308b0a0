(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8279],{29301:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>h});var r=a(95155),s=a(12115),i=a(35695),n=a(52643),o=a(66766),c=a(6874),l=a.n(c);function d(){return(0,r.jsx)(u,{})}function u(){(0,i.useRouter)();let e=(0,i.useSearchParams)(),t=(0,n.createSupabaseBrowserClient)(),[a,c]=(0,s.useState)(!0),[d,u]=(0,s.useState)(null),[h,p]=(0,s.useState)(null),[f,m]=(0,s.useState)(!1),g=e.get("plan")||"starter",x=e.get("user_id"),b=e.get("email"),y="true"===e.get("signup");e.get("upgrade"),(0,s.useEffect)(()=>{fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"COMPONENT_MOUNT",message:"ActualCheckoutContent component is mounting"})}).catch(()=>{}),m(!0)},[]),(0,s.useEffect)(()=>{if(!f)return;localStorage.getItem("pending_signup"),window.debugCheckout=()=>{};let{data:{subscription:e}}=t.auth.onAuthStateChange(async(e,t)=>{"SIGNED_IN"===e&&t&&setTimeout(()=>{w()},100)});return w(),()=>e.unsubscribe()},[f]);let w=async()=>{try{await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"CHECKOUT_INITIALIZATION",urlParams:{selectedPlan:g,userId:x,email:b,isSignup:y},currentUrl:window.location.href})}).catch(()=>{});let e=null,a=null,r=0;for(;!e&&r<3;){let s=await t.auth.getUser();e=s.data.user,a=s.error,!e&&r<2&&await new Promise(e=>setTimeout(e,1e3)),r++}if(await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"USER_CHECK",hasUser:!!e,authError:null==a?void 0:a.message,userId:null==e?void 0:e.id,userEmail:null==e?void 0:e.email,retryCount:r,maxRetries:3})}).catch(()=>{}),a||!e){if(await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"AUTH_FAILED",error:(null==a?void 0:a.message)||"No user found",userId:x,email:b,hasUserId:!!x,hasEmail:!!b,retryCount:r,redirecting:!0})}).catch(()=>{}),x&&b){await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"PENDING_USER_CHECKOUT",userId:x,email:b,plan:g,message:"Proceeding with checkout for pending user"})}).catch(()=>{});try{await N(x,b)}catch(e){u("Failed to create checkout session. Please try again.")}}else u('Authentication required. Please sign up first. Click "Try Again" to go to sign up page.');return}p(e);let s=e.user_metadata,i=null==s?void 0:s.payment_status;await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"USER_FOUND",userId:e.id,email:e.email,paymentStatus:i,userMetadata:s})}).catch(()=>{}),await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"CALLING_CREATE_CHECKOUT_SESSION",userId:e.id,selectedPlan:g,aboutToCall:!0})}).catch(()=>{});try{await j(e.id,e)}catch(e){throw await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"CREATE_CHECKOUT_SESSION_ERROR",error:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:void 0})}).catch(()=>{}),e}}catch(e){await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"ERROR",error:e instanceof Error?e.message:String(e)})}).catch(()=>{}),u("Failed to initialize checkout. Please try again.")}finally{c(!1)}},N=async(e,t)=>{try{let a=await v(g),r=await fetch("/api/stripe/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({priceId:a,tier:g,userId:e,userEmail:t,signup:!1})}),s=await r.json();if(!r.ok)throw Error(s.error||"Failed to create checkout session");if(s.url)window.location.href=s.url;else throw Error("No checkout URL received")}catch(e){throw e}},j=async(e,t)=>{try{var a;let r=t||h,s=(null==r?void 0:r.email)||(null==r||null==(a=r.user_metadata)?void 0:a.email)||b;if(!s)throw Error("User email not found");let i=await v(g),n=await fetch("/api/stripe/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({priceId:i,tier:g,userId:e,userEmail:s,signup:!1})}),o=await n.json();if(!n.ok)throw Error(o.error||"Failed to create checkout session");if(o.url)window.location.href=o.url;else throw Error("No checkout URL received")}catch(e){u(e instanceof Error?e.message:"Failed to start checkout")}},v=async e=>{try{let t=await fetch("/api/stripe/price-ids");if(!t.ok)throw Error("Failed to fetch price IDs");let a=await t.json();switch(e.toLowerCase()){case"free":return a.free;case"starter":return a.starter;case"professional":default:return a.professional;case"enterprise":return a.enterprise}}catch(t){switch(e.toLowerCase()){case"starter":return"price_1RcekMC97XFBBUvdYnl0leWM";case"professional":default:return"price_1RcelCC97XFBBUvdfvuJnGnC";case"enterprise":return"price_1RceljC97XFBBUvdyCtcBYyT"}}};return f?d?(0,r.jsxs)("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center px-4",style:{background:"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)"},children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-[0.03]",style:{backgroundImage:"\n              linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\n              linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\n            ",backgroundSize:"60px 60px"}}),(0,r.jsxs)("div",{className:"max-w-md w-full text-center relative z-10",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6 border border-red-500/30",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:"Checkout Error"}),(0,r.jsx)("p",{className:"text-gray-300 mb-6",children:d}),(0,r.jsx)(l(),{href:"/auth/signup?plan=".concat(g),className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-semibold rounded-lg hover:from-[#e55a2b] hover:to-[#e6821a] transition-all duration-300 shadow-lg hover:shadow-xl",children:"Try Again"})]})]}):(0,r.jsxs)("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center px-4",style:{background:"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)"},children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-[0.03]",style:{backgroundImage:"\n            linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\n          ",backgroundSize:"60px 60px"}}),(0,r.jsxs)("div",{className:"max-w-md w-full text-center relative z-10",children:[(0,r.jsx)("div",{className:"flex justify-center mb-8",children:(0,r.jsxs)("div",{className:"relative inline-block",children:[(0,r.jsx)(o.default,{src:"/RouKey_Logo_GLOW.png",alt:"RouKey",width:64,height:64,className:"w-16 h-16 transition-transform duration-300 hover:scale-110",priority:!0}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-full opacity-30 blur-xl animate-pulse"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-full opacity-20 blur-2xl animate-pulse",style:{animationDelay:"1s"}})]})}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"RouKey"})}),(0,r.jsxs)("div",{className:"bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 p-8 shadow-2xl",children:[(0,r.jsx)("div",{className:"w-12 h-12 border-4 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-6"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-white mb-4",children:"Setting up your subscription..."}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-[#ff6b35]/20 to-[#f7931e]/20 border border-[#ff6b35]/30 rounded-xl p-4 mb-6 backdrop-blur-sm",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-[#ff6b35] rounded-full"}),(0,r.jsxs)("span",{className:"text-[#ff6b35] font-semibold",children:[g.charAt(0).toUpperCase()+g.slice(1)," Plan"]}),(0,r.jsx)("div",{className:"w-2 h-2 bg-[#ff6b35] rounded-full"})]}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-white",children:[(e=>{switch(e.toLowerCase()){case"free":return"$0";case"starter":return"$19";case"professional":default:return"$49";case"enterprise":return"$149"}})(g),"/month"]})]}),(0,r.jsx)("p",{className:"text-gray-300 mb-4",children:"You'll be redirected to Stripe to complete your payment securely."}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"After payment, you'll verify your email and gain access to your dashboard."})]}),(0,r.jsxs)("div",{className:"mt-6 flex items-center justify-center space-x-2 text-sm text-gray-400",children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})}),(0,r.jsx)("span",{children:"Secured by Stripe"})]})]})]}):(0,r.jsxs)("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center",style:{background:"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)"},children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-[0.03]",style:{backgroundImage:"\n              linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\n              linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\n            ",backgroundSize:"60px 60px"}}),(0,r.jsxs)("div",{className:"text-center relative z-10",children:[(0,r.jsx)("div",{className:"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-300",children:"Loading checkout..."})]})]})}function h(){return(0,r.jsx)(d,{})}},31915:(e,t,a)=>{Promise.resolve().then(a.bind(a,29301))}},e=>{var t=t=>e(e.s=t);e.O(0,[8888,1459,5738,4755,563,2662,8669,4703,3269,9173,9219,4288,7706,7544,1142,2993,1561,9248,6761,7358],()=>t(31915)),_N_E=e.O()}]);