{"/api/admin/cleanup-semantic-cache/route": "/api/admin/cleanup-semantic-cache", "/api/admin/populate-cost-tiers/route": "/api/admin/populate-cost-tiers", "/api/analytics/cache/route": "/api/analytics/cache", "/api/analytics/errors/route": "/api/analytics/errors", "/api/analytics/summary/route": "/api/analytics/summary", "/api/auth/check-pending-payment/route": "/api/auth/check-pending-payment", "/api/analytics/users/route": "/api/analytics/users", "/api/activity/route": "/api/activity", "/api/analytics/metadata/route": "/api/analytics/metadata", "/api/auth/generate-magic-link/route": "/api/auth/generate-magic-link", "/api/cache/invalidate/route": "/api/cache/invalidate", "/api/chat/conversations/route": "/api/chat/conversations", "/api/chat/messages/route": "/api/chat/messages", "/api/auth/free-signup/route": "/api/auth/free-signup", "/api/custom-configs/[configId]/browsing/route": "/api/custom-configs/[configId]/browsing", "/api/chat/messages/delete-after-timestamp/route": "/api/chat/messages/delete-after-timestamp", "/api/chat/messages/update-by-timestamp/route": "/api/chat/messages/update-by-timestamp", "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]/route": "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]", "/api/cleanup/pending-users/route": "/api/cleanup/pending-users", "/api/custom-configs/[configId]/default-chat-key/route": "/api/custom-configs/[configId]/default-chat-key", "/api/custom-configs/route": "/api/custom-configs", "/api/custom-configs/[configId]/routing/route": "/api/custom-configs/[configId]/routing", "/api/debug/checkout/route": "/api/debug/checkout", "/api/custom-configs/[configId]/route": "/api/custom-configs/[configId]", "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments/route": "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments", "/api/debug/auth-test/route": "/api/debug/auth-test", "/api/debug/clear-cache/route": "/api/debug/clear-cache", "/api/debug/subscription-data/route": "/api/debug/subscription-data", "/api/debug/env-check/route": "/api/debug/env-check", "/api/debug/workflow-tools/route": "/api/debug/workflow-tools", "/api/debug/supabase-test/route": "/api/debug/supabase-test", "/api/debug/webhook-logs/route": "/api/debug/webhook-logs", "/api/debug/test-webhook/route": "/api/debug/test-webhook", "/api/documents/upload/route": "/api/documents/upload", "/api/documents/[documentId]/route": "/api/documents/[documentId]", "/api/auth/paid-signup/route": "/api/auth/paid-signup", "/api/email/test-welcome/route": "/api/email/test-welcome", "/api/external/v1/async/status/[jobId]/route": "/api/external/v1/async/status/[jobId]", "/api/external/v1/async/result/[jobId]/route": "/api/external/v1/async/result/[jobId]", "/api/external/v1/async/submit/route": "/api/external/v1/async/submit", "/api/documents/search/route": "/api/documents/search", "/api/keys/[apiKeyId]/roles/route": "/api/keys/[apiKeyId]/roles", "/api/documents/list/route": "/api/documents/list", "/api/internal/classify-multi-role/route": "/api/internal/classify-multi-role", "/api/internal/async/process/route": "/api/internal/async/process", "/api/keys/[apiKeyId]/route": "/api/keys/[apiKeyId]", "/api/keys/[apiKeyId]/roles/[roleName]/route": "/api/keys/[apiKeyId]/roles/[roleName]", "/api/keys/route": "/api/keys", "/api/logs/route": "/api/logs", "/api/orchestration/start/route": "/api/orchestration/start", "/api/orchestration/status/[executionId]/route": "/api/orchestration/status/[executionId]", "/api/memory/stats/route": "/api/memory/stats", "/api/orchestration/process-step/route": "/api/orchestration/process-step", "/api/orchestration/synthesis-fallback/[executionId]/route": "/api/orchestration/synthesis-fallback/[executionId]", "/api/orchestration/synthesis-stream-direct/[executionId]/route": "/api/orchestration/synthesis-stream-direct/[executionId]", "/api/orchestration/stream/[executionId]/route": "/api/orchestration/stream/[executionId]", "/api/providers/list-models/route": "/api/providers/list-models", "/api/orchestration/synthesis-stream/[executionId]/route": "/api/orchestration/synthesis-stream/[executionId]", "/api/pricing/tiers/route": "/api/pricing/tiers", "/api/quality-feedback/route": "/api/quality-feedback", "/api/playground/route": "/api/playground", "/api/quality-analytics/route": "/api/quality-analytics", "/api/stripe/price-ids/route": "/api/stripe/price-ids", "/api/stripe/create-checkout-session/route": "/api/stripe/create-checkout-session", "/api/stripe/subscription-status/route": "/api/stripe/subscription-status", "/api/stripe/webhooks/route": "/api/stripe/webhooks", "/api/system-status/route": "/api/system-status", "/api/stripe/payment-success/route": "/api/stripe/payment-success", "/api/test-subscription/route": "/api/test-subscription", "/api/orchestration/classify-roles/route": "/api/orchestration/classify-roles", "/api/stripe/customer-portal/route": "/api/stripe/customer-portal", "/api/training/jobs/upsert/route": "/api/training/jobs/upsert", "/api/training/jobs/route": "/api/training/jobs", "/api/user-api-keys/[keyId]/route": "/api/user-api-keys/[keyId]", "/api/user-api-keys/route": "/api/user-api-keys", "/api/user/custom-roles/route": "/api/user/custom-roles", "/api/user/custom-roles/[customRoleId]/route": "/api/user/custom-roles/[customRoleId]", "/api/user/delete-account/route": "/api/user/delete-account", "/auth/callback/route": "/auth/callback", "/apple-icon.png/route": "/apple-icon.png", "/api/v1/chat/completions/route": "/api/v1/chat/completions", "/api/user/subscription-tier/route": "/api/user/subscription-tier", "/api/test/semantic-cache/route": "/api/test/semantic-cache", "/icon.png/route": "/icon.png", "/add-keys/page": "/add-keys", "/analytics/page": "/analytics", "/_not-found/page": "/_not-found", "/auth/signup/page": "/auth/signup", "/auth/recover/page": "/auth/recover", "/auth/verify-email/page": "/auth/verify-email", "/auth/reset-password/page": "/auth/reset-password", "/billing/page": "/billing", "/checkout/page": "/checkout", "/auth/signin/page": "/auth/signin", "/debug-session/page": "/debug-session", "/dashboard/page": "/dashboard", "/logs/page": "/logs", "/page": "/", "/my-models/page": "/my-models", "/my-models/[configId]/page": "/my-models/[configId]", "/playground/page": "/playground", "/routing-setup/page": "/routing-setup", "/pricing/page": "/pricing", "/routing-setup/[configId]/page": "/routing-setup/[configId]", "/success/page": "/success", "/test-full-browsing/page": "/test-full-browsing", "/training/page": "/training", "/playground/workflows/page": "/playground/workflows", "/(app)/settings/page": "/settings", "/about-developer/page": "/about-developer", "/about/page": "/about", "/blog/ai-api-gateway-2025-guide/page": "/blog/ai-api-gateway-2025-guide", "/blog/bootstrap-lean-startup-2025/page": "/blog/bootstrap-lean-startup-2025", "/blog/build-ai-powered-saas/page": "/blog/build-ai-powered-saas", "/blog/roukey-ai-routing-strategies/page": "/blog/roukey-ai-routing-strategies", "/blog/page": "/blog", "/blog/cost-effective-ai-development/page": "/blog/cost-effective-ai-development", "/blog/openai-vs-claude-vs-gemini-2025/page": "/blog/openai-vs-claude-vs-gemini-2025", "/blog/ai-model-selection-guide/page": "/blog/ai-model-selection-guide", "/blog/building-ai-apps-byok-advantage-2025/page": "/blog/building-ai-apps-byok-advantage-2025", "/contact/page": "/contact", "/features/page": "/features", "/cookies/page": "/cookies", "/privacy/page": "/privacy", "/blog/roukey-vs-openrouter-vs-portkey-2025/page": "/blog/roukey-vs-openrouter-vs-portkey-2025", "/routing-strategies/page": "/routing-strategies", "/security/page": "/security", "/terms/page": "/terms", "/docs/page": "/docs", "/api/external/v1/api-keys/[keyId]/route": "/api/external/v1/api-keys/[keyId]", "/api/external/v1/configs/[configId]/keys/[keyId]/route": "/api/external/v1/configs/[configId]/keys/[keyId]", "/api/external/v1/chat/completions/route": "/api/external/v1/chat/completions", "/api/external/v1/configs/[configId]/keys/route": "/api/external/v1/configs/[configId]/keys", "/api/external/v1/configs/[configId]/route": "/api/external/v1/configs/[configId]", "/api/external/v1/models/route": "/api/external/v1/models", "/api/external/v1/api-keys/route": "/api/external/v1/api-keys", "/api/external/v1/configs/route": "/api/external/v1/configs", "/api/external/v1/configs/[configId]/routing/route": "/api/external/v1/configs/[configId]/routing", "/api/external/v1/usage/route": "/api/external/v1/usage", "/api/external/v1/docs/route": "/api/external/v1/docs", "/api/external/v1/providers/route": "/api/external/v1/providers"}