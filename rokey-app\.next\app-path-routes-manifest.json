{"/api/activity/route": "/api/activity", "/api/admin/populate-cost-tiers/route": "/api/admin/populate-cost-tiers", "/api/analytics/cache/route": "/api/analytics/cache", "/api/analytics/errors/route": "/api/analytics/errors", "/api/analytics/users/route": "/api/analytics/users", "/api/analytics/metadata/route": "/api/analytics/metadata", "/api/auth/check-pending-payment/route": "/api/auth/check-pending-payment", "/api/auth/free-signup/route": "/api/auth/free-signup", "/api/analytics/summary/route": "/api/analytics/summary", "/api/auth/generate-magic-link/route": "/api/auth/generate-magic-link", "/api/auth/paid-signup/route": "/api/auth/paid-signup", "/api/cache/invalidate/route": "/api/cache/invalidate", "/api/chat/conversations/route": "/api/chat/conversations", "/api/chat/messages/delete-after-timestamp/route": "/api/chat/messages/delete-after-timestamp", "/api/chat/messages/route": "/api/chat/messages", "/api/custom-configs/[configId]/browsing/route": "/api/custom-configs/[configId]/browsing", "/api/chat/messages/update-by-timestamp/route": "/api/chat/messages/update-by-timestamp", "/api/custom-configs/[configId]/default-chat-key/route": "/api/custom-configs/[configId]/default-chat-key", "/api/cleanup/pending-users/route": "/api/cleanup/pending-users", "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments/route": "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments", "/api/custom-configs/[configId]/route": "/api/custom-configs/[configId]", "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]/route": "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]", "/api/custom-configs/[configId]/routing/route": "/api/custom-configs/[configId]/routing", "/api/debug/checkout/route": "/api/debug/checkout", "/api/debug/auth-test/route": "/api/debug/auth-test", "/api/debug/clear-cache/route": "/api/debug/clear-cache", "/api/debug/env-check/route": "/api/debug/env-check", "/api/custom-configs/route": "/api/custom-configs", "/api/debug/supabase-test/route": "/api/debug/supabase-test", "/api/debug/webhook-logs/route": "/api/debug/webhook-logs", "/api/debug/test-webhook/route": "/api/debug/test-webhook", "/api/debug/workflow-tools/route": "/api/debug/workflow-tools", "/api/documents/[documentId]/route": "/api/documents/[documentId]", "/api/documents/list/route": "/api/documents/list", "/api/documents/search/route": "/api/documents/search", "/api/email/test-welcome/route": "/api/email/test-welcome", "/api/external/v1/async/result/[jobId]/route": "/api/external/v1/async/result/[jobId]", "/api/external/v1/async/submit/route": "/api/external/v1/async/submit", "/api/external/v1/async/status/[jobId]/route": "/api/external/v1/async/status/[jobId]", "/api/internal/async/process/route": "/api/internal/async/process", "/api/keys/[apiKeyId]/roles/[roleName]/route": "/api/keys/[apiKeyId]/roles/[roleName]", "/api/internal/classify-multi-role/route": "/api/internal/classify-multi-role", "/api/keys/[apiKeyId]/roles/route": "/api/keys/[apiKeyId]/roles", "/api/keys/[apiKeyId]/route": "/api/keys/[apiKeyId]", "/api/admin/cleanup-semantic-cache/route": "/api/admin/cleanup-semantic-cache", "/api/logs/route": "/api/logs", "/api/documents/upload/route": "/api/documents/upload", "/api/memory/stats/route": "/api/memory/stats", "/api/orchestration/classify-roles/route": "/api/orchestration/classify-roles", "/api/keys/route": "/api/keys", "/api/orchestration/start/route": "/api/orchestration/start", "/api/orchestration/status/[executionId]/route": "/api/orchestration/status/[executionId]", "/api/orchestration/stream/[executionId]/route": "/api/orchestration/stream/[executionId]", "/api/orchestration/synthesis-fallback/[executionId]/route": "/api/orchestration/synthesis-fallback/[executionId]", "/api/debug/subscription-data/route": "/api/debug/subscription-data", "/api/orchestration/synthesis-stream/[executionId]/route": "/api/orchestration/synthesis-stream/[executionId]", "/api/orchestration/synthesis-stream-direct/[executionId]/route": "/api/orchestration/synthesis-stream-direct/[executionId]", "/api/pricing/tiers/route": "/api/pricing/tiers", "/api/providers/list-models/route": "/api/providers/list-models", "/api/quality-feedback/route": "/api/quality-feedback", "/api/stripe/customer-portal/route": "/api/stripe/customer-portal", "/api/stripe/create-checkout-session/route": "/api/stripe/create-checkout-session", "/api/stripe/price-ids/route": "/api/stripe/price-ids", "/api/stripe/payment-success/route": "/api/stripe/payment-success", "/api/stripe/webhooks/route": "/api/stripe/webhooks", "/api/system-status/route": "/api/system-status", "/api/stripe/subscription-status/route": "/api/stripe/subscription-status", "/api/test-subscription/route": "/api/test-subscription", "/api/playground/route": "/api/playground", "/api/test/semantic-cache/route": "/api/test/semantic-cache", "/api/training/jobs/route": "/api/training/jobs", "/api/user-api-keys/[keyId]/route": "/api/user-api-keys/[keyId]", "/api/training/jobs/upsert/route": "/api/training/jobs/upsert", "/api/user-api-keys/route": "/api/user-api-keys", "/api/user/delete-account/route": "/api/user/delete-account", "/api/user/custom-roles/route": "/api/user/custom-roles", "/api/user/custom-roles/[customRoleId]/route": "/api/user/custom-roles/[customRoleId]", "/apple-icon.png/route": "/apple-icon.png", "/api/v1/chat/completions/route": "/api/v1/chat/completions", "/auth/callback/route": "/auth/callback", "/api/user/subscription-tier/route": "/api/user/subscription-tier", "/icon.png/route": "/icon.png", "/api/orchestration/process-step/route": "/api/orchestration/process-step", "/api/quality-analytics/route": "/api/quality-analytics", "/_not-found/page": "/_not-found", "/add-keys/page": "/add-keys", "/analytics/page": "/analytics", "/auth/recover/page": "/auth/recover", "/auth/reset-password/page": "/auth/reset-password", "/auth/verify-email/page": "/auth/verify-email", "/auth/signup/page": "/auth/signup", "/billing/page": "/billing", "/auth/signin/page": "/auth/signin", "/checkout/page": "/checkout", "/dashboard/page": "/dashboard", "/debug-session/page": "/debug-session", "/logs/page": "/logs", "/my-models/[configId]/page": "/my-models/[configId]", "/page": "/", "/my-models/page": "/my-models", "/playground/workflows/page": "/playground/workflows", "/pricing/page": "/pricing", "/playground/page": "/playground", "/routing-setup/[configId]/page": "/routing-setup/[configId]", "/routing-setup/page": "/routing-setup", "/success/page": "/success", "/test-full-browsing/page": "/test-full-browsing", "/training/page": "/training", "/(app)/settings/page": "/settings", "/about/page": "/about", "/blog/ai-api-gateway-2025-guide/page": "/blog/ai-api-gateway-2025-guide", "/blog/bootstrap-lean-startup-2025/page": "/blog/bootstrap-lean-startup-2025", "/blog/building-ai-apps-byok-advantage-2025/page": "/blog/building-ai-apps-byok-advantage-2025", "/blog/build-ai-powered-saas/page": "/blog/build-ai-powered-saas", "/blog/cost-effective-ai-development/page": "/blog/cost-effective-ai-development", "/blog/openai-vs-claude-vs-gemini-2025/page": "/blog/openai-vs-claude-vs-gemini-2025", "/blog/page": "/blog", "/blog/ai-model-selection-guide/page": "/blog/ai-model-selection-guide", "/blog/roukey-vs-openrouter-vs-portkey-2025/page": "/blog/roukey-vs-openrouter-vs-portkey-2025", "/blog/roukey-ai-routing-strategies/page": "/blog/roukey-ai-routing-strategies", "/about-developer/page": "/about-developer", "/features/page": "/features", "/cookies/page": "/cookies", "/docs/page": "/docs", "/contact/page": "/contact", "/routing-strategies/page": "/routing-strategies", "/privacy/page": "/privacy", "/terms/page": "/terms", "/security/page": "/security", "/api/external/v1/configs/[configId]/keys/[keyId]/route": "/api/external/v1/configs/[configId]/keys/[keyId]", "/api/external/v1/api-keys/route": "/api/external/v1/api-keys", "/api/external/v1/chat/completions/route": "/api/external/v1/chat/completions", "/api/external/v1/api-keys/[keyId]/route": "/api/external/v1/api-keys/[keyId]", "/api/external/v1/configs/[configId]/route": "/api/external/v1/configs/[configId]", "/api/external/v1/models/route": "/api/external/v1/models", "/api/external/v1/docs/route": "/api/external/v1/docs", "/api/external/v1/providers/route": "/api/external/v1/providers", "/api/external/v1/usage/route": "/api/external/v1/usage", "/api/external/v1/configs/[configId]/routing/route": "/api/external/v1/configs/[configId]/routing", "/api/external/v1/configs/[configId]/keys/route": "/api/external/v1/configs/[configId]/keys", "/api/external/v1/configs/route": "/api/external/v1/configs"}