(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[109],{11337:(e,s,r)=>{Promise.resolve().then(r.bind(r,18845))},18845:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>w});var a=r(95155),t=r(12115),l=r(35695),i=r(58828),n=r(18593),c=r(10184),d=r(48987),o=r(94038),m=r(8246),u=r(31151),x=r(14170),h=r(13741),g=r(85057),f=r(64198),p=r(52643),b=r(83298);function w(){var e,s,r;(0,l.useRouter)();let{user:w}=(0,b.R)(),y=(0,p.createSupabaseBrowserClient)(),{success:j,error:N}=(0,f.dj)(),[v,k]=(0,t.useState)("account"),[P,A]=(0,t.useState)(!1),[C,E]=(0,t.useState)(!1),[S,_]=(0,t.useState)(!1),[F,D]=(0,t.useState)(!1),[T,$]=(0,t.useState)(!1),[I,K]=(0,t.useState)(!1),[L,U]=(0,t.useState)(!1),[q,J]=(0,t.useState)(!1),[O,R]=(0,t.useState)(!1),[z,Y]=(0,t.useState)({newEmail:"",currentPassword:""}),[W,M]=(0,t.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[B,G]=(0,t.useState)({emailNotifications:!0,securityAlerts:!0,usageAlerts:!0,marketingEmails:!1}),[Z,H]=(0,t.useState)({configCount:0,apiKeyCount:0,userApiKeyCount:0}),Q=[{id:"account",label:"Account settings",icon:x.A},{id:"notifications",label:"Notifications",icon:i.A},{id:"security",label:"Security",icon:m.A},{id:"danger",label:"Danger zone",icon:u.A}];(0,t.useEffect)(()=>{w&&(V(),X())},[w]);let V=async()=>{try{let e=await fetch("/api/custom-configs"),s=e.ok?await e.json():[],r=Array.isArray(s)?s:[],a=await fetch("/api/user-api-keys"),t=(a.ok?await a.json():{api_keys:[]}).api_keys||[],l=0;for(let e of r)try{let s=await fetch("/api/custom-configs/".concat(e.id,"/keys")),r=s.ok?await s.json():{api_keys:[]};l+=(r.api_keys||[]).length}catch(e){}H({configCount:r.length,apiKeyCount:l,userApiKeyCount:t.length})}catch(e){}},X=async()=>{try{let e=localStorage.getItem("notification_settings");e&&G(JSON.parse(e))}catch(e){}},ee=async e=>{if(e.preventDefault(),!W.currentPassword.trim())return void N("Current password is required");if(!W.newPassword.trim())return void N("New password is required");if(W.newPassword!==W.confirmPassword)return void N("New passwords do not match");if(W.newPassword.length<8)return void N("Password must be at least 8 characters long");if(W.newPassword===W.currentPassword)return void N("New password must be different from current password");A(!0);try{let{error:e}=await y.auth.signInWithPassword({email:(null==w?void 0:w.email)||"",password:W.currentPassword});if(e){N("Current password is incorrect"),A(!1);return}let{error:s}=await y.auth.updateUser({password:W.newPassword});if(s)throw s;j("Password updated successfully","Your password has been changed successfully."),M({currentPassword:"",newPassword:"",confirmPassword:""})}catch(e){N("Failed to update password",e.message||"Please try again")}finally{A(!1)}},es=async()=>{A(!0);try{localStorage.setItem("notification_settings",JSON.stringify(B));let{error:e}=await y.auth.updateUser({data:{notification_preferences:B}});if(e)throw e;j("Notification preferences saved successfully")}catch(e){N("Failed to save notification preferences")}finally{A(!1)}},er=async()=>{if(!(null==w?void 0:w.email))return void N("No email address found");U(!0);try{let{error:e}=await y.auth.resetPasswordForEmail(w.email,{redirectTo:"".concat(window.location.origin,"/auth/reset-password")});if(e)throw e;j("Password reset email sent!","Check your inbox for instructions to reset your password."),K(!1)}catch(e){N("Failed to send reset email",e.message||"Please try again.")}finally{U(!1)}},ea=async()=>{if(!z.newEmail.trim())return void N("Please enter a new email address");if(!z.currentPassword.trim())return void N("Current password is required to change email");if(z.newEmail===(null==w?void 0:w.email))return void N("New email must be different from current email");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(z.newEmail))return void N("Please enter a valid email address");R(!0);try{let{error:e}=await y.auth.signInWithPassword({email:(null==w?void 0:w.email)||"",password:z.currentPassword});if(e){N("Current password is incorrect"),R(!1);return}let{error:s}=await y.auth.updateUser({email:z.newEmail});if(s)throw s;j("Email change initiated!","Check both your old and new email addresses for confirmation instructions."),J(!1),Y({newEmail:"",currentPassword:""})}catch(e){N("Failed to change email",e.message||"Please try again.")}finally{R(!1)}},et=async()=>{if(w){A(!0);try{let e=await fetch("/api/user/delete-account",{method:"DELETE",headers:{"Content-Type":"application/json"}});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to delete account")}j("Account deleted successfully. You will be redirected to the homepage."),await y.auth.signOut(),setTimeout(()=>{window.location.href="/"},2e3)}catch(e){N("Failed to delete account",e.message||"Please contact support.")}finally{A(!1)}}};return(0,a.jsxs)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:[(0,a.jsx)("div",{className:"border-b border-gray-800/50",children:(0,a.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-6",children:(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold text-white",children:"Account Settings"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Manage your account settings and preferences"})]})})})}),(0,a.jsx)("div",{className:"border-b border-gray-800/50",children:(0,a.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:Q.map(e=>{let s=e.icon;return(0,a.jsxs)("button",{onClick:()=>k(e.id),className:"flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ".concat(v===e.id?"border-orange-500 text-orange-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600"),children:[(0,a.jsx)(s,{className:"h-4 w-4"}),e.label]},e.id)})})})}),(0,a.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8 space-y-8",children:["account"===v&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)("div",{className:"p-2 bg-orange-500/10 rounded-lg",children:(0,a.jsx)(n.A,{className:"h-5 w-5 text-orange-400"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Email address"})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("p",{className:"text-gray-300",children:["Your email address is ",(0,a.jsx)("span",{className:"font-medium text-white",children:null==w?void 0:w.email})]}),(0,a.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"This is used for login and important notifications"})]}),(0,a.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-orange-400 hover:text-orange-300 hover:bg-orange-500/10 rounded-md transition-colors border border-orange-500/20 hover:border-orange-500/40 shrink-0",onClick:()=>J(!0),children:"Change"})]})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-500/10 rounded-lg",children:(0,a.jsx)(x.A,{className:"h-5 w-5 text-blue-400"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Account Information"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white mb-1",children:"Full Name"}),(0,a.jsx)("p",{className:"text-gray-300",children:(null==w||null==(e=w.user_metadata)?void 0:e.full_name)||(null==w||null==(s=w.user_metadata)?void 0:s.first_name)+" "+(null==w||null==(r=w.user_metadata)?void 0:r.last_name)||"Not set"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white mb-1",children:"Account Created"}),(0,a.jsx)("p",{className:"text-gray-300",children:(null==w?void 0:w.created_at)?new Date(w.created_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"Unknown"})]})]}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white mb-1",children:"Account Usage"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-gray-300",children:[(0,a.jsxs)("p",{children:[Z.configCount," configurations"]}),(0,a.jsxs)("p",{children:[Z.apiKeyCount," API keys"]}),(0,a.jsxs)("p",{children:[Z.userApiKeyCount," user-generated keys"]})]})]})})]})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,a.jsx)("div",{className:"p-2 bg-green-500/10 rounded-lg",children:(0,a.jsx)(o.A,{className:"h-5 w-5 text-green-400"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Password"})]}),(0,a.jsxs)("form",{onSubmit:ee,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(g.J,{htmlFor:"currentPassword",className:"text-sm font-medium text-white",children:"Current password"}),(0,a.jsxs)("div",{className:"relative mt-2",children:[(0,a.jsx)("input",{id:"currentPassword",type:C?"text":"password",value:W.currentPassword,onChange:e=>M(s=>({...s,currentPassword:e.target.value})),placeholder:"••••••••••",className:"w-full pr-10 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>E(!C),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300",children:C?(0,a.jsx)(d.A,{className:"h-4 w-4"}):(0,a.jsx)(c.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(g.J,{htmlFor:"newPassword",className:"text-sm font-medium text-white",children:"New password"}),(0,a.jsxs)("div",{className:"relative mt-2",children:[(0,a.jsx)("input",{id:"newPassword",type:S?"text":"password",value:W.newPassword,onChange:e=>M(s=>({...s,newPassword:e.target.value})),placeholder:"••••••••••",className:"w-full pr-10 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>_(!S),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300",children:S?(0,a.jsx)(d.A,{className:"h-4 w-4"}):(0,a.jsx)(c.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(g.J,{htmlFor:"confirmPassword",className:"text-sm font-medium text-white",children:"Confirm new password"}),(0,a.jsxs)("div",{className:"relative mt-2",children:[(0,a.jsx)("input",{id:"confirmPassword",type:F?"text":"password",value:W.confirmPassword,onChange:e=>M(s=>({...s,confirmPassword:e.target.value})),placeholder:"••••••••••",className:"w-full pr-10 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>D(!F),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300",children:F?(0,a.jsx)(d.A,{className:"h-4 w-4"}):(0,a.jsx)(c.A,{className:"h-4 w-4"})})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-300",children:["Can't remember your current password? ",(0,a.jsx)("button",{type:"button",className:"text-orange-400 hover:text-orange-300 font-medium",onClick:()=>K(!0),children:"Reset your password"})]}),(0,a.jsx)(h.$,{type:"submit",disabled:P,className:"bg-orange-600 hover:bg-orange-700 text-white",children:P?"Saving...":"Save password"})]})]})]})]}),"notifications"===v&&(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-500/10 rounded-lg",children:(0,a.jsx)(i.A,{className:"h-5 w-5 text-blue-400"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Notification Preferences"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"Email Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Receive important updates via email"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:B.emailNotifications,onChange:e=>G(s=>({...s,emailNotifications:e.target.checked})),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"Security Alerts"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Get notified about security events"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:B.securityAlerts,onChange:e=>G(s=>({...s,securityAlerts:e.target.checked})),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"Usage Alerts"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Notifications about API usage and limits"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:B.usageAlerts,onChange:e=>G(s=>({...s,usageAlerts:e.target.checked})),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"Marketing Emails"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Product updates and feature announcements"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:B.marketingEmails,onChange:e=>G(s=>({...s,marketingEmails:e.target.checked})),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"})]})]})]}),(0,a.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-700",children:(0,a.jsx)(h.$,{onClick:es,disabled:P,className:"bg-orange-600 hover:bg-orange-700 text-white",children:P?"Saving...":"Save Preferences"})})]})}),"security"===v&&(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,a.jsx)("div",{className:"p-2 bg-green-500/10 rounded-lg",children:(0,a.jsx)(m.A,{className:"h-5 w-5 text-green-400"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Security Settings"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-green-500/10 border border-green-500/20 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 text-green-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-green-400",children:"Account Security Status"}),(0,a.jsx)("p",{className:"text-sm text-green-300",children:"Your account is secure and protected"})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white mb-2",children:"Last Login"}),(0,a.jsx)("p",{className:"text-sm text-gray-300",children:(null==w?void 0:w.last_sign_in_at)?new Date(w.last_sign_in_at).toLocaleString():"Unknown"})]}),(0,a.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white mb-2",children:"Account Type"}),(0,a.jsx)("p",{className:"text-sm text-gray-300",children:"Email & Password"})]})]})]})]})}),"danger"===v&&(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"bg-red-900/20 backdrop-blur-sm rounded-lg border border-red-800/50 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,a.jsx)("div",{className:"p-2 bg-red-500/10 rounded-lg",children:(0,a.jsx)(u.A,{className:"h-5 w-5 text-red-400"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Danger Zone"})]}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-red-400 mb-2",children:"⚠️ Account Deletion"}),(0,a.jsx)("p",{className:"text-sm text-red-300 mb-4",children:"Once you delete your account, there is no going back. This will permanently delete your account, configurations, API keys, and all associated data."}),(0,a.jsx)(h.$,{onClick:()=>$(!0),className:"bg-red-600 hover:bg-red-700 text-white",children:"Delete Account"})]})})]})})]}),q&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-gray-900 border border-gray-800 rounded-xl shadow-2xl max-w-md w-full p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(n.A,{className:"h-6 w-6 text-orange-400"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Change Email Address"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(g.J,{htmlFor:"newEmail",className:"text-sm font-medium text-white",children:"New Email Address"}),(0,a.jsx)("input",{id:"newEmail",type:"email",value:z.newEmail,onChange:e=>Y(s=>({...s,newEmail:e.target.value})),placeholder:"Enter new email address",className:"w-full mt-2 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(g.J,{htmlFor:"currentPasswordEmail",className:"text-sm font-medium text-white",children:"Current Password"}),(0,a.jsx)("input",{id:"currentPasswordEmail",type:"password",value:z.currentPassword,onChange:e=>Y(s=>({...s,currentPassword:e.target.value})),placeholder:"Enter current password",className:"w-full mt-2 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent"})]})]}),(0,a.jsxs)("div",{className:"flex gap-3 mt-6",children:[(0,a.jsx)(h.$,{variant:"outline",onClick:()=>J(!1),className:"flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50",children:"Cancel"}),(0,a.jsx)(h.$,{onClick:ea,disabled:O,className:"flex-1 bg-orange-600 hover:bg-orange-700 text-white",children:O?"Changing...":"Change Email"})]})]})}),I&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-gray-900 border border-gray-800 rounded-xl shadow-2xl max-w-md w-full p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(o.A,{className:"h-6 w-6 text-blue-400"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Reset Password"})]}),(0,a.jsx)("p",{className:"text-gray-300 mb-4",children:"We'll send a password reset link to your email address."}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)(h.$,{variant:"outline",onClick:()=>K(!1),className:"flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50",children:"Cancel"}),(0,a.jsx)(h.$,{onClick:er,disabled:L,className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white",children:L?"Sending...":"Send Reset Link"})]})]})}),T&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-gray-900 border border-red-800/50 rounded-xl shadow-2xl max-w-md w-full p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(u.A,{className:"h-6 w-6 text-red-400"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Delete Account"})]}),(0,a.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-4",children:(0,a.jsxs)("p",{className:"text-red-300 text-sm",children:["⚠️ ",(0,a.jsx)("strong",{children:"This action cannot be undone."})," This will permanently delete your account, all configurations, API keys, and associated data."]})}),(0,a.jsx)("p",{className:"text-gray-300 mb-6",children:'Are you sure you want to delete your account? Type "DELETE" to confirm.'}),(0,a.jsx)("input",{type:"text",placeholder:"Type DELETE to confirm",className:"w-full px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-red-500 focus:border-transparent mb-4",onChange:e=>{}}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)(h.$,{variant:"outline",onClick:()=>$(!1),className:"flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50",children:"Cancel"}),(0,a.jsx)(h.$,{onClick:et,disabled:P,className:"flex-1 bg-red-600 hover:bg-red-700 text-white",children:P?"Deleting...":"Delete Account"})]})]})})]})}},21884:(e,s,r)=>{"use strict";r.d(s,{C1:()=>a.A,KS:()=>l.A,Pi:()=>t.A,fK:()=>n.A,qh:()=>i.A});var a=r(6865),t=r(55628),l=r(67695),i=r(52589),n=r(74500)}},e=>{var s=s=>e(e.s=s);e.O(0,[8888,1459,5738,9968,6060,4755,563,2662,8669,4703,3269,9173,9219,4288,7706,7544,1142,2993,1561,9248,6761,7358],()=>s(11337)),_N_E=e.O()}]);